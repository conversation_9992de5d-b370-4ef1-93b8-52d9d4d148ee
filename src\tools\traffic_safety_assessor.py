# -*- coding: utf-8 -*-
"""
交通安全评估工具 - Traffic Safety Assessor

这个模块实现了综合的交通场景安全风险评估功能，是整个系统的安全分析核心。

核心思路：
1. 综合分析场景分析和违规检测结果
2. 基于多维度指标计算安全风险等级
3. 识别主要风险因素和潜在危险点
4. 提供量化的安全评估报告

主要功能：
1. 风险等级评估 - 采用A-E五级评估体系
2. 风险因素分析 - 识别和量化各类风险因素
3. 安全趋势预测 - 基于历史数据预测安全趋势
4. 预警建议生成 - 提供针对性的安全预警

设计特点：
- 多维度评估：考虑环境、设施、行为等多个维度
- 量化分析：提供具体的风险数值和等级
- 动态权重：根据场景特点调整评估权重
- 预警机制：及时识别高风险情况

学习要点：
- 多因素风险评估模型的设计
- 安全等级划分和量化方法
- 权重系统的动态调整机制
- 风险预测和预警算法实现
"""

import math
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta

from src.utils.logger import get_logger
from config.settings import SAFETY_ASSESSMENT_CONFIG

# 获取专用日志记录器
logger = get_logger(__name__)

def safe_str_lower(value):
    """
    安全地将值转换为小写字符串
    处理dict、list、None等类型，避免.lower()调用错误
    """
    if isinstance(value, str):
        return value.lower()
    elif isinstance(value, dict):
        # 如果是dict，尝试获取常见的描述性字段
        for key in ['condition', 'status', 'description', 'state']:
            if key in value and isinstance(value[key], str):
                return value[key].lower()
        # 如果没有找到，返回空字符串
        return ""
    elif isinstance(value, list):
        # 如果是list，join成字符串
        return " ".join(str(item).lower() if isinstance(item, str) else str(item) for item in value)
    elif value is None:
        return ""
    else:
        return str(value).lower()

class SafetyLevel(Enum):
    """
    安全等级枚举
    
    采用A-E五级评估体系，类似于国际通用的安全评估标准：
    - A级：非常安全，风险极低
    - B级：比较安全，风险较低  
    - C级：一般安全，风险中等
    - D级：不够安全，风险较高
    - E级：危险状态，风险极高
    """
    A = "A"  # 优秀 (90-100分)
    B = "B"  # 良好 (80-89分)
    C = "C"  # 一般 (70-79分)
    D = "D"  # 较差 (60-69分)
    E = "E"  # 危险 (0-59分)

class RiskFactor(Enum):
    """
    风险因素类型枚举
    
    定义了交通安全评估中的主要风险因素类型，
    用于系统化地分析和量化各种安全风险。
    """
    ENVIRONMENTAL = "environmental"      # 环境因素：天气、光线、路面状况等
    INFRASTRUCTURE = "infrastructure"    # 基础设施：道路设计、信号设施等  
    BEHAVIORAL = "behavioral"           # 行为因素：违规行为、驾驶习惯等
    TRAFFIC_FLOW = "traffic_flow"       # 交通流：密度、速度、冲突点等
    EQUIPMENT = "equipment"             # 设备因素：车辆状态、安全设施等

@dataclass
class RiskAssessment:
    """
    风险评估结果数据类
    
    封装了完整的风险评估信息，包括：
    - 总体安全等级和分数
    - 各维度的详细评估
    - 风险因素识别和量化
    - 改进建议和预警信息
    
    设计目的：
    - 统一风险评估数据格式
    - 便于结果传递和处理
    - 支持详细的风险分析
    - 提供可操作的评估信息
    """
    overall_score: float                    # 总体安全分数 (0-100)
    safety_level: SafetyLevel              # 安全等级 (A-E)
    risk_factors: Dict[RiskFactor, Dict]   # 各类风险因素详情
    dimensional_scores: Dict[str, float]   # 各维度评分
    major_risks: List[str]                 # 主要风险点
    improvement_urgency: str               # 改进紧急程度
    prediction_trend: str                  # 安全趋势预测
    confidence_level: float                # 评估置信度

class TrafficSafetyAssessor:
    """
    交通安全评估器
    
    这个类实现了全面的交通安全风险评估功能。
    
    核心方法：
    - assess(): 主要评估接口
    - _calculate_overall_score(): 计算总体安全分数
    - _analyze_risk_factors(): 分析具体风险因素
    - _predict_safety_trend(): 预测安全趋势
    - _generate_risk_assessment(): 生成评估报告
    
    设计原则：
    - 科学性：基于交通安全理论和实践经验
    - 全面性：考虑影响交通安全的各个方面
    - 实用性：提供可操作的安全评估结果
    - 准确性：采用多重验证确保评估质量
    """
    
    def __init__(self):
        """
        初始化交通安全评估器
        
        初始化步骤：
        1. 设置日志记录器
        2. 加载评估配置参数
        3. 初始化评估模型和权重
        4. 建立历史数据记录
        """
        # 设置专用日志记录器
        self.logger = get_logger(self.__class__.__name__)
        
        # 评估统计信息
        self.assessment_count = 0
        self.high_risk_detections = 0
        
        # 评估配置参数
        self.score_weights = SAFETY_ASSESSMENT_CONFIG.get("score_weights", {
            "environmental": 0.25,      # 环境因素权重
            "infrastructure": 0.20,     # 基础设施权重  
            "behavioral": 0.30,         # 行为因素权重
            "traffic_flow": 0.15,       # 交通流权重
            "equipment": 0.10           # 设备因素权重
        })
        
        # 风险阈值配置
        self.risk_thresholds = SAFETY_ASSESSMENT_CONFIG.get("risk_thresholds", {
            "high_risk": 60,           # 高风险阈值
            "medium_risk": 70,         # 中等风险阈值
            "low_risk": 80            # 低风险阈值
        })
        
        # 历史评估数据（用于趋势分析）
        self.historical_assessments = []
        
        # 初始化完成日志
        self.logger.info("🛡️ 交通安全评估器初始化完成")
    
    async def assess(self, 
                    scene_analysis: Dict[str, Any],
                    violation_results: Dict[str, Any],
                    assessment_options: Optional[Dict] = None) -> Dict[str, Any]:
        """
        执行交通安全评估的主要接口
        
        这是安全评估的核心方法，负责：
        1. 整合各种输入数据
        2. 执行多维度安全评估  
        3. 计算总体安全分数和等级
        4. 识别主要风险因素
        5. 生成完整的评估报告
        
        Args:
            scene_analysis: 交通场景分析结果
            violation_results: 违规检测结果
            assessment_options: 评估选项配置
            
        Returns:
            Dict[str, Any]: 安全评估结果，包含：
            {
                "success": bool,                    # 评估是否成功
                "safety_assessment": Dict,          # 详细的安全评估结果
                "risk_level": str,                  # 风险等级（A-E）
                "overall_score": float,             # 总体安全分数
                "major_risks": List[str],           # 主要风险点
                "recommendations": List[str],       # 安全建议
                "assessment_metadata": Dict         # 评估元数据
            }
            
        学习要点：
        - 多源数据的整合和分析
        - 复杂评估逻辑的组织和实现
        - 评估结果的标准化输出
        - 异常情况的处理和恢复
        """
        # 记录评估开始时间
        import time
        start_time = time.time()
        
        try:
            # 更新评估统计
            self.assessment_count += 1
            
            self.logger.info(f"🔍 开始交通安全评估，第 {self.assessment_count} 次评估")
            
            # 步骤1：验证输入数据
            if not self._validate_assessment_inputs(scene_analysis, violation_results):
                return {
                    "success": False,
                    "error": "输入数据不完整或格式错误",
                    "safety_assessment": {},
                    "risk_level": "E"
                }
            
            # 步骤2：计算各维度安全分数
            dimensional_scores = await self._calculate_dimensional_scores(
                scene_analysis, violation_results
            )
            
            # 步骤3：计算总体安全分数
            overall_score = self._calculate_overall_score(dimensional_scores)
            
            # 步骤4：确定安全等级
            safety_level = self._determine_safety_level(overall_score)
            
            # 步骤5：分析风险因素
            risk_factors = await self._analyze_risk_factors(
                scene_analysis, violation_results, dimensional_scores
            )
            
            # 步骤6：识别主要风险点
            major_risks = self._identify_major_risks(risk_factors, violation_results)
            
            # 步骤7：预测安全趋势
            safety_trend = self._predict_safety_trend(overall_score)
            
            # 步骤8：生成改进建议
            recommendations = self._generate_safety_recommendations(
                safety_level, risk_factors, major_risks
            )
            
            # 步骤9：构建完整的评估结果
            assessment_result = RiskAssessment(
                overall_score=overall_score,
                safety_level=safety_level,
                risk_factors=risk_factors,
                dimensional_scores=dimensional_scores,
                major_risks=major_risks,
                improvement_urgency=self._assess_improvement_urgency(safety_level),
                prediction_trend=safety_trend,
                confidence_level=self._calculate_confidence_level(dimensional_scores)
            )
            
            # 步骤10：记录历史数据
            self._record_historical_assessment(assessment_result)
            
            # 步骤11：更新统计信息
            if safety_level in [SafetyLevel.D, SafetyLevel.E]:
                self.high_risk_detections += 1
            
            # 构建返回结果
            processing_time = time.time() - start_time
            result = {
                "success": True,
                "safety_assessment": {
                    "overall_score": overall_score,
                    "safety_level": safety_level.value,
                    "level_description": self._get_level_description(safety_level),
                    "dimensional_scores": dimensional_scores,
                    "risk_factors": self._format_risk_factors(risk_factors),
                    "improvement_urgency": assessment_result.improvement_urgency,
                    "prediction_trend": safety_trend,
                    "confidence_level": assessment_result.confidence_level
                },
                "risk_level": safety_level.value,
                "overall_score": overall_score,
                "major_risks": major_risks,
                "recommendations": recommendations,
                "assessment_metadata": {
                    "processing_time": round(processing_time, 3),
                    "assessment_timestamp": datetime.now().isoformat(),
                    "total_assessments": self.assessment_count,
                    "high_risk_rate": round(self.high_risk_detections / self.assessment_count, 2),
                    "assessment_version": "1.0"
                }
            }
            
            # 记录评估完成日志
            self.logger.info(f"✅ 安全评估完成，等级：{safety_level.value}，分数：{overall_score:.1f}，耗时：{processing_time:.3f}秒")
            
            return result
            
        except Exception as e:
            # 异常处理：确保评估失败不会影响整个系统
            error_msg = f"安全评估过程中发生错误: {str(e)}"
            self.logger.error(f"❌ {error_msg}")
            
            return {
                "success": False,
                "error": error_msg,
                "safety_assessment": {},
                "risk_level": "E",
                "processing_time": time.time() - start_time
            }
    
    def _validate_assessment_inputs(self, 
                                  scene_analysis: Dict[str, Any],
                                  violation_results: Dict[str, Any]) -> bool:
        """
        验证评估输入数据的有效性
        
        Args:
            scene_analysis: 场景分析数据
            violation_results: 违规检测结果
            
        Returns:
            bool: 数据是否有效
            
        学习要点：
        - 数据完整性检查的重要性
        - 多层次数据验证策略
        - 错误信息的详细记录
        - 防御性编程的实践
        """
        try:
            # 检查场景分析数据
            if not isinstance(scene_analysis, dict):
                self.logger.error("❌ 场景分析数据不是字典格式")
                return False
            
            required_scene_fields = ["traffic_participants", "road_environment"]
            for field in required_scene_fields:
                if field not in scene_analysis:
                    self.logger.error(f"❌ 场景分析缺少必需字段: {field}")
                    return False
            
            # 检查违规检测数据
            if not isinstance(violation_results, dict):
                self.logger.error("❌ 违规检测数据不是字典格式")
                return False
            
            if "violations_detected" not in violation_results:
                self.logger.error("❌ 违规检测缺少violations_detected字段")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 输入验证失败: {e}")
            return False
    
    async def _calculate_dimensional_scores(self, 
                                          scene_analysis: Dict[str, Any],
                                          violation_results: Dict[str, Any]) -> Dict[str, float]:
        """
        计算各维度的安全分数
        
        这是安全评估的核心计算逻辑，分别评估：
        1. 环境安全：天气、光线、路面等环境因素
        2. 基础设施安全：道路设计、标识设施等
        3. 行为安全：违规行为、驾驶习惯等
        4. 交通流安全：交通密度、流量配置等
        5. 设备安全：车辆状态、安全设施等
        
        Args:
            scene_analysis: 场景分析数据
            violation_results: 违规检测结果
            
        Returns:
            Dict[str, float]: 各维度安全分数 (0-100)
            
        学习要点：
        - 多维度评估模型的设计
        - 评分算法的数学原理
        - 权重分配的考虑因素
        - 评估标准的量化方法
        """
        try:
            dimensional_scores = {}
            
            # 1. 环境安全评估
            environmental_score = await self._assess_environmental_safety(scene_analysis)
            dimensional_scores["environmental"] = environmental_score
            
            # 2. 基础设施安全评估
            infrastructure_score = await self._assess_infrastructure_safety(scene_analysis)
            dimensional_scores["infrastructure"] = infrastructure_score
            
            # 3. 行为安全评估
            behavioral_score = await self._assess_behavioral_safety(
                scene_analysis, violation_results
            )
            dimensional_scores["behavioral"] = behavioral_score
            
            # 4. 交通流安全评估
            traffic_flow_score = await self._assess_traffic_flow_safety(scene_analysis)
            dimensional_scores["traffic_flow"] = traffic_flow_score
            
            # 5. 设备安全评估
            equipment_score = await self._assess_equipment_safety(scene_analysis)
            dimensional_scores["equipment"] = equipment_score
            
            # 记录各维度评分
            self.logger.debug(f"📊 各维度安全评分: {dimensional_scores}")
            
            return dimensional_scores
            
        except Exception as e:
            self.logger.error(f"❌ 维度评分计算失败: {e}")
            # 返回默认的中等分数
            return {
                "environmental": 70.0,
                "infrastructure": 70.0,
                "behavioral": 70.0,
                "traffic_flow": 70.0,
                "equipment": 70.0
            }
    
    async def _assess_environmental_safety(self, scene_analysis: Dict[str, Any]) -> float:
        """
        评估环境安全因素
        
        分析影响交通安全的环境条件，包括：
        - 天气条件（晴雨雪雾等）
        - 光照条件（白天夜晚黄昏等）
        - 路面状况（干燥湿滑等）
        - 能见度情况
        
        Args:
            scene_analysis: 场景分析数据
            
        Returns:
            float: 环境安全分数 (0-100)
        """
        try:
            base_score = 85.0  # 基础环境分数
            
            environment = scene_analysis.get("environment", {})
            
            # 天气影响评估
            weather = safe_str_lower(environment.get("weather", ""))
            weather_penalties = {
                "雨": -10,    # 雨天路滑，制动距离增加
                "雪": -15,    # 雪天更危险
                "雾": -20,    # 雾天能见度差，事故率高
                "冰雹": -25   # 极端天气
            }
            
            for weather_type, penalty in weather_penalties.items():
                if weather_type in weather:
                    base_score += penalty
                    self.logger.debug(f"🌧️ 天气影响: {weather_type}, 扣分: {-penalty}")
            
            # 光照影响评估
            lighting = safe_str_lower(environment.get("lighting", ""))
            lighting_penalties = {
                "夜晚": -8,    # 夜间视线差
                "黄昏": -5,    # 黄昏时段事故高发
                "阴暗": -6     # 光线不足
            }
            
            for lighting_type, penalty in lighting_penalties.items():
                if lighting_type in lighting:
                    base_score += penalty
                    self.logger.debug(f"💡 光照影响: {lighting_type}, 扣分: {-penalty}")
            
            # 能见度影响评估
            visibility = safe_str_lower(environment.get("visibility", ""))
            if "低" in visibility or "差" in visibility:
                base_score -= 12
                self.logger.debug("👁️ 能见度差，扣分: 12")
            
            # 确保分数在合理范围内
            environmental_score = max(0.0, min(100.0, base_score))
            
            return environmental_score
            
        except Exception as e:
            self.logger.error(f"❌ 环境安全评估失败: {e}")
            return 70.0  # 返回中等分数
    
    async def _assess_infrastructure_safety(self, scene_analysis: Dict[str, Any]) -> float:
        """
        评估基础设施安全因素
        
        分析道路基础设施对安全的影响，包括：
        - 道路类型和设计
        - 车道配置和标识
        - 交通信号设施
        - 安全防护设施
        
        Args:
            scene_analysis: 场景分析数据
            
        Returns:
            float: 基础设施安全分数 (0-100)
        """
        try:
            base_score = 80.0  # 基础设施基础分数
            
            road_env = scene_analysis.get("road_environment", {})
            facilities = scene_analysis.get("traffic_facilities", {})
            
            # 道路类型安全评估
            road_type = safe_str_lower(road_env.get("road_type", ""))
            road_type_scores = {
                "高速公路": 5,     # 设计标准高，相对安全
                "城市道路": 0,     # 标准配置
                "乡村道路": -10,   # 设施相对简陋
                "施工路段": -15    # 临时设施，风险较高
            }
            
            for r_type, score_adjust in road_type_scores.items():
                if r_type in road_type:
                    base_score += score_adjust
                    self.logger.debug(f"🛣️ 道路类型: {r_type}, 调整: {score_adjust}")
                    break
            
            # 交通设施完整性评估
            signals = safe_str_lower(facilities.get("signals", ""))
            if "正常" in signals or "清晰" in signals:
                base_score += 5
            elif "故障" in signals or "不清晰" in signals:
                base_score -= 15
                self.logger.debug("🚦 信号设施问题，扣分: 15")
            
            # 标志标线评估
            signs = safe_str_lower(facilities.get("signs", ""))
            if "完整" in signs or "清晰" in signs:
                base_score += 3
            elif "缺失" in signs or "模糊" in signs:
                base_score -= 10
                self.logger.debug("🚧 标识缺失或模糊，扣分: 10")
            
            # 护栏隔离设施评估
            barriers = safe_str_lower(facilities.get("barriers", ""))
            if "完好" in barriers:
                base_score += 2
            elif "损坏" in barriers or "缺失" in barriers:
                base_score -= 8
                self.logger.debug("🛡️ 护栏设施问题，扣分: 8")
            
            # 车道配置评估
            lane_config = safe_str_lower(road_env.get("lane_config", ""))
            if "标准" in lane_config or "清晰" in lane_config:
                base_score += 3
            elif "混乱" in lane_config or "不清" in lane_config:
                base_score -= 12
                self.logger.debug("🛤️ 车道配置问题，扣分: 12")
            
            # 确保分数在合理范围内
            infrastructure_score = max(0.0, min(100.0, base_score))
            
            return infrastructure_score
            
        except Exception as e:
            self.logger.error(f"❌ 基础设施安全评估失败: {e}")
            return 75.0  # 返回中等偏上分数
    
    async def _assess_behavioral_safety(self, 
                                      scene_analysis: Dict[str, Any],
                                      violation_results: Dict[str, Any]) -> float:
        """
        评估行为安全因素
        
        这是最关键的评估维度，分析交通参与者的行为安全，包括：
        - 违规行为的类型和严重程度
        - 驾驶行为的规范性
        - 行人和非机动车行为
        - 整体交通秩序
        
        Args:
            scene_analysis: 场景分析数据
            violation_results: 违规检测结果
            
        Returns:
            float: 行为安全分数 (0-100)
        """
        try:
            base_score = 90.0  # 行为安全基础分数（假设大部分人遵守规则）
            
            # 违规行为影响评估
            violations = violation_results.get("violations_detected", [])
            
            # 根据违规严重程度扣分
            severity_penalties = {
                "critical": -25,   # 极严重违规，如危险驾驶
                "high": -15,       # 严重违规，如闯红灯
                "medium": -8,      # 中等违规，如车道违规
                "low": -3          # 轻微违规，如轻微超速
            }
            
            total_violations = len(violations)
            for violation in violations:
                severity = violation.get("severity", "medium")
                penalty = severity_penalties.get(severity, -5)
                base_score += penalty
                self.logger.debug(f"⚠️ 违规行为: {violation.get('name', '未知')}, 扣分: {-penalty}")
            
            # 违规数量的额外影响
            if total_violations > 3:
                # 多个违规行为表明整体秩序较差
                additional_penalty = (total_violations - 3) * -2
                base_score += additional_penalty
                self.logger.debug(f"📊 多违规附加扣分: {-additional_penalty}")
            
            # 分析交通参与者行为
            participants = scene_analysis.get("traffic_participants", {})
            
            # 机动车行为评估
            motor_vehicles = participants.get("motor_vehicles", {})
            motor_behaviors = motor_vehicles.get("behaviors", [])
            
            for behavior in motor_behaviors:
                behavior_lower = safe_str_lower(behavior)
                if any(danger_word in behavior_lower for danger_word in 
                       ["急刹", "急转", "超速", "占道", "逆行"]):
                    base_score -= 6
                    self.logger.debug(f"🚗 危险驾驶行为: {behavior}, 扣分: 6")
                elif any(good_word in behavior_lower for good_word in 
                        ["正常", "规范", "礼让", "减速"]):
                    base_score += 2
                    self.logger.debug(f"🚗 良好驾驶行为: {behavior}, 加分: 2")
            
            # 非机动车行为评估
            non_motor = participants.get("non_motor_vehicles", {})
            non_motor_behaviors = non_motor.get("behaviors", [])
            
            for behavior in non_motor_behaviors:
                behavior_lower = safe_str_lower(behavior)
                if any(violation_word in behavior_lower for violation_word in 
                       ["占道", "逆行", "闯灯", "违规"]):
                    base_score -= 4
                    self.logger.debug(f"🚴 非机动车违规: {behavior}, 扣分: 4")
            
            # 行人行为评估
            pedestrians = participants.get("pedestrians", {})
            pedestrian_behaviors = pedestrians.get("behaviors", [])
            
            for behavior in pedestrian_behaviors:
                behavior_lower = safe_str_lower(behavior)
                if any(danger_word in behavior_lower for danger_word in 
                       ["乱穿", "闯灯", "不走人行道"]):
                    base_score -= 5
                    self.logger.debug(f"🚶 行人违规行为: {behavior}, 扣分: 5")
            
            # 确保分数在合理范围内
            behavioral_score = max(0.0, min(100.0, base_score))
            
            return behavioral_score
            
        except Exception as e:
            self.logger.error(f"❌ 行为安全评估失败: {e}")
            return 65.0  # 返回偏低分数，体现行为风险的重要性
    
    async def _assess_traffic_flow_safety(self, scene_analysis: Dict[str, Any]) -> float:
        """
        评估交通流安全因素
        
        分析交通流特征对安全的影响，包括：
        - 交通密度和拥堵程度
        - 车速分布和一致性
        - 交通组织和秩序
        - 冲突点和瓶颈识别
        
        Args:
            scene_analysis: 场景分析数据
            
        Returns:
            float: 交通流安全分数 (0-100)
        """
        try:
            base_score = 80.0  # 交通流基础分数
            
            participants = scene_analysis.get("traffic_participants", {})
            
            # 计算总体交通密度
            total_vehicles = 0
            vehicle_types = ["motor_vehicles", "non_motor_vehicles"]
            
            for vehicle_type in vehicle_types:
                if vehicle_type in participants:
                    count = participants[vehicle_type].get("count", 0)
                    total_vehicles += count
            
            pedestrian_count = participants.get("pedestrians", {}).get("count", 0)
            
            # 交通密度影响评估
            if total_vehicles == 0:
                # 无车辆，可能是空旷路段
                base_score += 5
                self.logger.debug("🛣️ 交通密度低，加分: 5")
            elif total_vehicles <= 3:
                # 低密度，相对安全
                base_score += 3
            elif total_vehicles <= 8:
                # 中等密度，标准情况
                pass  # 不调整分数
            elif total_vehicles <= 15:
                # 高密度，需要注意
                base_score -= 5
                self.logger.debug("🚗 交通密度高，扣分: 5")
            else:
                # 极高密度，拥堵状态
                base_score -= 12
                self.logger.debug("🚦 交通拥堵，扣分: 12")
            
            # 混合交通影响评估
            has_motor = participants.get("motor_vehicles", {}).get("count", 0) > 0
            has_non_motor = participants.get("non_motor_vehicles", {}).get("count", 0) > 0
            has_pedestrians = pedestrian_count > 0
            
            # 混合交通类型数量
            traffic_mix_count = sum([has_motor, has_non_motor, has_pedestrians])
            
            if traffic_mix_count >= 3:
                # 机非人混行，冲突点多
                base_score -= 8
                self.logger.debug("🚲🚗🚶 机非人混行，扣分: 8")
            elif traffic_mix_count == 2:
                # 两种交通方式混合
                base_score -= 3
                self.logger.debug("🚴🚗 混合交通，扣分: 3")
            
            # 行人密度特别评估
            if pedestrian_count > 5:
                # 行人较多，需要特别注意
                base_score -= 6
                self.logger.debug("🚶‍♂️🚶‍♀️ 行人密度高，扣分: 6")
            
            # 交通组织评估（基于关键观察）
            key_observations = scene_analysis.get("key_observations", [])
            for observation in key_observations:
                observation_lower = safe_str_lower(observation)
                if any(disorder_word in observation_lower for disorder_word in 
                       ["混乱", "无序", "拥堵", "冲突"]):
                    base_score -= 7
                    self.logger.debug(f"📊 交通秩序问题: {observation}, 扣分: 7")
                elif any(order_word in observation_lower for order_word in 
                        ["有序", "畅通", "规范"]):
                    base_score += 3
                    self.logger.debug(f"📊 交通秩序良好: {observation}, 加分: 3")
            
            # 确保分数在合理范围内
            traffic_flow_score = max(0.0, min(100.0, base_score))
            
            return traffic_flow_score
            
        except Exception as e:
            self.logger.error(f"❌ 交通流安全评估失败: {e}")
            return 75.0  # 返回中等分数
    
    async def _assess_equipment_safety(self, scene_analysis: Dict[str, Any]) -> float:
        """
        评估设备安全因素
        
        分析车辆设备和安全设施状况，包括：
        - 车辆技术状况
        - 安全防护设备
        - 辅助设施完好性
        - 应急设备可用性
        
        Args:
            scene_analysis: 场景分析数据
            
        Returns:
            float: 设备安全分数 (0-100)
        """
        try:
            base_score = 85.0  # 设备安全基础分数（假设大部分设备正常）
            
            facilities = scene_analysis.get("traffic_facilities", {})
            
            # 交通信号设备评估
            signals = safe_str_lower(facilities.get("signals", ""))
            if "故障" in signals or "损坏" in signals:
                base_score -= 20
                self.logger.debug("🚦 信号设备故障，扣分: 20")
            elif "闪烁" in signals or "不稳定" in signals:
                base_score -= 10
                self.logger.debug("🚦 信号设备不稳定，扣分: 10")
            
            # 监控设备评估
            other_facilities = facilities.get("other", "").lower()
            if "监控" in other_facilities:
                if "正常" in other_facilities:
                    base_score += 3
                    self.logger.debug("📹 监控设备正常，加分: 3")
                elif "故障" in other_facilities:
                    base_score -= 5
                    self.logger.debug("📹 监控设备故障，扣分: 5")
            
            # 照明设备评估（夜间场景）
            environment = scene_analysis.get("environment", {})
            lighting = safe_str_lower(environment.get("lighting", ""))
            
            if "夜晚" in lighting:
                # 夜间场景需要评估照明设备
                if "照明" in other_facilities:
                    if "充足" in other_facilities or "明亮" in other_facilities:
                        base_score += 5
                        self.logger.debug("💡 夜间照明充足，加分: 5")
                    elif "不足" in other_facilities or "昏暗" in other_facilities:
                        base_score -= 15
                        self.logger.debug("💡 夜间照明不足，扣分: 15")
                else:
                    # 夜间但没有照明信息，默认扣分
                    base_score -= 8
                    self.logger.debug("💡 夜间照明未知，扣分: 8")
            
            # 护栏和隔离带评估
            barriers = safe_str_lower(facilities.get("barriers", ""))
            if "损坏" in barriers or "缺失" in barriers:
                base_score -= 10
                self.logger.debug("🛡️ 护栏设施问题，扣分: 10")
            elif "完好" in barriers:
                base_score += 2
                self.logger.debug("🛡️ 护栏设施完好，加分: 2")
            
            # 车辆设备状况评估（基于观察）
            key_observations = scene_analysis.get("key_observations", [])
            for observation in key_observations:
                observation_lower = safe_str_lower(observation)
                if any(equipment_issue in observation_lower for equipment_issue in 
                       ["故障车", "抛锚", "设备故障", "灯不亮"]):
                    base_score -= 12
                    self.logger.debug(f"🔧 设备故障: {observation}, 扣分: 12")
            
            # 确保分数在合理范围内
            equipment_score = max(0.0, min(100.0, base_score))
            
            return equipment_score
            
        except Exception as e:
            self.logger.error(f"❌ 设备安全评估失败: {e}")
            return 80.0  # 返回中等偏上分数
    
    def _calculate_overall_score(self, dimensional_scores: Dict[str, float]) -> float:
        """
        计算总体安全分数
        
        基于各维度分数和权重计算加权总分。
        
        Args:
            dimensional_scores: 各维度安全分数
            
        Returns:
            float: 总体安全分数 (0-100)
        """
        try:
            total_score = 0.0
            total_weight = 0.0
            
            for dimension, score in dimensional_scores.items():
                weight = self.score_weights.get(dimension, 0.2)  # 默认权重20%
                total_score += score * weight
                total_weight += weight
            
            # 标准化到100分制
            if total_weight > 0:
                overall_score = total_score / total_weight
            else:
                overall_score = 70.0  # 默认中等分数
            
            # 确保分数在0-100范围内
            overall_score = max(0.0, min(100.0, overall_score))
            
            return round(overall_score, 1)
            
        except Exception as e:
            self.logger.error(f"❌ 总体分数计算失败: {e}")
            return 70.0
    
    def _determine_safety_level(self, overall_score: float) -> SafetyLevel:
        """
        根据总体分数确定安全等级
        
        Args:
            overall_score: 总体安全分数
            
        Returns:
            SafetyLevel: 安全等级
        """
        if overall_score >= 90:
            return SafetyLevel.A
        elif overall_score >= 80:
            return SafetyLevel.B
        elif overall_score >= 70:
            return SafetyLevel.C
        elif overall_score >= 60:
            return SafetyLevel.D
        else:
            return SafetyLevel.E
    
    async def _analyze_risk_factors(self, 
                                  scene_analysis: Dict[str, Any],
                                  violation_results: Dict[str, Any],
                                  dimensional_scores: Dict[str, float]) -> Dict[RiskFactor, Dict]:
        """
        分析具体的风险因素
        
        Args:
            scene_analysis: 场景分析数据
            violation_results: 违规检测结果
            dimensional_scores: 各维度分数
            
        Returns:
            Dict[RiskFactor, Dict]: 风险因素详细分析
        """
        risk_factors = {}
        
        try:
            # 环境风险因素
            env_score = dimensional_scores.get("environmental", 70)
            if env_score < 80:
                risk_factors[RiskFactor.ENVIRONMENTAL] = {
                    "score": env_score,
                    "risk_level": "高" if env_score < 60 else "中",
                    "main_issues": self._identify_environmental_issues(scene_analysis),
                    "impact_description": "恶劣环境条件显著增加事故风险"
                }
            
            # 基础设施风险因素
            infra_score = dimensional_scores.get("infrastructure", 75)
            if infra_score < 75:
                risk_factors[RiskFactor.INFRASTRUCTURE] = {
                    "score": infra_score,
                    "risk_level": "高" if infra_score < 60 else "中",
                    "main_issues": self._identify_infrastructure_issues(scene_analysis),
                    "impact_description": "基础设施缺陷影响交通安全"
                }
            
            # 行为风险因素
            behavior_score = dimensional_scores.get("behavioral", 70)
            if behavior_score < 85:
                risk_factors[RiskFactor.BEHAVIORAL] = {
                    "score": behavior_score,
                    "risk_level": "高" if behavior_score < 60 else "中",
                    "main_issues": self._identify_behavioral_issues(violation_results),
                    "impact_description": "违规行为是交通事故的主要原因"
                }
            
            # 交通流风险因素
            flow_score = dimensional_scores.get("traffic_flow", 75)
            if flow_score < 75:
                risk_factors[RiskFactor.TRAFFIC_FLOW] = {
                    "score": flow_score,
                    "risk_level": "高" if flow_score < 60 else "中",
                    "main_issues": self._identify_traffic_flow_issues(scene_analysis),
                    "impact_description": "交通流混乱增加冲突概率"
                }
            
            # 设备风险因素
            equipment_score = dimensional_scores.get("equipment", 80)
            if equipment_score < 80:
                risk_factors[RiskFactor.EQUIPMENT] = {
                    "score": equipment_score,
                    "risk_level": "高" if equipment_score < 60 else "中",
                    "main_issues": self._identify_equipment_issues(scene_analysis),
                    "impact_description": "设备故障影响交通管理效果"
                }
            
            return risk_factors
            
        except Exception as e:
            self.logger.error(f"❌ 风险因素分析失败: {e}")
            return {}
    
    def _identify_environmental_issues(self, scene_analysis: Dict[str, Any]) -> List[str]:
        """识别环境相关问题"""
        issues = []
        environment = scene_analysis.get("environment", {})
        
        weather = safe_str_lower(environment.get("weather", ""))
        if any(bad_weather in weather for bad_weather in ["雨", "雪", "雾"]):
            issues.append(f"恶劣天气条件: {weather}")
        
        lighting = environment.get("lighting", "").lower()
        if any(bad_light in lighting for bad_light in ["夜晚", "黄昏", "阴暗"]):
            issues.append(f"光照条件不佳: {lighting}")
        
        return issues
    
    def _identify_infrastructure_issues(self, scene_analysis: Dict[str, Any]) -> List[str]:
        """识别基础设施相关问题"""
        issues = []
        facilities = scene_analysis.get("traffic_facilities", {})
        
        for facility_type, condition in facilities.items():
            condition_lower = safe_str_lower(condition)
            if any(problem in condition_lower for problem in ["故障", "损坏", "缺失", "不清晰"]):
                issues.append(f"{facility_type}设施问题: {condition}")
        
        return issues
    
    def _identify_behavioral_issues(self, violation_results: Dict[str, Any]) -> List[str]:
        """识别行为相关问题"""
        issues = []
        violations = violation_results.get("violations_detected", [])
        
        for violation in violations:
            issues.append(f"{violation.get('name', '违规行为')}: {violation.get('description', '')}")
        
        return issues
    
    def _identify_traffic_flow_issues(self, scene_analysis: Dict[str, Any]) -> List[str]:
        """识别交通流相关问题"""
        issues = []
        key_observations = scene_analysis.get("key_observations", [])
        
        for observation in key_observations:
            if any(flow_issue in safe_str_lower(observation) for flow_issue in 
                   ["拥堵", "混乱", "冲突", "无序"]):
                issues.append(observation)
        
        return issues
    
    def _identify_equipment_issues(self, scene_analysis: Dict[str, Any]) -> List[str]:
        """识别设备相关问题"""
        issues = []
        facilities = scene_analysis.get("traffic_facilities", {})
        
        for facility_type, condition in facilities.items():
            if "故障" in safe_str_lower(condition):
                issues.append(f"{facility_type}设备故障")
        
        return issues
    
    def _identify_major_risks(self, 
                            risk_factors: Dict[RiskFactor, Dict],
                            violation_results: Dict[str, Any]) -> List[str]:
        """
        识别主要风险点
        
        Args:
            risk_factors: 风险因素分析结果
            violation_results: 违规检测结果
            
        Returns:
            List[str]: 主要风险点列表
        """
        major_risks = []
        
        try:
            # 基于风险因素识别
            for risk_type, risk_info in risk_factors.items():
                if risk_info.get("risk_level") == "高":
                    major_risks.extend(risk_info.get("main_issues", []))
            
            # 基于严重违规识别
            violations = violation_results.get("violations_detected", [])
            for violation in violations:
                if violation.get("severity") in ["critical", "high"]:
                    major_risks.append(f"严重违规: {violation.get('name', '未知')}")
            
            # 去重并限制数量
            major_risks = list(set(major_risks))[:5]
            
            return major_risks
            
        except Exception as e:
            self.logger.error(f"❌ 主要风险识别失败: {e}")
            return ["风险识别过程中出现错误"]
    
    def _predict_safety_trend(self, current_score: float) -> str:
        """
        预测安全趋势
        
        Args:
            current_score: 当前安全分数
            
        Returns:
            str: 安全趋势预测
        """
        try:
            if len(self.historical_assessments) < 3:
                return "数据不足，无法预测趋势"
            
            # 计算最近几次评估的分数趋势
            recent_scores = [assessment.overall_score for assessment in self.historical_assessments[-3:]]
            recent_scores.append(current_score)
            
            # 简单的趋势分析
            if len(recent_scores) >= 2:
                trend = recent_scores[-1] - recent_scores[-2]
                if trend > 5:
                    return "安全状况呈改善趋势"
                elif trend < -5:
                    return "安全状况呈恶化趋势"
                else:
                    return "安全状况基本稳定"
            
            return "安全趋势平稳"
            
        except Exception as e:
            self.logger.error(f"❌ 安全趋势预测失败: {e}")
            return "趋势预测不可用"
    
    def _generate_safety_recommendations(self, 
                                       safety_level: SafetyLevel,
                                       risk_factors: Dict[RiskFactor, Dict],
                                       major_risks: List[str]) -> List[str]:
        """
        生成安全建议
        
        Args:
            safety_level: 安全等级
            risk_factors: 风险因素
            major_risks: 主要风险点
            
        Returns:
            List[str]: 安全建议列表
        """
        recommendations = []
        
        try:
            # 基于安全等级的通用建议
            if safety_level == SafetyLevel.E:
                recommendations.extend([
                    "立即采取紧急管控措施",
                    "暂时限制该区域交通流量",
                    "增派执法人员现场管理"
                ])
            elif safety_level == SafetyLevel.D:
                recommendations.extend([
                    "加强现场交通管理",
                    "提高执法巡查频次",
                    "完善安全警示设施"
                ])
            elif safety_level == SafetyLevel.C:
                recommendations.extend([
                    "优化交通组织方案",
                    "加强安全宣传教育",
                    "定期检查维护设施"
                ])
            
            # 基于具体风险因素的针对性建议
            for risk_type, risk_info in risk_factors.items():
                if risk_type == RiskFactor.ENVIRONMENTAL and risk_info.get("risk_level") == "高":
                    recommendations.append("加强恶劣天气交通管理")
                elif risk_type == RiskFactor.INFRASTRUCTURE and risk_info.get("risk_level") == "高":
                    recommendations.append("及时维修和完善交通设施")
                elif risk_type == RiskFactor.BEHAVIORAL and risk_info.get("risk_level") == "高":
                    recommendations.append("加大违规行为执法力度")
            
            # 去重并限制数量
            recommendations = list(set(recommendations))[:6]
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"❌ 安全建议生成失败: {e}")
            return ["建议加强交通安全管理"]
    
    def _assess_improvement_urgency(self, safety_level: SafetyLevel) -> str:
        """评估改进紧急程度"""
        urgency_mapping = {
            SafetyLevel.A: "无需紧急改进",
            SafetyLevel.B: "建议适时改进",
            SafetyLevel.C: "需要计划改进",
            SafetyLevel.D: "需要尽快改进",
            SafetyLevel.E: "需要立即改进"
        }
        return urgency_mapping.get(safety_level, "需要评估")
    
    def _calculate_confidence_level(self, dimensional_scores: Dict[str, float]) -> float:
        """计算评估置信度"""
        try:
            # 基于各维度分数的方差计算置信度
            scores = list(dimensional_scores.values())
            if not scores:
                return 0.5
            
            mean_score = sum(scores) / len(scores)
            variance = sum((score - mean_score) ** 2 for score in scores) / len(scores)
            
            # 方差越小，置信度越高
            confidence = max(0.5, min(1.0, 1.0 - variance / 1000))
            
            return round(confidence, 2)
            
        except Exception as e:
            self.logger.error(f"❌ 置信度计算失败: {e}")
            return 0.7
    
    def _record_historical_assessment(self, assessment: RiskAssessment):
        """记录历史评估数据"""
        try:
            self.historical_assessments.append(assessment)
            
            # 只保留最近20次评估记录
            if len(self.historical_assessments) > 20:
                self.historical_assessments = self.historical_assessments[-20:]
                
        except Exception as e:
            self.logger.error(f"❌ 历史数据记录失败: {e}")
    
    def _get_level_description(self, safety_level: SafetyLevel) -> str:
        """获取安全等级描述"""
        descriptions = {
            SafetyLevel.A: "安全状况优秀，交通秩序良好，风险很低",
            SafetyLevel.B: "安全状况良好，存在少量小问题，风险较低",
            SafetyLevel.C: "安全状况一般，存在一些问题需要关注，风险中等",
            SafetyLevel.D: "安全状况较差，存在多个问题需要改进，风险较高",
            SafetyLevel.E: "安全状况危险，存在严重问题需要立即处理，风险很高"
        }
        return descriptions.get(safety_level, "安全状况需要评估")
    
    def _format_risk_factors(self, risk_factors: Dict[RiskFactor, Dict]) -> Dict[str, Any]:
        """格式化风险因素输出"""
        formatted = {}
        
        for risk_type, risk_info in risk_factors.items():
            formatted[risk_type.value] = {
                "score": risk_info["score"],
                "risk_level": risk_info["risk_level"],
                "main_issues": risk_info["main_issues"],
                "impact_description": risk_info["impact_description"]
            }
        
        return formatted
    
    def get_assessment_statistics(self) -> Dict[str, Any]:
        """
        获取评估统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "total_assessments": self.assessment_count,
            "high_risk_detections": self.high_risk_detections,
            "high_risk_rate": round(self.high_risk_detections / max(self.assessment_count, 1), 2),
            "score_weights": self.score_weights,
            "risk_thresholds": self.risk_thresholds,
            "historical_records": len(self.historical_assessments)
        }