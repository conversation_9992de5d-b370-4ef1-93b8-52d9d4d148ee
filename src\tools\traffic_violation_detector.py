# -*- coding: utf-8 -*-
"""
交通违规检测工具 - Traffic Violation Detector

这个模块实现了基于图像分析结果的交通违规行为检测功能。

核心思路：
1. 接收图像分析结果作为输入
2. 基于预定义的违规模式进行匹配
3. 结合交通法规知识进行判断
4. 输出结构化的违规检测结果

主要功能：
1. 违规模式匹配 - 基于规则和关键词识别违规行为
2. 严重程度评估 - 根据违规类型和影响程度评级
3. 法规条文匹配 - 为每个违规行为匹配相应的法律依据
4. 上下文分析 - 考虑环境条件对违规判断的影响

设计特点：
- 模块化的违规检测规则，便于维护和扩展
- 支持多种违规类型的同时检测
- 提供详细的违规描述和法律依据
- 考虑环境因素对违规判断的影响

学习要点：
- 规则引擎的设计和实现
- 模式匹配算法的应用
- 领域知识的代码化表示
- 复杂业务逻辑的结构化处理
"""

import re
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from src.utils.logger import get_logger
from config.settings import VIOLATION_PATTERNS

# 获取专用日志记录器
logger = get_logger(__name__)

class ViolationSeverity(Enum):
    """
    违规严重程度枚举
    
    这个枚举定义了违规行为的严重程度分级，用于：
    - 统一的严重程度表示
    - 处罚建议的差异化
    - 风险评估的量化标准
    """
    LOW = "low"        # 轻微违规，主要以教育为主
    MEDIUM = "medium"  # 中等违规，需要处罚和教育结合
    HIGH = "high"      # 严重违规，需要严厉处罚
    CRITICAL = "critical"  # 极严重违规，可能涉及刑事责任

@dataclass
class ViolationPattern:
    """
    违规模式数据类
    
    这个数据类定义了违规检测规则的标准结构，包含：
    - 违规类型和描述
    - 检测关键词和模式
    - 严重程度和法律依据
    - 检测逻辑和条件
    
    使用dataclass的好处：
    - 自动生成构造函数和比较方法
    - 类型注解支持
    - 清晰的数据结构定义
    - 便于序列化和反序列化
    """
    violation_type: str          # 违规类型ID
    name: str                   # 违规名称
    description: str            # 详细描述
    keywords: List[str]         # 检测关键词
    severity: ViolationSeverity # 严重程度
    legal_reference: str        # 法律依据
    penalty_info: str          # 处罚信息
    detection_logic: str       # 检测逻辑描述

class TrafficViolationDetector:
    """
    交通违规检测器
    
    这个类实现了交通违规行为的智能检测功能。
    
    核心方法：
    - detect(): 主要检测接口
    - _load_violation_patterns(): 加载违规检测规则
    - _match_violation_pattern(): 执行模式匹配
    - _assess_severity(): 评估违规严重程度
    - _generate_violation_report(): 生成检测报告
    
    设计原则：
    - 规则驱动：基于可配置的检测规则
    - 可扩展性：支持新增违规类型
    - 准确性：结合多种信息源进行判断
    - 实用性：提供可操作的检测结果
    """
    
    def __init__(self):
        """
        初始化交通违规检测器
        
        初始化过程：
        1. 设置日志记录器
        2. 加载违规检测规则
        3. 初始化统计变量
        4. 配置检测参数
        """
        # 设置专用日志记录器
        self.logger = get_logger(self.__class__.__name__)
        
        # 加载违规检测模式
        # 这些模式定义了如何识别不同类型的违规行为
        self.violation_patterns = self._load_violation_patterns()
        
        # 检测统计信息
        self.detection_count = 0
        self.violations_found = 0
        
        # 检测参数配置
        self.confidence_threshold = 0.7  # 检测置信度阈值
        self.context_weight = 0.3        # 上下文因素权重
        
        # 初始化完成日志
        self.logger.info(f"⚠️ 交通违规检测器初始化完成，加载了 {len(self.violation_patterns)} 种违规模式")
    
    def _load_violation_patterns(self) -> List[ViolationPattern]:
        """
        加载违规检测模式
        
        这个方法从配置文件中加载预定义的违规检测规则，
        并将其转换为结构化的ViolationPattern对象。
        
        Returns:
            List[ViolationPattern]: 违规检测模式列表
            
        学习要点：
        - 配置驱动的规则加载
        - 数据类的实例化
        - 枚举类型的使用
        - 错误处理和日志记录
        """
        patterns = []
        
        try:
            # 从配置文件加载违规模式定义
            # VIOLATION_PATTERNS 在 config/settings.py 中定义
            for pattern_id, pattern_config in VIOLATION_PATTERNS.items():
                
                # 创建违规模式对象
                # 这里展示了如何将配置数据转换为数据类实例
                pattern = ViolationPattern(
                    violation_type=pattern_id,
                    name=pattern_config.get("description", "未知违规"),
                    description=pattern_config.get("description", ""),
                    keywords=pattern_config.get("keywords", []),
                    severity=ViolationSeverity(pattern_config.get("severity", "medium")),
                    legal_reference=pattern_config.get("legal_reference", ""),
                    penalty_info=pattern_config.get("penalty_info", "详见相关法规"),
                    detection_logic=pattern_config.get("detection_logic", "关键词匹配")
                )
                
                patterns.append(pattern)
                self.logger.debug(f"📋 加载违规模式: {pattern.name}")
            
            return patterns
            
        except Exception as e:
            # 加载失败的处理
            self.logger.error(f"❌ 违规模式加载失败: {e}")
            
            # 返回默认的基础模式，确保系统可以继续运行
            return self._get_default_patterns()
    
    def _get_default_patterns(self) -> List[ViolationPattern]:
        """
        获取默认的违规检测模式
        
        当配置加载失败时，这个方法提供基础的违规检测规则，
        确保系统的基本功能不受影响。
        
        Returns:
            List[ViolationPattern]: 默认违规模式列表
        """
        default_patterns = [
            ViolationPattern(
                violation_type="lane_violation",
                name="车道违规",
                description="非机动车占用机动车道行驶",
                keywords=["电动车", "自行车", "机动车道", "占用"],
                severity=ViolationSeverity.MEDIUM,
                legal_reference="《道路交通安全法》第57条",
                penalty_info="罚款5-50元",
                detection_logic="检测非机动车在机动车道行驶"
            ),
            ViolationPattern(
                violation_type="pedestrian_violation",
                name="行人违规",
                description="机动车未礼让行人",
                keywords=["人行横道", "行人", "机动车", "未让行"],
                severity=ViolationSeverity.HIGH,
                legal_reference="《道路交通安全法》第47条",
                penalty_info="罚款200元，记3分",
                detection_logic="检测机动车在人行横道未礼让行人"
            )
        ]
        
        self.logger.warning("⚠️ 使用默认违规检测模式")
        return default_patterns
    
    async def detect(self, 
                    scene_analysis: Dict[str, Any],
                    detection_focus: Optional[List[str]] = None,
                    confidence_threshold: Optional[float] = None) -> Dict[str, Any]:
        """
        执行交通违规检测的主要接口
        
        这是违规检测的核心方法，负责：
        1. 分析输入的场景数据
        2. 应用违规检测规则
        3. 评估检测结果的可信度
        4. 生成结构化的检测报告
        
        Args:
            scene_analysis: 图像分析结果，包含场景的详细信息
            detection_focus: 检测重点，指定关注的违规类型
            confidence_threshold: 检测置信度阈值，覆盖默认设置
            
        Returns:
            Dict[str, Any]: 违规检测结果，包含：
            {
                "success": bool,                    # 检测是否成功
                "violations_detected": List[Dict], # 检测到的违规列表
                "total_violations": int,           # 违规总数
                "risk_level": str,                 # 整体风险等级
                "detection_metadata": Dict,        # 检测元数据
                "recommendations": List[str]       # 处理建议
            }
            
        学习要点：
        - 复杂业务逻辑的分步处理
        - 异步方法的实现模式
        - 结果数据的结构化组织
        - 错误处理和容错机制
        """
        # 记录检测开始时间，用于性能统计
        import time
        start_time = time.time()
        
        try:
            # 更新统计信息
            self.detection_count += 1
            
            # 设置检测参数
            threshold = confidence_threshold or self.confidence_threshold
            
            self.logger.info(f"🔍 开始违规检测，重点：{detection_focus}，阈值：{threshold}")
            
            # 步骤1：预处理和验证输入数据
            if not self._validate_scene_analysis(scene_analysis):
                return {
                    "success": False,
                    "error": "无效的场景分析数据",
                    "violations_detected": [],
                    "total_violations": 0
                }
            
            # 步骤2：执行违规模式匹配
            detected_violations = []
            
            # 遍历所有违规模式进行检测
            for pattern in self.violation_patterns:
                # 如果指定了检测重点，只检测相关类型
                if detection_focus and pattern.violation_type not in detection_focus:
                    continue
                
                # 执行单个模式的匹配检测
                violation_result = await self._match_violation_pattern(
                    pattern, scene_analysis, threshold
                )
                
                # 如果检测到违规，添加到结果列表
                if violation_result and violation_result["confidence"] >= threshold:
                    detected_violations.append(violation_result)
                    self.logger.info(f"✅ 检测到违规: {pattern.name}")
            
            # 步骤3：对检测结果进行后处理和优化
            # 去重和合并相似的违规
            filtered_violations = self._filter_and_merge_violations(detected_violations)
            
            # 评估整体风险等级
            risk_level = self._assess_overall_risk(filtered_violations)
            
            # 生成处理建议
            recommendations = self._generate_recommendations(filtered_violations, scene_analysis)
            
            # 步骤4：构建完整的检测结果
            processing_time = time.time() - start_time
            result = {
                "success": True,
                "violations_detected": filtered_violations,
                "total_violations": len(filtered_violations),
                "risk_level": risk_level,
                "detection_metadata": {
                    "processing_time": round(processing_time, 2),
                    "patterns_checked": len(self.violation_patterns),
                    "detection_focus": detection_focus,
                    "confidence_threshold": threshold,
                    "total_detections": self.detection_count
                },
                "recommendations": recommendations
            }
            
            # 更新统计信息
            self.violations_found += len(filtered_violations)
            
            # 记录检测完成日志
            self.logger.info(f"✅ 违规检测完成，发现 {len(filtered_violations)} 个违规，耗时 {processing_time:.2f}秒")
            
            return result
            
        except Exception as e:
            # 异常处理：确保检测失败不会影响整个系统
            error_msg = f"违规检测过程中发生错误: {str(e)}"
            self.logger.error(f"❌ {error_msg}")
            
            return {
                "success": False,
                "error": error_msg,
                "violations_detected": [],
                "total_violations": 0,
                "processing_time": time.time() - start_time
            }
    
    def _validate_scene_analysis(self, scene_analysis: Dict[str, Any]) -> bool:
        """
        验证场景分析数据的有效性
        
        在执行检测之前，需要确保输入数据包含必要的信息。
        这个方法检查数据的完整性和格式正确性。
        
        Args:
            scene_analysis: 待验证的场景分析数据
            
        Returns:
            bool: 数据是否有效
            
        学习要点：
        - 输入验证的重要性
        - 数据结构的检查方法
        - 防御性编程的实践
        """
        try:
            # 检查基本数据结构
            if not isinstance(scene_analysis, dict):
                self.logger.error("❌ 场景分析数据不是字典格式")
                return False
            
            # 检查必需的字段
            required_fields = ["traffic_participants", "key_observations"]
            for field in required_fields:
                if field not in scene_analysis:
                    self.logger.error(f"❌ 缺少必需字段: {field}")
                    return False
            
            # 检查数据内容
            if not scene_analysis.get("key_observations"):
                self.logger.warning("⚠️ 关键观察为空，检测效果可能受影响")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 数据验证失败: {e}")
            return False
    
    async def _match_violation_pattern(self, 
                                     pattern: ViolationPattern,
                                     scene_analysis: Dict[str, Any],
                                     threshold: float) -> Optional[Dict[str, Any]]:
        """
        执行单个违规模式的匹配检测
        
        这个方法实现了核心的违规检测逻辑：
        1. 关键词匹配检测
        2. 上下文信息分析
        3. 置信度计算
        4. 违规信息构建
        
        Args:
            pattern: 违规检测模式
            scene_analysis: 场景分析数据  
            threshold: 检测置信度阈值
            
        Returns:
            Optional[Dict[str, Any]]: 检测结果，如果未检测到则返回None
            
        学习要点：
        - 模式匹配算法的实现
        - 置信度计算的方法
        - 文本处理和关键词检测
        - 结果数据的构建
        """
        try:
            # 提取场景描述文本，用于关键词匹配
            scene_text = self._extract_scene_text(scene_analysis)
            
            # 步骤1：执行关键词匹配
            keyword_matches = []
            keyword_confidence = 0.0
            
            for keyword in pattern.keywords:
                # 使用正则表达式进行模糊匹配
                # 这里支持部分匹配和大小写不敏感
                if re.search(keyword, scene_text, re.IGNORECASE):
                    keyword_matches.append(keyword)
            
            # 计算关键词匹配置信度
            if pattern.keywords:
                keyword_confidence = len(keyword_matches) / len(pattern.keywords)
            
            # 步骤2：分析上下文信息
            context_confidence = self._analyze_context_factors(pattern, scene_analysis)
            
            # 步骤3：计算综合置信度
            # 综合考虑关键词匹配和上下文信息
            overall_confidence = (
                keyword_confidence * (1 - self.context_weight) + 
                context_confidence * self.context_weight
            )
            
            # 如果置信度不够，返回None
            if overall_confidence < threshold:
                return None
            
            # 步骤4：构建违规检测结果
            violation_result = {
                "violation_type": pattern.violation_type,
                "name": pattern.name,
                "description": pattern.description,
                "severity": pattern.severity.value,
                "confidence": round(overall_confidence, 2),
                "legal_reference": pattern.legal_reference,
                "penalty_info": pattern.penalty_info,
                "evidence": {
                    "matched_keywords": keyword_matches,
                    "keyword_confidence": round(keyword_confidence, 2),
                    "context_confidence": round(context_confidence, 2),
                    "scene_evidence": self._extract_evidence(pattern, scene_analysis)
                },
                "location": self._estimate_violation_location(pattern, scene_analysis),
                "timestamp": self._get_timestamp()
            }
            
            return violation_result
            
        except Exception as e:
            self.logger.error(f"❌ 模式匹配失败 {pattern.name}: {e}")
            return None
    
    def _extract_scene_text(self, scene_analysis: Dict[str, Any]) -> str:
        """
        从场景分析中提取文本内容用于关键词匹配
        
        这个方法将结构化的场景分析数据转换为文本字符串，
        便于进行关键词匹配和文本处理。
        
        Args:
            scene_analysis: 场景分析数据
            
        Returns:
            str: 提取的文本内容
        """
        try:
            text_parts = []
            
            # 提取关键观察
            if "key_observations" in scene_analysis:
                observations = scene_analysis["key_observations"]
                if isinstance(observations, list):
                    text_parts.extend(observations)
                else:
                    text_parts.append(str(observations))
            
            # 提取原始分析内容（如果有）
            if "raw_analysis" in scene_analysis:
                text_parts.append(str(scene_analysis["raw_analysis"]))
            
            # 提取交通参与者信息
            if "traffic_participants" in scene_analysis:
                participants = scene_analysis["traffic_participants"]
                if isinstance(participants, dict):
                    for key, value in participants.items():
                        text_parts.append(f"{key}: {str(value)}")
            
            # 合并所有文本
            combined_text = " ".join(text_parts)
            
            return combined_text
            
        except Exception as e:
            self.logger.error(f"❌ 文本提取失败: {e}")
            return ""
    
    def _analyze_context_factors(self, 
                                pattern: ViolationPattern,
                                scene_analysis: Dict[str, Any]) -> float:
        """
        分析上下文因素对违规判断的影响
        
        违规检测不仅仅依赖关键词匹配，还需要考虑：
        - 环境条件（天气、光照等）
        - 道路状况（类型、拥堵程度等）
        - 交通密度和复杂程度
        - 其他相关因素
        
        Args:
            pattern: 违规检测模式
            scene_analysis: 场景分析数据
            
        Returns:
            float: 上下文置信度 (0.0-1.0)
            
        学习要点：
        - 上下文信息的重要性
        - 多因素权重计算
        - 业务逻辑的量化表示
        """
        try:
            context_score = 0.5  # 基础分数
            
            # 分析环境因素
            environment = scene_analysis.get("environment", {})
            
            # 考虑天气影响
            weather = environment.get("weather", "")
            if "雨" in weather or "雪" in weather:
                # 恶劣天气增加违规风险
                context_score += 0.1
            
            # 考虑光照影响
            lighting = environment.get("lighting", "")
            if "夜晚" in lighting or "黄昏" in lighting:
                # 光线不足增加违规风险
                context_score += 0.1
            
            # 分析道路环境因素
            road_env = scene_analysis.get("road_environment", {})
            
            # 考虑道路类型
            road_type = road_env.get("road_type", "")
            if pattern.violation_type == "lane_violation":
                if "城市道路" in road_type:
                    # 城市道路车道违规更常见
                    context_score += 0.15
            
            # 分析交通密度
            participants = scene_analysis.get("traffic_participants", {})
            total_vehicles = 0
            
            # 统计车辆数量
            for participant_type, info in participants.items():
                if isinstance(info, dict) and "count" in info:
                    total_vehicles += info["count"]
            
            # 交通密度影响
            if total_vehicles > 5:
                # 高密度交通增加违规检测的准确性
                context_score += 0.1
            
            # 确保分数在合理范围内
            context_score = max(0.0, min(1.0, context_score))
            
            return context_score
            
        except Exception as e:
            self.logger.error(f"❌ 上下文分析失败: {e}")
            return 0.5  # 返回中性分数
    
    def _extract_evidence(self, 
                         pattern: ViolationPattern,
                         scene_analysis: Dict[str, Any]) -> List[str]:
        """
        提取违规行为的具体证据
        
        为了让违规检测结果更有说服力，需要提取具体的
        证据信息，包括：
        - 具体的违规行为描述
        - 相关的场景细节
        - 支持违规判断的观察点
        
        Args:
            pattern: 违规检测模式
            scene_analysis: 场景分析数据
            
        Returns:
            List[str]: 证据描述列表
        """
        evidence = []
        
        try:
            # 从关键观察中提取相关证据
            observations = scene_analysis.get("key_observations", [])
            
            for observation in observations:
                # 检查观察是否与当前违规模式相关
                for keyword in pattern.keywords:
                    if keyword in observation:
                        evidence.append(f"观察到: {observation}")
                        break
            
            # 从交通参与者信息中提取证据
            participants = scene_analysis.get("traffic_participants", {})
            
            if pattern.violation_type == "lane_violation":
                # 对于车道违规，提取相关的车辆信息
                non_motor = participants.get("non_motor_vehicles", {})
                motor = participants.get("motor_vehicles", {})
                
                if non_motor.get("count", 0) > 0 and motor.get("count", 0) > 0:
                    evidence.append(f"检测到非机动车 {non_motor.get('count', 0)} 辆，机动车 {motor.get('count', 0)} 辆")
            
            elif pattern.violation_type == "pedestrian_violation":
                # 对于行人违规，提取行人和车辆信息
                pedestrians = participants.get("pedestrians", {})
                motor = participants.get("motor_vehicles", {})
                
                if pedestrians.get("count", 0) > 0:
                    evidence.append(f"检测到行人 {pedestrians.get('count', 0)} 名")
            
            # 如果没有找到具体证据，添加通用描述
            if not evidence:
                evidence.append("基于场景分析的综合判断")
            
            return evidence
            
        except Exception as e:
            self.logger.error(f"❌ 证据提取失败: {e}")
            return ["证据提取过程中出现错误"]
    
    def _estimate_violation_location(self, 
                                   pattern: ViolationPattern,
                                   scene_analysis: Dict[str, Any]) -> str:
        """
        估算违规行为发生的位置
        
        Args:
            pattern: 违规检测模式
            scene_analysis: 场景分析数据
            
        Returns:
            str: 位置描述
        """
        try:
            # 基于道路环境信息估算位置
            road_env = scene_analysis.get("road_environment", {})
            road_type = road_env.get("road_type", "未知路段")
            
            # 基于违规类型提供更具体的位置描述
            if pattern.violation_type == "lane_violation":
                return f"{road_type}的车道区域"
            elif pattern.violation_type == "pedestrian_violation":
                return f"{road_type}的人行横道区域"
            else:
                return f"{road_type}"
                
        except Exception as e:
            self.logger.error(f"❌ 位置估算失败: {e}")
            return "位置未知"
    
    def _filter_and_merge_violations(self, violations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤和合并相似的违规检测结果
        
        有时候同一个违规行为可能被多个模式检测到，
        这个方法负责去重和合并，避免重复报告。
        
        Args:
            violations: 原始检测结果列表
            
        Returns:
            List[Dict[str, Any]]: 过滤后的结果列表
        """
        if not violations:
            return []
        
        try:
            # 按违规类型分组
            grouped_violations = {}
            
            for violation in violations:
                v_type = violation["violation_type"]
                if v_type not in grouped_violations:
                    grouped_violations[v_type] = []
                grouped_violations[v_type].append(violation)
            
            # 对每组选择置信度最高的结果
            filtered_violations = []
            
            for v_type, group in grouped_violations.items():
                # 按置信度排序，选择最高的
                best_violation = max(group, key=lambda x: x["confidence"])
                filtered_violations.append(best_violation)
            
            # 按严重程度排序
            severity_order = {"critical": 4, "high": 3, "medium": 2, "low": 1}
            filtered_violations.sort(
                key=lambda x: severity_order.get(x["severity"], 0), 
                reverse=True
            )
            
            return filtered_violations
            
        except Exception as e:
            self.logger.error(f"❌ 违规结果过滤失败: {e}")
            return violations  # 返回原始结果
    
    def _assess_overall_risk(self, violations: List[Dict[str, Any]]) -> str:
        """
        评估整体风险等级
        
        基于检测到的违规情况，评估整个场景的风险等级。
        
        Args:
            violations: 违规检测结果列表
            
        Returns:
            str: 风险等级 ("low", "medium", "high", "critical")
        """
        if not violations:
            return "low"
        
        # 统计不同严重程度的违规数量
        severity_count = {"critical": 0, "high": 0, "medium": 0, "low": 0}
        
        for violation in violations:
            severity = violation.get("severity", "low")
            severity_count[severity] += 1
        
        # 基于严重程度确定整体风险
        if severity_count["critical"] > 0:
            return "critical"
        elif severity_count["high"] > 0:
            return "high"
        elif severity_count["medium"] > 1:  # 多个中等违规
            return "high"
        elif severity_count["medium"] > 0:
            return "medium"
        else:
            return "low"
    
    def _generate_recommendations(self, 
                                violations: List[Dict[str, Any]],
                                scene_analysis: Dict[str, Any]) -> List[str]:
        """
        生成处理建议
        
        基于检测到的违规情况和场景分析，生成具体的
        处理建议和改进措施。
        
        Args:
            violations: 违规检测结果列表
            scene_analysis: 场景分析数据
            
        Returns:
            List[str]: 建议列表
        """
        recommendations = []
        
        if not violations:
            recommendations.append("场景分析正常，建议继续保持良好的交通秩序")
            return recommendations
        
        try:
            # 基于违规类型生成建议
            violation_types = [v["violation_type"] for v in violations]
            
            if "lane_violation" in violation_types:
                recommendations.extend([
                    "加强非机动车道使用规范的宣传教育",
                    "完善道路标识和隔离设施",
                    "增加该路段的执法巡查频次"
                ])
            
            if "pedestrian_violation" in violation_types:
                recommendations.extend([
                    "加强机动车礼让行人的执法力度",
                    "优化人行横道的信号配时",
                    "增设礼让行人的提醒标识"
                ])
            
            # 基于风险等级添加建议
            risk_level = self._assess_overall_risk(violations)
            
            if risk_level in ["high", "critical"]:
                recommendations.extend([
                    "建议立即采取现场管控措施",
                    "加强该区域的安全警示",
                    "考虑调整交通组织方案"
                ])
            
            # 去重
            recommendations = list(set(recommendations))
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"❌ 建议生成失败: {e}")
            return ["建议加强交通安全管理"]
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def get_detection_statistics(self) -> Dict[str, Any]:
        """
        获取检测统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "total_detections": self.detection_count,
            "violations_found": self.violations_found,
            "violation_rate": round(self.violations_found / max(self.detection_count, 1), 2),
            "patterns_loaded": len(self.violation_patterns),
            "confidence_threshold": self.confidence_threshold
        }