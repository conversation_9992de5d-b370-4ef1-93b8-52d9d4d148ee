# -*- coding: utf-8 -*-
"""
Ollama客户端封装
"""
import asyncio
import aiohttp
import base64
from typing import List, Dict, Any, Optional
from langchain_core.language_models.llms import BaseLLM
from langchain_core.outputs import LLMResult, Generation
from pydantic import Field
from config.settings import OLLAMA_CONFIG
from src.utils.logger import get_logger

logger = get_logger(__name__)

class OllamaMultimodalLLM(BaseLLM):
    """Ollama多模态语言模型封装"""
    
    # 使用Pydantic Field定义模型字段
    base_url: str = Field(default_factory=lambda: OLLAMA_CONFIG["base_url"], description="Ollama服务器地址")
    model_name: str = Field(default_factory=lambda: OLLAMA_CONFIG["model_name"], description="模型名称")
    timeout: int = Field(default_factory=lambda: OLLAMA_CONFIG["timeout"], description="请求超时时间")
    temperature: float = Field(default_factory=lambda: OLLAMA_CONFIG["temperature"], description="生成温度")
    max_tokens: int = Field(default_factory=lambda: OLLAMA_CONFIG["max_tokens"], description="最大令牌数")
    
    def __init__(self, **kwargs):
        # 先提取自定义字段
        base_url = kwargs.pop("base_url", OLLAMA_CONFIG["base_url"])
        model_name = kwargs.pop("model_name", OLLAMA_CONFIG["model_name"])
        timeout = kwargs.pop("timeout", OLLAMA_CONFIG["timeout"])
        temperature = kwargs.pop("temperature", OLLAMA_CONFIG["temperature"])
        max_tokens = kwargs.pop("max_tokens", OLLAMA_CONFIG["max_tokens"])
        
        # 调用父类构造函数
        super().__init__(
            base_url=base_url,
            model_name=model_name,
            timeout=timeout,
            temperature=temperature,
            max_tokens=max_tokens,
            **kwargs
        )
        
    @property
    def _llm_type(self) -> str:
        """返回LLM类型"""
        return "ollama_multimodal"
    
    def _call(self, prompt: str, stop: Optional[List[str]] = None, **kwargs) -> str:
        """同步调用接口（通过异步实现）"""
        return asyncio.run(self._acall(prompt, stop, **kwargs))
    
    async def _acall(self, prompt: str, stop: Optional[List[str]] = None, **kwargs) -> str:
        """异步调用Ollama API"""
        try:
            # 提取图像（如果有）
            images = kwargs.get('images', [])
            
            # 构建请求数据
            request_data = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": self.temperature,
                    "num_predict": self.max_tokens
                }
            }
            
            # 添加图像数据
            if images:
                request_data["images"] = images
            
            # 发送请求
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.post(
                    f"{self.base_url}/api/generate",
                    json=request_data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get("response", "")
                    else:
                        error_text = await response.text()
                        raise Exception(f"Ollama API错误: {response.status} - {error_text}")
                        
        except Exception as e:
            logger.error(f"Ollama调用失败: {str(e)}")
            raise e
    
    def _generate(
        self, 
        prompts: List[str], 
        stop: Optional[List[str]] = None,
        **kwargs
    ) -> LLMResult:
        """同步批量生成（通过异步实现）"""
        return asyncio.run(self._agenerate(prompts, stop, **kwargs))
    
    async def _agenerate(
        self, 
        prompts: List[str], 
        stop: Optional[List[str]] = None,
        **kwargs
    ) -> LLMResult:
        """异步批量生成"""
        generations = []
        
        for prompt in prompts:
            try:
                response = await self._acall(prompt, stop, **kwargs)
                generations.append([Generation(text=response)])
            except Exception as e:
                logger.error(f"生成失败: {str(e)}")
                generations.append([Generation(text=f"生成失败: {str(e)}")])
        
        return LLMResult(
            generations=generations,
            llm_output={"model": self.model_name}
        )
    
    async def generate_with_image(
        self, 
        prompt: str, 
        image_base64: str = None,
        image_path: str = None
    ) -> str:
        """
        使用图像生成回答
        
        Args:
            prompt: 文本提示
            image_base64: base64编码的图像
            image_path: 图像文件路径
            
        Returns:
            生成的回答
        """
        images = []
        
        # 处理图像输入
        if image_base64:
            images.append(image_base64)
        elif image_path:
            try:
                with open(image_path, 'rb') as f:
                    image_data = base64.b64encode(f.read()).decode()
                    images.append(image_data)
            except Exception as e:
                logger.error(f"读取图像文件失败: {str(e)}")
                raise e
        
        return await self._acall(prompt, images=images)
    
    async def check_model_availability(self) -> bool:
        """
        检查模型是否可用
        
        Returns:
            模型是否可用
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/tags") as response:
                    if response.status == 200:
                        tags_data = await response.json()
                        models = [model["name"] for model in tags_data.get("models", [])]
                        return self.model_name in models
                    return False
        except Exception as e:
            logger.error(f"检查模型可用性失败: {str(e)}")
            return False

class OllamaClient:
    """简化的Ollama客户端"""
    
    def __init__(self, base_url: str = None, model: str = None):
        self.base_url = base_url or OLLAMA_CONFIG["base_url"]
        self.model = model or OLLAMA_CONFIG["model_name"]
    
    async def chat(self, messages: List[Dict], **kwargs) -> Dict[str, Any]:
        """
        聊天接口
        
        Args:
            messages: 消息列表
            **kwargs: 其他参数
            
        Returns:
            响应结果
        """
        try:
            request_data = {
                "model": self.model,
                "messages": messages,
                "stream": kwargs.get("stream", False),
                "options": {
                    "temperature": kwargs.get("temperature", OLLAMA_CONFIG["temperature"]),
                    "num_predict": kwargs.get("max_tokens", OLLAMA_CONFIG["max_tokens"])
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/api/chat",
                    json=request_data
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        raise Exception(f"Ollama Chat API错误: {response.status} - {error_text}")
                        
        except Exception as e:
            logger.error(f"Ollama聊天失败: {str(e)}")
            raise e
    
    async def generate(self, prompt: str, images: List[str] = None, **kwargs) -> str:
        """
        生成接口
        
        Args:
            prompt: 文本提示
            images: 图像列表（base64编码）
            **kwargs: 其他参数
            
        Returns:
            生成的文本
        """
        try:
            request_data = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": kwargs.get("temperature", OLLAMA_CONFIG["temperature"]),
                    "num_predict": kwargs.get("max_tokens", OLLAMA_CONFIG["max_tokens"])
                }
            }
            
            if images:
                request_data["images"] = images
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/api/generate",
                    json=request_data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get("response", "")
                    else:
                        error_text = await response.text()
                        raise Exception(f"Ollama Generate API错误: {response.status} - {error_text}")
                        
        except Exception as e:
            logger.error(f"Ollama生成失败: {str(e)}")
            raise e