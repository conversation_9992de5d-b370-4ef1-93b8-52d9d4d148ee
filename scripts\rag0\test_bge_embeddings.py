#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试BGE嵌入模型基本功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.rag.embeddings import BGEEmbeddings

def test_bge_embeddings():
    """测试BGE嵌入模型的各项功能"""
    print("🧪 测试BGE嵌入模型基本功能")
    print("=" * 50)
    
    try:
        # 1. 初始化模型
        print("📦 初始化BGE嵌入模型...")
        embeddings = BGEEmbeddings()
        print("✅ 模型初始化成功!")
        
        # 显示模型信息
        model_info = embeddings.get_model_info()
        print(f"📋 模型信息:")
        print(f"   - 模型名称: {model_info['model_name']}")
        print(f"   - 设备: {model_info['device']}")
        print(f"   - 向量维度: {model_info['embedding_dimension']}")
        print(f"   - 最大序列长度: {model_info['max_seq_length']}")
        
        # 2. 测试单个文本嵌入
        print(f"\n🔍 测试单个文本嵌入...")
        test_text = "机动车超速行驶的处罚标准是什么？"
        vector = embeddings.embed_query(test_text)
        print(f"✅ 嵌入成功!")
        print(f"   - 测试文本: {test_text}")
        print(f"   - 向量维度: {len(vector)}")
        print(f"   - 向量前5个值: {[round(v, 4) for v in vector[:5]]}")
        
        # 3. 测试批量文本嵌入
        print(f"\n📝 测试批量文本嵌入...")
        test_texts = [
            "酒后驾驶的法律后果",
            "雨天驾驶注意事项",
            "人行横道礼让规定",
            "机动车载物要求",
            "安全带使用规定"
        ]
        
        vectors = embeddings.embed_documents(test_texts, show_progress=True)
        print(f"✅ 批量嵌入成功!")
        print(f"   - 文档数量: {len(test_texts)}")
        print(f"   - 向量数量: {len(vectors)}")
        print(f"   - 每个向量维度: {len(vectors[0]) if vectors else 0}")
        
        # 4. 测试相似度计算
        print(f"\n📊 测试相似度计算...")
        similarity_tests = [
            ("超速违法", "超速驾驶"),
            ("酒后驾驶", "醉酒驾车"),
            ("人行横道", "斑马线"),
            ("交通事故", "车辆碰撞"),
            ("超速违法", "雨天驾驶")  # 不相关的对比
        ]
        
        for text1, text2 in similarity_tests:
            similarity = embeddings.compute_similarity(text1, text2)
            print(f"   - '{text1}' vs '{text2}': {similarity:.4f}")
        
        # 5. 性能基准测试
        print(f"\n⚡ 执行性能基准测试...")
        benchmark_results = embeddings.benchmark_performance(num_samples=20)
        
        if "error" not in benchmark_results:
            print(f"✅ 性能测试完成!")
            print(f"   - 批量处理: {benchmark_results['batch_embedding']['docs_per_second']:.1f} docs/sec")
            print(f"   - 单次查询: {benchmark_results['single_query']['queries_per_second']:.1f} queries/sec")
            print(f"   - 设备: {benchmark_results['system_info']['device']}")
        else:
            print(f"❌ 性能测试失败: {benchmark_results['error']}")
        
        print(f"\n🎉 BGE嵌入模型测试全部通过!")
        return True
        
    except Exception as e:
        print(f"❌ BGE嵌入模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_bge_embeddings()
    if not success:
        sys.exit(1)