# -*- coding: utf-8 -*-
"""
工具模块 - Tools Module

这个模块包含了智能交通多模态RAG助手系统中的各种专业分析工具。
主要功能包括：

1. 交通图像分析工具 (TrafficImageAnalyzer)
   - 使用Qwen2.5-VL-7B模型分析交通场景图像
   - 识别道路环境、交通参与者、基础设施等元素
   - 生成客观详细的场景描述

2. 交通违规检测工具 (TrafficViolationDetector)
   - 基于图像分析结果检测潜在的交通违规行为
   - 匹配违规模式并引用相关法规条文
   - 评估违规严重程度和风险等级

3. 交通法规检索工具 (TrafficRegulationSearcher)
   - 从RAG知识库中检索相关交通法规
   - 提供准确的法规条文引用和解释
   - 支持多种检索策略和重排序

4. 交通安全评估工具 (TrafficSafetyAssessor)
   - 综合分析交通场景的安全风险
   - 计算安全等级（A-E级别评估）
   - 识别主要风险因素

5. 改进建议生成工具 (TrafficSuggestionGenerator)
   - 基于违规检测和安全评估结果
   - 生成具体可行的改进措施建议
   - 提供实施指导和预期效果

这些工具通过LangChain Agent框架协同工作，为用户提供专业的交通安全分析服务。
"""

# 导入所有核心工具类
from .traffic_image_analyzer import TrafficImageAnalyzer
from .traffic_violation_detector import TrafficViolationDetector
from .traffic_regulation_searcher import TrafficRegulationSearcher
from .traffic_safety_assessor import TrafficSafetyAssessor
from .traffic_suggestion_generator import TrafficSuggestionGenerator

# 导出所有工具类，便于外部模块导入
__all__ = [
    'TrafficImageAnalyzer',
    'TrafficViolationDetector', 
    'TrafficRegulationSearcher',
    'TrafficSafetyAssessor',
    'TrafficSuggestionGenerator'
]