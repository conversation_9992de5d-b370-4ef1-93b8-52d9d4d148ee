# 智能交通多模态RAG助手 - Docker配置文件
# Traffic Multimodal RAG Assistant - Dockerfile
# 
# 这个Dockerfile用于构建智能交通助手的容器镜像
# 包含以下功能：
# 1. Python 3.9运行环境
# 2. 系统依赖和Python包安装
# 3. 应用代码部署
# 4. Streamlit Web服务配置
# 
# 构建命令：
#   docker build -t traffic-rag-assistant .
# 
# 运行命令：
#   docker run -p 8501:8501 -v $(pwd)/data:/app/data traffic-rag-assistant

# 使用Python 3.9官方镜像作为基础镜像
FROM python:3.9-slim

# 设置维护者信息
LABEL maintainer="AI Algorithm Engineer <<EMAIL>>"
LABEL description="智能交通多模态RAG助手 - 基于LangChain Agent + Qwen2.5-VL + ChromaDB"
LABEL version="1.0.0"

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV DEBIAN_FRONTEND=noninteractive

# 设置工作目录
WORKDIR /app

# 更新系统包管理器并安装系统依赖
RUN apt-get update && apt-get install -y \
    # 系统工具
    curl \
    wget \
    git \
    # 图像处理依赖
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    # OpenCV依赖
    libgdal-dev \
    # 网络工具
    netcat-openbsd \
    # 清理缓存
    && rm -rf /var/lib/apt/lists/*

# 复制requirements.txt并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 复制项目代码
COPY . .

# 创建必要的目录
RUN mkdir -p data/knowledge_base data/test_images data/models logs && \
    chmod -R 755 data logs

# 设置Python路径
ENV PYTHONPATH="/app:${PYTHONPATH}"

# 暴露Streamlit默认端口
EXPOSE 8501

# 创建健康检查脚本
RUN echo '#!/bin/bash\ncurl -f http://localhost:8501/_stcore/health || exit 1' > /app/healthcheck.sh && \
    chmod +x /app/healthcheck.sh

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /app/healthcheck.sh

# 创建启动脚本
RUN echo '#!/bin/bash\n\
echo "🚗 启动智能交通多模态RAG助手..."\n\
echo "📋 检查系统状态..."\n\
python main.py --check\n\
if [ $? -eq 0 ]; then\n\
    echo "✅ 系统检查通过，启动Web界面..."\n\
    streamlit run src/web/main_app.py \\\n\
        --server.address 0.0.0.0 \\\n\
        --server.port 8501 \\\n\
        --browser.gatherUsageStats false \\\n\
        --server.headless true \\\n\
        --server.enableCORS false \\\n\
        --server.enableXsrfProtection false\n\
else\n\
    echo "❌ 系统检查失败，请检查配置"\n\
    exit 1\n\
fi\n' > /app/start.sh && chmod +x /app/start.sh

# 设置启动命令
CMD ["/app/start.sh"]

# 添加元数据标签
LABEL org.opencontainers.image.title="智能交通多模态RAG助手"
LABEL org.opencontainers.image.description="基于LangChain Agent框架的专业交通安全分析系统"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.authors="AI Algorithm Engineer"
LABEL org.opencontainers.image.source="https://github.com/example/traffic-rag-assistant"
LABEL org.opencontainers.image.documentation="https://github.com/example/traffic-rag-assistant/blob/main/README.md"