# -*- coding: utf-8 -*-
"""
交通图像分析工具 - Traffic Image Analyzer

这个模块实现了专业的交通场景图像分析功能，是整个系统的视觉理解核心。

主要功能：
1. 使用Qwen2.5-VL-7B多模态大语言模型分析交通图像
2. 识别和描述道路环境（道路类型、车道配置、路面状况等）
3. 检测交通参与者（机动车、非机动车、行人及其行为）
4. 识别交通基础设施（信号灯、标志标线、护栏等）
5. 分析环境条件（天气、光照、时间等影响因素）

设计思路：
- 采用结构化的提示词工程，确保分析结果的一致性和完整性
- 使用专业的交通领域术语，提高分析的专业性
- 支持多种图像输入格式（base64、文件路径等）
- 提供详细的错误处理和日志记录

学习要点：
- 如何设计多模态LLM的提示词
- 如何处理图像输入和编码
- 如何结构化输出复杂的分析结果
- 如何进行异步操作和错误处理
"""

import json
import base64
from typing import Dict, Any, Optional, List
from pathlib import Path

from src.utils.logger import get_logger
from src.utils.ollama_client import OllamaClient
from config.settings import OLLAMA_CONFIG

# 获取日志记录器，用于记录工具的运行状态和调试信息
logger = get_logger(__name__)

class TrafficImageAnalyzer:
    """
    交通图像分析器
    
    这个类封装了交通图像分析的核心逻辑，使用多模态大语言模型
    对交通场景进行专业分析。
    
    核心方法：
    - analyze(): 主要分析接口，接受图像输入并返回结构化分析结果
    - _build_analysis_prompt(): 构建专业的分析提示词
    - _parse_analysis_result(): 解析和结构化分析结果
    - _validate_image_input(): 验证图像输入的有效性
    
    设计原则：
    - 单一职责：专注于图像分析功能
    - 可扩展性：支持不同类型的分析需求
    - 鲁棒性：完善的错误处理和边界情况处理
    - 可测试性：清晰的输入输出接口
    """
    
    def __init__(self):
        """
        初始化交通图像分析器
        
        初始化步骤：
        1. 设置日志记录器，用于调试和监控
        2. 创建Ollama客户端，连接到多模态LLM服务
        3. 定义分析模板和配置参数
        4. 初始化性能统计变量
        """
        # 获取类专用的日志记录器，便于区分不同组件的日志
        self.logger = get_logger(self.__class__.__name__)
        
        # 创建Ollama客户端，用于与Qwen2.5-VL模型通信
        # 注意：这里使用简化的客户端，专注于生成功能
        self.ollama_client = OllamaClient(
            base_url=OLLAMA_CONFIG["base_url"],
            model=OLLAMA_CONFIG["model_name"]
        )
        
        # 性能统计：记录分析次数和总耗时，用于性能监控
        self.analysis_count = 0
        self.total_analysis_time = 0.0
        
        # 初始化完成日志
        self.logger.info("🖼️ 交通图像分析器初始化完成")
    
    async def analyze(self, 
                     image_input: Any, 
                     analysis_focus: Optional[List[str]] = None,
                     output_format: str = "structured") -> Dict[str, Any]:
        """
        分析交通场景图像的主要接口
        
        这是整个工具的核心方法，负责协调整个分析流程：
        1. 输入验证和预处理
        2. 构建分析提示词
        3. 调用多模态LLM进行分析
        4. 解析和结构化结果
        5. 错误处理和日志记录
        
        Args:
            image_input: 图像输入，支持多种格式：
                        - base64编码字符串
                        - 文件路径字符串  
                        - 二进制数据
            analysis_focus: 分析重点列表，如 ["violations", "safety", "infrastructure"]
                           用于指导分析的侧重点
            output_format: 输出格式，"structured" | "narrative" | "json"
                          控制返回结果的格式
        
        Returns:
            Dict[str, Any]: 包含分析结果的字典，结构如下：
            {
                "success": bool,              # 分析是否成功
                "analysis_result": dict,      # 结构化的分析结果
                "raw_response": str,          # LLM的原始回答
                "metadata": dict,             # 元数据（耗时、模型信息等）
                "error": str                  # 错误信息（如果失败）
            }
        
        学习要点：
        - 异步方法的设计和实现
        - 复杂参数的处理和验证
        - 错误处理的最佳实践
        - 性能监控的实现方式
        """
        # 记录分析开始时间，用于性能统计
        import time
        start_time = time.time()
        
        try:
            # 步骤1：记录分析开始日志，包含关键参数信息
            self.logger.info(f"🔍 开始分析交通图像，重点：{analysis_focus}, 格式：{output_format}")
            
            # 步骤2：验证和预处理图像输入
            # 这一步确保输入的有效性，避免后续处理出错
            image_data = await self._validate_and_process_image(image_input)
            if not image_data["success"]:
                # 如果图像处理失败，直接返回错误结果
                return {
                    "success": False,
                    "error": image_data["error"],
                    "metadata": {"processing_time": time.time() - start_time}
                }
            
            # 步骤3：构建专业的分析提示词
            # 提示词工程是多模态LLM应用的关键，直接影响分析质量
            analysis_prompt = self._build_analysis_prompt(
                analysis_focus=analysis_focus,
                output_format=output_format
            )
            
            # 步骤4：调用多模态LLM进行图像分析
            # 这是核心的AI推理步骤，将图像和文本一起发送给模型
            self.logger.info("🧠 调用Qwen2.5-VL模型进行图像分析...")
            raw_response = await self.ollama_client.generate(
                prompt=analysis_prompt,
                images=[image_data["base64_image"]],  # 将图像作为base64传递给模型
                temperature=0.1,  # 低温度确保输出的一致性和准确性
                max_tokens=800    # 控制输出长度，避免过长的回答
            )
            
            # 步骤5：解析和结构化分析结果
            # 将LLM的自然语言输出转换为结构化数据
            parsed_result = await self._parse_analysis_result(
                raw_response, 
                output_format
            )
            
            # 步骤6：计算性能统计信息
            processing_time = time.time() - start_time
            self.analysis_count += 1
            self.total_analysis_time += processing_time
            
            # 步骤7：构建成功的返回结果
            result = {
                "success": True,
                "analysis_result": parsed_result,
                "raw_response": raw_response,
                "metadata": {
                    "processing_time": round(processing_time, 2),
                    "model_used": OLLAMA_CONFIG["model_name"],
                    "analysis_focus": analysis_focus,
                    "output_format": output_format,
                    "image_info": image_data.get("image_info", {}),
                    "total_analyses": self.analysis_count,
                    "avg_processing_time": round(self.total_analysis_time / self.analysis_count, 2)
                }
            }
            
            # 记录成功日志，包含关键性能指标
            self.logger.info(f"✅ 图像分析完成，耗时：{processing_time:.2f}秒")
            return result
            
        except Exception as e:
            # 异常处理：记录错误并返回失败结果
            # 这里采用优雅降级的策略，不让异常中断整个流程
            error_msg = f"图像分析过程中发生错误: {str(e)}"
            self.logger.error(f"❌ {error_msg}")
            
            return {
                "success": False,
                "error": error_msg,
                "metadata": {
                    "processing_time": time.time() - start_time,
                    "error_type": type(e).__name__
                }
            }
    
    async def _validate_and_process_image(self, image_input: Any) -> Dict[str, Any]:
        """
        验证和预处理图像输入
        
        这个方法处理多种类型的图像输入，并将其统一转换为base64格式。
        这是一个重要的预处理步骤，确保后续处理的一致性。
        
        支持的输入格式：
        1. base64编码字符串（直接使用）
        2. 文件路径字符串（读取文件并编码）
        3. 二进制数据（编码为base64）
        4. PIL Image对象（转换并编码）
        
        Args:
            image_input: 各种格式的图像输入
            
        Returns:
            Dict[str, Any]: 处理结果，包含base64图像数据和元信息
            
        学习要点：
        - 如何处理多种数据类型
        - 图像编码和解码的实现
        - 错误处理的细节
        - 元数据的提取和记录
        """
        try:
            # 情况1：输入已经是base64编码的字符串
            if isinstance(image_input, str):
                # 判断是否为文件路径
                if Path(image_input).exists():
                    # 从文件读取图像并编码
                    self.logger.info(f"📁 从文件读取图像: {image_input}")
                    with open(image_input, 'rb') as f:
                        image_bytes = f.read()
                    base64_image = base64.b64encode(image_bytes).decode('utf-8')
                    
                    # 提取文件信息作为元数据
                    file_path = Path(image_input)
                    image_info = {
                        "source": "file",
                        "filename": file_path.name,
                        "size_bytes": len(image_bytes),
                        "format": file_path.suffix.lower()
                    }
                else:
                    # 假设输入已经是base64编码
                    self.logger.info("🔤 处理base64编码的图像")
                    base64_image = image_input
                    image_info = {
                        "source": "base64",
                        "size_chars": len(image_input)
                    }
            
            # 情况2：输入是二进制数据
            elif isinstance(image_input, bytes):
                self.logger.info("💾 处理二进制图像数据")
                base64_image = base64.b64encode(image_input).decode('utf-8')
                image_info = {
                    "source": "bytes",
                    "size_bytes": len(image_input)
                }
            
            # 情况3：其他类型的输入（尝试转换）
            else:
                # 尝试从PIL Image或其他对象中提取数据
                self.logger.info("🖼️ 尝试处理其他类型的图像输入")
                # 这里可以添加更多的处理逻辑，比如PIL Image的处理
                raise ValueError(f"不支持的图像输入类型: {type(image_input)}")
            
            # 验证base64编码的有效性
            try:
                # 尝试解码验证
                decoded_data = base64.b64decode(base64_image)
                if len(decoded_data) == 0:
                    raise ValueError("图像数据为空")
                    
                # 更新图像信息
                image_info["decoded_size"] = len(decoded_data)
                
            except Exception as decode_error:
                raise ValueError(f"无效的图像数据: {str(decode_error)}")
            
            # 返回成功结果
            return {
                "success": True,
                "base64_image": base64_image,
                "image_info": image_info
            }
            
        except Exception as e:
            # 图像处理失败的错误处理
            error_msg = f"图像预处理失败: {str(e)}"
            self.logger.error(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg
            }
    
    def _build_analysis_prompt(self, 
                              analysis_focus: Optional[List[str]] = None, 
                              output_format: str = "structured") -> str:
        """
        构建交通图像分析的专业提示词
        
        提示词工程是大语言模型应用的核心技术。一个好的提示词能够：
        1. 引导模型产生高质量、一致的输出
        2. 确保输出符合特定的格式和结构要求
        3. 体现领域专业知识和分析标准
        4. 控制输出的详细程度和重点
        
        这个方法采用了模板化的提示词设计，包含：
        - 角色定位：明确AI的专家身份
        - 任务描述：清楚说明分析目标
        - 输出格式：规定结构化的输出要求
        - 专业标准：体现交通领域的专业性
        
        Args:
            analysis_focus: 分析重点列表，用于自定义分析侧重点
            output_format: 输出格式要求
            
        Returns:
            str: 构建好的提示词
            
        学习要点：
        - 提示词工程的最佳实践
        - 如何设计结构化输出的提示词
        - 领域知识在提示词中的体现
        - 动态提示词的构建技巧
        """
        
        # 基础角色和任务定义
        # 这部分定义AI的身份和核心任务，是提示词的基础
        base_prompt = """
你是一名专业的交通安全分析专家，具备丰富的道路交通管理和安全评估经验。
现在需要你对一张交通场景图像进行专业分析。

## 分析任务
请仔细观察图像中的交通场景，并按照以下结构进行专业分析：

### 1. 道路环境分析
- 道路类型（城市道路/高速公路/乡村道路等）
- 车道配置（车道数量、宽度、分隔设施等）
- 路面状况（干燥/湿滑/破损等）
- 道路标识（标志标线的完整性和清晰度）

### 2. 交通参与者识别
- 机动车：类型、数量、位置、行驶状态
- 非机动车：类型、数量、位置、行驶状态  
- 行人：数量、位置、行为状态
- 其他交通参与者

### 3. 交通设施评估
- 信号灯：类型、状态、可见性
- 交通标志：类型、位置、清晰度
- 护栏隔离：类型、完整性
- 其他交通设施

### 4. 环境条件分析
- 天气条件（晴天/雨天/雾天等）
- 光照条件（白天/夜晚/黄昏等）
- 能见度情况
- 其他环境因素

### 5. 关键观察点
- 值得注意的交通行为
- 潜在的安全隐患
- 特殊的交通情况
- 其他重要观察
"""
        
        # 根据分析重点调整提示词
        # 这里展示了如何根据用户需求动态调整提示词内容
        if analysis_focus:
            focus_prompt = "\n## 特别关注\n请在分析中特别关注以下方面：\n"
            
            # 将分析重点映射为具体的分析指导
            focus_mapping = {
                "violations": "- 仔细识别可能的交通违规行为",
                "safety": "- 重点评估安全风险和隐患",
                "infrastructure": "- 详细分析交通基础设施状况",
                "participants": "- 深入分析各类交通参与者的行为",
                "environment": "- 全面评估环境条件的影响"
            }
            
            for focus in analysis_focus:
                if focus in focus_mapping:
                    focus_prompt += focus_mapping[focus] + "\n"
            
            base_prompt += focus_prompt
        
        # 根据输出格式添加格式要求
        # 不同的输出格式需要不同的格式化指导
        if output_format == "structured":
            format_prompt = """
## 输出格式要求
请严格按照以下JSON格式输出分析结果：

```json
{
    "road_environment": {
        "road_type": "道路类型",
        "lane_config": "车道配置",
        "surface_condition": "路面状况",
        "markings_condition": "标识状况"
    },
    "traffic_participants": {
        "motor_vehicles": {
            "count": 数量,
            "types": ["类型列表"],
            "behaviors": ["行为描述"]
        },
        "non_motor_vehicles": {
            "count": 数量,
            "types": ["类型列表"], 
            "behaviors": ["行为描述"]
        },
        "pedestrians": {
            "count": 数量,
            "locations": ["位置描述"],
            "behaviors": ["行为描述"]
        }
    },
    "traffic_facilities": {
        "signals": "信号灯状况",
        "signs": "交通标志状况",
        "barriers": "护栏状况",
        "other": "其他设施"
    },
    "environment": {
        "weather": "天气条件",
        "lighting": "光照条件",
        "visibility": "能见度",
        "time_period": "时间段"
    },
    "key_observations": [
        "关键观察点1",
        "关键观察点2",
        "..."
    ]
}
```

## 分析要求
- 保持客观和准确，基于图像中的实际内容进行描述
- 使用专业的交通术语和标准表述
- 如果某些内容在图像中不清楚，请明确说明
- 重点关注安全相关的因素和潜在风险
"""
        elif output_format == "narrative":
            format_prompt = """
## 输出格式要求
请以专业报告的形式，用自然语言详细描述分析结果。
- 使用专业术语，保持客观准确
- 按照道路环境、交通参与者、设施状况、环境条件的顺序组织内容
- 重点突出安全相关的观察和发现
- 如有潜在问题，请明确指出
"""
        else:  # json格式
            format_prompt = """
## 输出格式要求
请以标准JSON格式输出所有分析结果，确保数据结构清晰，便于程序处理。
"""
        
        # 组合完整的提示词
        complete_prompt = base_prompt + format_prompt
        
        # 记录提示词构建日志（用于调试和优化）
        self.logger.debug(f"🔧 构建分析提示词，重点：{analysis_focus}，格式：{output_format}")
        
        return complete_prompt
    
    async def _parse_analysis_result(self, 
                                   raw_response: str, 
                                   output_format: str) -> Dict[str, Any]:
        """
        解析和结构化LLM的分析结果
        
        由于大语言模型的输出是自然语言文本，我们需要将其转换为
        结构化的数据格式，便于后续的处理和使用。
        
        这个方法处理多种输出格式：
        1. structured: 解析JSON格式的结构化数据
        2. narrative: 处理自然语言描述
        3. json: 直接解析JSON数据
        
        Args:
            raw_response: LLM的原始回答文本
            output_format: 期望的输出格式
            
        Returns:
            Dict[str, Any]: 结构化的分析结果
            
        学习要点：
        - 自然语言处理和结构化提取
        - JSON解析和错误处理
        - 正则表达式的使用
        - 数据清洗和标准化
        """
        try:
            # 情况1：结构化JSON格式
            if output_format == "structured" or output_format == "json":
                # 尝试从回答中提取JSON数据
                # 使用正则表达式找到JSON代码块
                import re
                
                # 查找被```json包围的JSON代码块
                json_pattern = r'```json\s*(\{.*?\})\s*```'
                json_match = re.search(json_pattern, raw_response, re.DOTALL)
                
                if json_match:
                    # 提取JSON字符串并解析
                    json_str = json_match.group(1)
                    try:
                        parsed_data = json.loads(json_str)
                        self.logger.info("✅ 成功解析JSON格式的分析结果")
                        return parsed_data
                    except json.JSONDecodeError as e:
                        self.logger.warning(f"⚠️ JSON解析失败: {e}，尝试其他方法")
                
                # 如果没有找到JSON代码块，尝试直接解析整个回答
                try:
                    parsed_data = json.loads(raw_response)
                    return parsed_data
                except json.JSONDecodeError:
                    # JSON解析失败，使用文本解析方法
                    self.logger.warning("⚠️ 无法解析为JSON，使用文本解析方法")
                    return await self._parse_narrative_response(raw_response)
            
            # 情况2：自然语言叙述格式
            else:
                return await self._parse_narrative_response(raw_response)
                
        except Exception as e:
            # 解析失败的兜底处理
            self.logger.error(f"❌ 结果解析失败: {e}")
            return {
                "parsing_error": True,
                "raw_content": raw_response,
                "error_message": str(e),
                "fallback_summary": self._extract_key_points(raw_response)
            }
    
    async def _parse_narrative_response(self, response: str) -> Dict[str, Any]:
        """
        解析自然语言格式的分析结果
        
        当LLM输出自然语言而不是结构化JSON时，我们需要使用
        文本处理技术来提取关键信息。这包括：
        1. 关键词提取
        2. 实体识别
        3. 分类和归纳
        4. 结构化重组
        
        Args:
            response: 自然语言格式的分析结果
            
        Returns:
            Dict[str, Any]: 结构化的分析结果
            
        学习要点：
        - 自然语言处理的基本技巧
        - 正则表达式的高级应用
        - 文本分类和提取方法
        - 启发式规则的设计
        """
        try:
            # 初始化结果结构
            result = {
                "road_environment": {},
                "traffic_participants": {},
                "traffic_facilities": {},
                "environment": {},
                "key_observations": [],
                "raw_analysis": response
            }
            
            # 使用正则表达式和关键词匹配来提取信息
            # 这里展示了基本的文本处理技巧
            
            # 提取道路类型
            road_patterns = {
                "城市道路": r"城市道路|市区道路|城区道路",
                "高速公路": r"高速公路|高速路|快速路",
                "乡村道路": r"乡村道路|农村道路|县道|乡道"
            }
            
            for road_type, pattern in road_patterns.items():
                if re.search(pattern, response):
                    result["road_environment"]["road_type"] = road_type
                    break
            
            # 提取车辆数量信息
            # 使用正则表达式匹配数字和车辆类型
            vehicle_patterns = [
                r"(\d+)\s*辆?\s*(机动车|汽车|车辆)",
                r"(机动车|汽车|车辆)\s*(\d+)\s*辆?",
                r"共?\s*(\d+)\s*辆?\s*(车|机动车)"
            ]
            
            for pattern in vehicle_patterns:
                matches = re.findall(pattern, response)
                for match in matches:
                    if len(match) == 2:
                        try:
                            # 尝试提取数字
                            if match[0].isdigit():
                                count = int(match[0])
                                vehicle_type = match[1]
                            elif match[1].isdigit():
                                count = int(match[1])
                                vehicle_type = match[0]
                            else:
                                continue
                                
                            result["traffic_participants"]["motor_vehicles"] = {
                                "count": count,
                                "types": [vehicle_type]
                            }
                            break
                        except ValueError:
                            continue
            
            # 提取环境条件
            weather_patterns = {
                "晴天": r"晴天|晴朗|阳光",
                "雨天": r"雨天|下雨|雨水|湿润",
                "阴天": r"阴天|多云|阴暗",
                "雾天": r"雾天|大雾|雾霾"
            }
            
            for weather, pattern in weather_patterns.items():
                if re.search(pattern, response):
                    result["environment"]["weather"] = weather
                    break
            
            # 提取关键观察点
            # 寻找表示问题或重要信息的关键词
            observation_keywords = [
                "违规", "违法", "不规范", "安全隐患", "风险", 
                "注意", "问题", "建议", "改进", "优化"
            ]
            
            sentences = response.split('。')
            for sentence in sentences:
                for keyword in observation_keywords:
                    if keyword in sentence and sentence.strip():
                        result["key_observations"].append(sentence.strip() + '。')
                        break
            
            # 去重关键观察点
            result["key_observations"] = list(set(result["key_observations"]))
            
            self.logger.info("✅ 自然语言分析结果解析完成")
            return result
            
        except Exception as e:
            # 文本解析失败的处理
            self.logger.error(f"❌ 自然语言解析失败: {e}")
            return {
                "parsing_error": True,
                "raw_content": response,
                "error_message": str(e)
            }
    
    def _extract_key_points(self, text: str) -> List[str]:
        """
        从文本中提取关键要点的兜底方法
        
        当所有解析方法都失败时，这个方法提供最基本的信息提取，
        确保系统不会完全失败。
        
        Args:
            text: 待处理的文本
            
        Returns:
            List[str]: 提取的关键要点列表
        """
        try:
            # 简单的关键句提取逻辑
            sentences = text.split('。')
            key_points = []
            
            # 选择包含重要关键词的句子
            important_keywords = [
                "道路", "车辆", "行人", "信号", "标志", 
                "安全", "违规", "建议", "状况", "条件"
            ]
            
            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) > 10:  # 过滤太短的句子
                    for keyword in important_keywords:
                        if keyword in sentence:
                            key_points.append(sentence + '。')
                            break
            
            # 限制返回的要点数量
            return key_points[:5]
            
        except Exception as e:
            self.logger.error(f"❌ 关键要点提取失败: {e}")
            return [f"原始分析内容：{text[:200]}..."]
    
    def get_analysis_statistics(self) -> Dict[str, Any]:
        """
        获取分析统计信息
        
        这个方法提供工具使用的统计信息，用于性能监控和优化。
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "total_analyses": self.analysis_count,
            "total_time": round(self.total_analysis_time, 2),
            "average_time": round(self.total_analysis_time / max(self.analysis_count, 1), 2),
            "model_used": OLLAMA_CONFIG["model_name"]
        }