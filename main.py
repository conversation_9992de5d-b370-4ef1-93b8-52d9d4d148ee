#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能交通多模态RAG助手 - 主启动程序
Traffic Multimodal RAG Assistant - Main Entry Point

这是系统的主启动程序，负责：
1. 初始化系统环境和配置
2. 启动各个核心服务组件
3. 检查依赖服务的可用性
4. 启动Web界面服务

使用方法：
    python main.py              # 启动完整系统
    python main.py --test       # 启动测试模式
    python main.py --config     # 显示配置信息
    python main.py --check      # 检查系统状态

系统要求：
- Python 3.8+
- Ollama服务运行在localhost:11434
- Qwen2.5-VL-7B模型已安装
- 所需Python依赖包已安装

作者：AI Algorithm Engineer
版本：v1.0.0
"""

import sys
import os
import argparse
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

from config.settings import OLLAMA_CONFIG, CHROMA_CONFIG, STREAMLIT_CONFIG
from src.utils.logger import setup_logger, get_logger

# 设置日志
setup_logger()
logger = get_logger(__name__)

class SystemLauncher:
    """系统启动器 - 负责系统初始化和启动流程"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        
    async def check_dependencies(self) -> bool:
        """
        检查系统依赖
        
        Returns:
            bool: 依赖检查是否通过
        """
        self.logger.info("🔍 开始检查系统依赖...")
        
        # 检查Ollama服务
        if not await self._check_ollama_service():
            self.logger.error("❌ Ollama服务不可用")
            return False
        
        # 检查模型可用性
        if not await self._check_model_availability():
            self.logger.error("❌ Qwen2.5-VL模型不可用")
            return False
            
        # 检查Python依赖
        if not self._check_python_dependencies():
            self.logger.error("❌ Python依赖不完整")
            return False
            
        # 检查数据目录
        if not self._check_data_directories():
            self.logger.error("❌ 数据目录创建失败")
            return False
            
        self.logger.info("✅ 系统依赖检查通过")
        return True
    
    async def _check_ollama_service(self) -> bool:
        """检查Ollama服务状态"""
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{OLLAMA_CONFIG['base_url']}/api/version",
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        version_info = await response.json()
                        self.logger.info(f"✅ Ollama服务运行正常，版本：{version_info.get('version', 'unknown')}")
                        return True
                    else:
                        self.logger.error(f"❌ Ollama服务响应异常：{response.status}")
                        return False
        except Exception as e:
            self.logger.error(f"❌ Ollama服务连接失败：{str(e)}")
            return False
    
    async def _check_model_availability(self) -> bool:
        """检查模型可用性"""
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{OLLAMA_CONFIG['base_url']}/api/tags",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        tags_data = await response.json()
                        models = [model["name"] for model in tags_data.get("models", [])]
                        
                        if OLLAMA_CONFIG["model_name"] in models:
                            self.logger.info(f"✅ 模型 {OLLAMA_CONFIG['model_name']} 可用")
                            return True
                        else:
                            self.logger.error(f"❌ 模型 {OLLAMA_CONFIG['model_name']} 未找到")
                            self.logger.info(f"可用模型：{models}")
                            return False
                    else:
                        return False
        except Exception as e:
            self.logger.error(f"❌ 模型检查失败：{str(e)}")
            return False
    
    def _check_python_dependencies(self) -> bool:
        """检查Python依赖"""
        required_packages = [
            'streamlit', 'langchain', 'chromadb', 
            'aiohttp', 'pillow', 'opencv-python', 'numpy'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            self.logger.error(f"❌ 缺少依赖包：{missing_packages}")
            self.logger.info("请运行：pip install -r requirements.txt")
            return False
        
        self.logger.info("✅ Python依赖检查通过")
        return True
    
    def _check_data_directories(self) -> bool:
        """检查并创建数据目录"""
        try:
            # 确保必要目录存在
            directories = [
                CHROMA_CONFIG["persist_directory"],
                PROJECT_ROOT / "logs",
                PROJECT_ROOT / "data" / "test_images",
                PROJECT_ROOT / "data" / "models"
            ]
            
            for directory in directories:
                Path(directory).mkdir(parents=True, exist_ok=True)
            
            self.logger.info("✅ 数据目录检查通过")
            return True
        except Exception as e:
            self.logger.error(f"❌ 数据目录创建失败：{str(e)}")
            return False
    
    def initialize_system(self):
        """初始化系统组件"""
        self.logger.info("🚀 开始初始化系统组件...")
        
        try:
            # 初始化知识库
            self._initialize_knowledge_base()
            
            # 初始化Agent
            self._initialize_agent()
            
            self.logger.info("✅ 系统组件初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 系统初始化失败：{str(e)}")
            return False
    
    def _initialize_knowledge_base(self):
        """初始化知识库"""
        self.logger.info("📚 初始化交通知识库...")
        # 这里会在后续阶段实现具体的知识库初始化逻辑
        pass
    
    def _initialize_agent(self):
        """初始化Agent"""
        self.logger.info("🤖 初始化LangChain Agent...")
        # 这里会在后续阶段实现具体的Agent初始化逻辑
        pass
    
    def start_web_interface(self):
        """启动Web界面"""
        self.logger.info("🌐 启动Web界面...")
        
        try:
            import subprocess
            
            # 构建Streamlit启动命令
            streamlit_cmd = [
                "streamlit", "run",
                str(PROJECT_ROOT / "src" / "web" / "main_app.py"),
                "--server.address", "0.0.0.0",
                "--server.port", "8501",
                "--browser.gatherUsageStats", "false",
                "--server.headless", "true"
            ]
            
            self.logger.info("启动命令：" + " ".join(streamlit_cmd))
            self.logger.info("🌐 Web界面将在 http://localhost:8501 启动")
            
            # 启动Streamlit
            subprocess.run(streamlit_cmd)
            
        except Exception as e:
            self.logger.error(f"❌ Web界面启动失败：{str(e)}")
            return False
    
    def show_config(self):
        """显示系统配置信息"""
        print("\n" + "="*60)
        print("🚗 智能交通多模态RAG助手 - 系统配置")
        print("="*60)
        print(f"📂 项目根目录: {PROJECT_ROOT}")
        print(f"🤖 Ollama服务: {OLLAMA_CONFIG['base_url']}")
        print(f"🧠 模型名称: {OLLAMA_CONFIG['model_name']}")
        print(f"📚 知识库目录: {CHROMA_CONFIG['persist_directory']}")
        print(f"🌐 Web端口: 8501")
        print("="*60)

def main():
    """主函数 - 解析命令行参数并启动相应服务"""
    
    parser = argparse.ArgumentParser(
        description="智能交通多模态RAG助手",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        "--test", 
        action="store_true", 
        help="启动测试模式"
    )
    
    parser.add_argument(
        "--config", 
        action="store_true", 
        help="显示配置信息"
    )
    
    parser.add_argument(
        "--check", 
        action="store_true", 
        help="检查系统状态"
    )
    
    args = parser.parse_args()
    
    # 创建系统启动器
    launcher = SystemLauncher()
    
    # 显示配置信息
    if args.config:
        launcher.show_config()
        return
    
    # 检查系统状态
    if args.check:
        success = asyncio.run(launcher.check_dependencies())
        sys.exit(0 if success else 1)
    
    # 系统启动流程
    logger.info("🚗 智能交通多模态RAG助手启动中...")
    
    try:
        # 1. 检查依赖
        if not asyncio.run(launcher.check_dependencies()):
            logger.error("❌ 依赖检查失败，启动中止")
            sys.exit(1)
        
        # 2. 初始化系统
        if not launcher.initialize_system():
            logger.error("❌ 系统初始化失败，启动中止")
            sys.exit(1)
        
        # 3. 启动Web界面
        if args.test:
            logger.info("🧪 测试模式：跳过Web界面启动")
        else:
            launcher.start_web_interface()
    
    except KeyboardInterrupt:
        logger.info("👋 用户中断，系统正常退出")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ 系统启动失败：{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()