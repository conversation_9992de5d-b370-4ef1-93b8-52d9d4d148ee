#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
纯净的图像先行RAG架构测试
完全基于图像内容，无任何预设
"""

import asyncio
import sys
from pathlib import Path

project_root = Path(__file__).parent
sys.path.append(str(project_root))

from src.agent.traffic_rag_agent import create_traffic_rag_agent
from src.utils.logger import get_logger

logger = get_logger(__name__)

# 全局测试图片路径配置 - 只需修改这一个地方
TEST_IMAGE_PATH = "/home/<USER>/lhp/projects/0714agent/my-agent1/data/test_images/test2.png"

async def test_pure_image_first_rag():
    """完全纯净的图像先行RAG测试 - 无任何预设"""
    print("🚀 纯净图像先行RAG架构测试")
    print("=" * 80)
    print("特点：完全基于图像内容，无任何预设违规行为或场景描述")
    print()
    
    # 使用全局配置的测试图片路径
    image_path = TEST_IMAGE_PATH
    
    # 完全开放式的用户问题，不包含任何预设
    user_question = "请分析这张图片，如果有问题请告诉我具体情况和相关规定。"
    
    agent = create_traffic_rag_agent(enable_rag=True)
    
    print(f"📷 未知图像: {image_path}")
    print(f"❓ 开放式问题: {user_question}")
    print()
    print("🎯 开始完全未知的图像先行RAG分析...")
    print("=" * 60)
    
    try:
        # 让系统完全自主分析，不提供任何提示
        response = await agent.process_query(
            user_input=user_question,
            image_path=image_path
        )
        
        print("\n📋 系统完全自主分析结果:")
        print("-" * 60)
        if isinstance(response, dict):
            answer = response.get('answer', response)
            confidence = response.get('confidence', 'N/A')
            print(answer)
            print(f"\n🤖 系统置信度: {confidence}")
        else:
            print(response)
        
        print("\n✅ 纯净图像先行RAG架构验证完成!")
        print("\n📊 验证标准:")
        print("• ✓ 图像内容完全未知")
        print("• ✓ 用户问题完全开放")  
        print("• ✓ 系统自主进行图像分析")
        print("• ✓ 基于分析结果自动构建RAG查询")
        print("• ✓ 检索相关专业知识")
        print("• ✓ 给出基于实际内容的专业回复")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_architecture_components(main_image_path):
    """测试架构各组件的纯净性"""
    print("\n🔬 架构组件纯净性验证")
    print("=" * 80)
    
    agent = create_traffic_rag_agent(enable_rag=True)
    await agent._initialize_rag_system()
    
    # 测试1: 完全基于图像的场景分析（使用与主测试相同的图片）
    print("1. 🖼️ 图像预分析测试（无预设）:")
    image_path = main_image_path
    try:
        scene_info = await agent._analyze_image_scene(image_path)
        print(f"   系统分析结果: {scene_info}")
        if scene_info and scene_info != "一般交通场景: ":
            print("   ✓ 成功从图像提取具体场景信息")
        else:
            print("   ⚠️ 图像分析返回通用结果")
    except Exception as e:
        print(f"   ❌ 图像分析失败: {e}")
    
    # 测试2: 动态查询构建
    print("\n2. 🎯 动态查询构建测试:")
    open_question = "这张图有什么问题吗？"
    
    # 根据实际图像分析结果构建查询
    if scene_info and scene_info != "一般交通场景: ":
        enhanced_query = agent._build_enhanced_query(open_question, scene_info)
        print(f"   原始问题: {open_question}")
        print(f"   增强查询: {enhanced_query}")
        print("   ✓ 基于图像分析动态构建查询")
    else:
        print(f"   使用原始问题: {open_question}")
        print("   ⚠️ 未能基于图像信息增强查询")
    
    # 测试3: RAG检索效果
    print("\n3. 📚 RAG检索效果测试:")
    query_to_test = enhanced_query if 'enhanced_query' in locals() else open_question
    
    rag_result = await agent.test_rag_knowledge(query_to_test)
    print(f"   查询: {query_to_test}")
    print(f"   检索结果: {rag_result['knowledge_count']} 条")
    
    if rag_result['knowledge_results']:
        print("   检索到的知识:")
        for i, knowledge in enumerate(rag_result['knowledge_results'][:2], 1):
            content = knowledge.get('content', '')[:100]
            print(f"     {i}. {content}...")
        print("   ✓ 成功检索到相关专业知识")
    else:
        print("   ⚠️ 未检索到相关知识")
    
    print("\n💡 架构纯净性总结:")
    print("• 无预设场景描述")
    print("• 无预设违规行为")
    print("• 完全基于图像内容分析")
    print("• 动态构建RAG查询")
    print("• 自适应知识检索")

if __name__ == "__main__":
    # 运行纯净测试（使用全局配置的图片路径）
    asyncio.run(test_pure_image_first_rag())
    asyncio.run(test_architecture_components(TEST_IMAGE_PATH))