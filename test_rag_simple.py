#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试RAG增强的概念验证
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from src.rag.langchain_rag import LangChainRAGSystem
from src.agent.traffic_multimodal_agent import TrafficMultimodalAgent

async def test_rag_concept():
    """测试RAG增强概念"""
    print("🚀 测试RAG增强概念验证")
    print("=" * 50)
    
    try:
        # 1. 初始化RAG系统
        print("🧠 初始化RAG系统...")
        rag_system = LangChainRAGSystem()
        print("✅ RAG系统初始化成功")
        
        # 2. 测试知识检索
        print("\n🔍 测试知识检索...")
        query = "超速违法怎么处罚？"
        knowledge_results = rag_system.search_knowledge(query, k=2)
        
        print(f"查询: {query}")
        print(f"找到 {len(knowledge_results)} 条相关知识")
        
        if knowledge_results:
            for i, result in enumerate(knowledge_results, 1):
                print(f"\n知识 {i}:")
                print(f"  内容: {result['content'][:150]}...")
                if result['law']:
                    print(f"  法律: {result['law']}")
                if result['article']:
                    print(f"  条文: {result['article']}")
        
        # 3. 初始化Agent
        print(f"\n🤖 初始化Agent...")
        agent = TrafficMultimodalAgent()
        await agent.initialize()
        print("✅ Agent初始化成功")
        
        # 4. 测试RAG增强的查询处理
        print(f"\n🎯 测试RAG增强查询...")
        
        if knowledge_results:
            # 构建知识上下文
            knowledge_context = "专业知识参考:\n"
            for i, result in enumerate(knowledge_results, 1):
                knowledge_context += f"{i}. {result['content']}\n"
                if result['law']:
                    knowledge_context += f"   法律依据: {result['law']}\n"
                knowledge_context += "\n"
            
            # 构建增强查询
            enhanced_query = f"""基于以下专业知识回答问题：

{knowledge_context}

用户问题：{query}

请参考上述专业知识，给出准确的回答并引用相关法规。"""
            
            print(f"增强查询长度: {len(enhanced_query)} 字符")
            
            # 调用Agent处理
            print(f"\n🚀 Agent处理增强查询...")
            response = await agent.process_query(enhanced_query)
            
            print(f"\n📝 Agent回复:")
            print("=" * 50)
            print(response)
            print("=" * 50)
            
            print(f"\n✅ RAG增强概念验证成功!")
        else:
            print("❌ 未找到相关知识，无法进行RAG增强")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = asyncio.run(test_rag_concept())
    
    if success:
        print("\n🎉 RAG增强概念验证成功!")
        print("💡 证明了RAG作为知识增强器的可行性!")
    else:
        print("\n❌ 概念验证失败")
        sys.exit(1)

if __name__ == "__main__":
    main()