#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多模态Agent系统全功能综合测试

测试目标：验证TrafficMultimodalAgent的所有核心工具和功能
包括：
1. 图像分析工具 (TrafficImageAnalyzer)
2. 违规检测工具 (TrafficViolationDetector) 
3. 安全评估工具 (TrafficSafetyAssessor)
4. 建议生成工具 (TrafficSuggestionGenerator)

注意：法规检索功能已由RAG系统在Agent层面统一处理

测试方法：
- 单独测试每个工具的功能
- 测试工具组合协作效果
- 测试Agent的完整推理流程
- 验证多模态输入处理能力
"""

import asyncio
import sys
import json
import time
from pathlib import Path

project_root = Path(__file__).parent
sys.path.append(str(project_root))

from src.agent.traffic_multimodal_agent import TrafficMultimodalAgent
from src.agent.traffic_rag_agent import create_traffic_rag_agent
from src.tools.traffic_image_analyzer import TrafficImageAnalyzer
from src.tools.traffic_violation_detector import TrafficViolationDetector
# TrafficRegulationSearcher已被RAG系统替代，无需单独导入
from src.tools.traffic_safety_assessor import TrafficSafetyAssessor
from src.tools.traffic_suggestion_generator import TrafficSuggestionGenerator
from src.utils.logger import get_logger

logger = get_logger(__name__)

# 测试配置
TEST_IMAGE_PATH = "/home/<USER>/lhp/projects/0714agent/my-agent1/data/test_images/test3.png"

class MultimodalAgentTester:
    """多模态Agent综合测试器"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.test_results = {}
        
    async def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 多模态Agent系统全功能综合测试")
        print("=" * 80)
        
        # 阶段1：核心工具单独测试
        await self.test_individual_tools()
        
        # 阶段2：多模态Agent集成测试
        await self.test_multimodal_agent()
        
        # 阶段3：RAG增强Agent测试
        await self.test_rag_enhanced_agent()
        
        # 阶段4：完整业务流程测试
        await self.test_complete_workflow()
        
        # 阶段5：性能和可靠性测试
        await self.test_performance_reliability()
        
        # 生成测试报告
        self.generate_test_report()
    
    async def test_individual_tools(self):
        """测试各个工具的独立功能"""
        print("\n📋 阶段1：核心工具单独测试")
        print("=" * 60)
        
        # 1.1 图像分析工具测试
        await self.test_image_analyzer()
        
        # 1.2 违规检测工具测试
        await self.test_violation_detector()
        
        # 1.3 安全评估工具测试
        await self.test_safety_assessor()
        
        # 1.4 建议生成工具测试
        await self.test_suggestion_generator()
    
    async def test_image_analyzer(self):
        """测试图像分析工具"""
        print("\n🖼️ 1.1 图像分析工具测试")
        print("-" * 40)
        
        try:
            analyzer = TrafficImageAnalyzer()
            
            # 测试用例1：基础图像分析
            print("测试用例1：基础图像分析")
            result = await analyzer.analyze(
                image_input=TEST_IMAGE_PATH,
                analysis_focus=None,
                output_format="structured"
            )
            
            if result["success"]:
                analysis = result["analysis_result"]
                print(f"✅ 图像分析成功")
                print(f"   - 道路类型: {analysis.get('road_environment', {}).get('road_type', '未识别')}")
                print(f"   - 交通参与者: {len(analysis.get('traffic_participants', {}))}")
                print(f"   - 关键观察: {len(analysis.get('key_observations', []))}")
                print(f"   - 处理时间: {result['metadata']['processing_time']}秒")
                
                self.test_results["image_analyzer"] = {
                    "status": "success",
                    "processing_time": result['metadata']['processing_time'],
                    "features_detected": len(analysis.get('key_observations', [])),
                    "quality": "good"
                }
            else:
                print(f"❌ 图像分析失败: {result.get('error', '未知错误')}")
                self.test_results["image_analyzer"] = {"status": "failed", "error": result.get('error')}
            
            # 测试用例2：特定焦点分析
            print("\n测试用例2：违规行为焦点分析")
            result2 = await analyzer.analyze(
                image_input=TEST_IMAGE_PATH,
                analysis_focus=["violations", "safety"],
                output_format="structured"
            )
            
            if result2["success"]:
                print(f"✅ 焦点分析成功，发现关键观察: {len(result2['analysis_result'].get('key_observations', []))}")
            else:
                print(f"❌ 焦点分析失败")
                
        except Exception as e:
            print(f"❌ 图像分析工具测试异常: {e}")
            self.test_results["image_analyzer"] = {"status": "error", "error": str(e)}
    
    async def test_violation_detector(self):
        """测试违规检测工具"""
        print("\n⚠️ 1.2 违规检测工具测试")
        print("-" * 40)
        
        try:
            detector = TrafficViolationDetector()
            
            # 模拟场景数据
            test_scene = {
                "traffic_participants": {
                    "motor_vehicles": {
                        "count": 2,
                        "types": ["小轿车", "SUV"],
                        "behaviors": ["正常行驶", "停在人行横道"]
                    },
                    "pedestrians": {
                        "count": 1,
                        "locations": ["人行横道"],
                        "behaviors": ["准备过马路"]
                    }
                },
                "road_environment": {
                    "road_type": "城市道路",
                    "lane_config": "双向四车道"
                },
                "traffic_facilities": {
                    "signals": "红绿灯正常",
                    "crosswalk": "人行横道标线清晰"
                },
                "key_observations": [
                    "机动车占用人行横道",
                    "行人等待通过"
                ]
            }
            
            print("测试用例1：人行横道违规检测")
            result = await detector.detect(
                scene_analysis=test_scene,
                detection_focus=["crosswalk_violations"],
                confidence_threshold=0.6
            )
            
            if result["success"]:
                violations = result["violations_detected"]
                print(f"✅ 检测成功，发现违规行为: {len(violations)}个")
                
                for i, violation in enumerate(violations, 1):
                    print(f"   {i}. {violation['name']} (严重度: {violation['severity']}, 置信度: {violation['confidence']:.2f})")
                
                print(f"   - 整体风险等级: {result['risk_level']}")
                
                self.test_results["violation_detector"] = {
                    "status": "success",
                    "violations_found": len(violations),
                    "risk_level": result['risk_level'],
                    "accuracy": "high"
                }
            else:
                print(f"❌ 违规检测失败: {result.get('error', '未知错误')}")
                self.test_results["violation_detector"] = {"status": "failed", "error": result.get('error')}
                
        except Exception as e:
            print(f"❌ 违规检测工具测试异常: {e}")
            self.test_results["violation_detector"] = {"status": "error", "error": str(e)}
    
    
    async def test_safety_assessor(self):
        """测试安全评估工具"""
        print("\n🛡️ 1.3 安全评估工具测试")
        print("-" * 40)
        
        try:
            assessor = TrafficSafetyAssessor()
            
            # 模拟场景和违规数据
            test_scene = {
                "road_environment": {"road_type": "城市道路", "visibility": "良好"},
                "traffic_participants": {
                    "motor_vehicles": {"count": 3, "behaviors": ["正常行驶", "违规停车"]},
                    "pedestrians": {"count": 2, "behaviors": ["正常通行"]}
                }
            }
            
            test_violations = {
                "violations_detected": [
                    {
                        "name": "占用人行横道",
                        "severity": "medium",
                        "confidence": 0.85,
                        "safety_impact": "high"
                    }
                ],
                "total_violations": 1,
                "risk_level": "medium"
            }
            
            print("测试用例1：综合安全评估")
            result = await assessor.assess(
                scene_analysis=test_scene,
                violation_results=test_violations,
                assessment_options={"include_prediction": True}
            )
            
            if result["success"]:
                assessment = result["safety_assessment"]
                print(f"✅ 安全评估成功")
                print(f"   - 总体安全分数: {assessment['overall_score']:.1f}/100")
                print(f"   - 安全等级: {assessment['safety_level']}级")
                print(f"   - 主要风险: {len(result.get('major_risks', []))}个")
                print(f"   - 改进紧急程度: {assessment.get('improvement_urgency', '未评估')}")
                
                self.test_results["safety_assessor"] = {
                    "status": "success",
                    "safety_score": assessment['overall_score'],
                    "safety_level": assessment['safety_level'],
                    "assessment_accuracy": "high"
                }
            else:
                print(f"❌ 安全评估失败: {result.get('error', '未知错误')}")
                self.test_results["safety_assessor"] = {"status": "failed", "error": result.get('error')}
                
        except Exception as e:
            print(f"❌ 安全评估工具测试异常: {e}")
            self.test_results["safety_assessor"] = {"status": "error", "error": str(e)}
    
    async def test_suggestion_generator(self):
        """测试建议生成工具"""
        print("\n💡 1.4 建议生成工具测试")
        print("-" * 40)
        
        try:
            generator = TrafficSuggestionGenerator()
            
            # 模拟综合分析数据
            test_scene = {"road_environment": {"road_type": "城市道路"}}
            test_violations = {
                "violations_detected": [
                    {"name": "占用人行横道", "severity": "medium"}
                ]
            }
            test_safety = {
                "safety_assessment": {
                    "overall_score": 65.0,
                    "safety_level": "C",
                    "major_risks": ["行人安全风险"]
                }
            }
            
            print("测试用例1：综合改进建议生成")
            result = await generator.generate(
                scene_analysis=test_scene,
                violation_results=test_violations,
                safety_assessment=test_safety,
                generation_options={"focus": "immediate_improvements"}
            )
            
            if result["success"]:
                suggestions = result["suggestions"]
                total_suggestions = result.get("total_suggestions", 0)
                
                print(f"✅ 建议生成成功，共生成建议: {total_suggestions}条")
                
                for category, items in suggestions.items():
                    if items:
                        print(f"   - {category}: {len(items)}条建议")
                        if items:
                            print(f"     示例: {items[0]['title']}")
                
                priority_summary = result.get("priority_summary", {})
                if priority_summary:
                    urgent_count = priority_summary.get("urgent", 0)
                    high_count = priority_summary.get("high", 0)
                    print(f"   - 紧急建议: {urgent_count}条, 高优先级: {high_count}条")
                
                self.test_results["suggestion_generator"] = {
                    "status": "success",
                    "total_suggestions": total_suggestions,
                    "categories_covered": len([k for k, v in suggestions.items() if v]),
                    "quality": "comprehensive"
                }
            else:
                print(f"❌ 建议生成失败: {result.get('error', '未知错误')}")
                self.test_results["suggestion_generator"] = {"status": "failed", "error": result.get('error')}
                
        except Exception as e:
            print(f"❌ 建议生成工具测试异常: {e}")
            self.test_results["suggestion_generator"] = {"status": "error", "error": str(e)}
    
    async def test_multimodal_agent(self):
        """测试多模态Agent集成功能"""
        print("\n📋 阶段2：多模态Agent集成测试")
        print("=" * 60)
        
        try:
            agent = TrafficMultimodalAgent()
            await agent.initialize()
            
            # 测试用例1：图像+文本查询
            print("\n🔍 2.1 多模态输入处理测试")
            test_query = "请分析这张图片，识别是否存在交通违规行为，并提供安全评估和改进建议。"
            
            print(f"查询: {test_query}")
            print(f"图像: {TEST_IMAGE_PATH}")
            
            result = await agent.process_query(
                query=test_query,
                image_path=TEST_IMAGE_PATH
            )
            
            if result["success"]:
                print(f"✅ 多模态分析成功")
                print(f"   - 使用的工具: {', '.join(result.get('tools_used', []))}")
                print(f"   - 推理步骤: {len(result.get('reasoning_steps', []))}")
                print(f"   - 置信度: {result.get('confidence', 0):.2f}")
                print(f"   - 回答长度: {len(result.get('answer', ''))}")
                
                # 验证工具调用
                tools_used = result.get('tools_used', [])
                expected_tools = ["detect_traffic_violations", "assess_safety_risk", "generate_improvement_suggestions"]
                tools_coverage = len([tool for tool in expected_tools if tool in tools_used])
                
                print(f"\n📊 工具调用分析:")
                print(f"   - 预期工具调用: {len(expected_tools)}")
                print(f"   - 实际工具调用: {len(tools_used)}")
                print(f"   - 工具覆盖率: {tools_coverage/len(expected_tools)*100:.1f}%")
                
                self.test_results["multimodal_agent"] = {
                    "status": "success",
                    "tools_used": len(tools_used),
                    "tools_coverage": tools_coverage/len(expected_tools),
                    "reasoning_steps": len(result.get('reasoning_steps', [])),
                    "confidence": result.get('confidence', 0),
                    "response_quality": "comprehensive" if len(result.get('answer', '')) > 500 else "basic"
                }
            else:
                print(f"❌ 多模态分析失败: {result.get('error', '未知错误')}")
                self.test_results["multimodal_agent"] = {"status": "failed", "error": result.get('error')}
            
            # 测试用例2：仅文本查询
            print("\n🔍 2.2 纯文本查询测试")
            text_query = "请告诉我机动车占用人行横道的违规情况和安全风险。"
            
            result2 = await agent.process_query(query=text_query)
            
            if result2["success"]:
                print(f"✅ 文本查询成功")
                print(f"   - 回答包含关键信息: {'违规' in result2['answer'] and '安全' in result2['answer']}")
            else:
                print(f"❌ 文本查询失败")
                
        except Exception as e:
            print(f"❌ 多模态Agent测试异常: {e}")
            self.test_results["multimodal_agent"] = {"status": "error", "error": str(e)}
    
    async def test_rag_enhanced_agent(self):
        """测试RAG增强Agent功能"""
        print("\n📋 阶段3：RAG增强Agent测试")
        print("=" * 60)
        
        try:
            # 测试图像先行RAG架构
            print("\n🚀 3.1 图像先行RAG架构测试")
            
            rag_agent = create_traffic_rag_agent(enable_rag=True)
            
            test_query = "请分析这张图片，如果有问题请告诉我具体情况和相关规定。"
            
            result = await rag_agent.process_query(
                user_input=test_query,
                image_path=TEST_IMAGE_PATH
            )
            
            print(f"✅ RAG增强分析完成")
            print(f"   - 回答长度: {len(result) if isinstance(result, str) else 'N/A'}")
            print(f"   - 包含法规引用: {'法' in result and '条' in result if isinstance(result, str) else False}")
            print(f"   - 包含专业术语: {'违规' in result or '安全' in result if isinstance(result, str) else False}")
            
            # 测试RAG状态
            print("\n🔍 3.2 RAG系统状态检查")
            rag_status = rag_agent.get_rag_status()
            
            print(f"   - RAG启用状态: {rag_status.get('enabled', False)}")
            print(f"   - RAG系统状态: {rag_status.get('status', 'unknown')}")
            
            if rag_status.get('collection_info'):
                collection_info = rag_status['collection_info']
                print(f"   - 知识库文档数: {collection_info.get('document_count', 0)}")
                print(f"   - 嵌入模型: {collection_info.get('embedding_model', 'unknown')}")
            
            # 测试知识检索功能
            print("\n🔍 3.3 知识检索功能测试")
            test_queries = [
                "占用人行横道违规",
                "未礼让行人处罚",
                "交通信号灯违规"
            ]
            
            total_retrieved = 0
            for query in test_queries:
                rag_result = await rag_agent.test_rag_knowledge(query)
                knowledge_count = rag_result.get('knowledge_count', 0)
                total_retrieved += knowledge_count
                print(f"   - \"{query}\": 检索到{knowledge_count}条知识")
            
            avg_retrieval = total_retrieved / len(test_queries)
            print(f"   - 平均检索效果: {avg_retrieval:.1f}条/查询")
            
            self.test_results["rag_enhanced_agent"] = {
                "status": "success",
                "rag_enabled": rag_status.get('enabled', False),
                "knowledge_base_size": rag_status.get('collection_info', {}).get('document_count', 0),
                "avg_retrieval": avg_retrieval,
                "knowledge_integration": "effective" if avg_retrieval > 1 else "limited"
            }
                
        except Exception as e:
            print(f"❌ RAG增强Agent测试异常: {e}")
            self.test_results["rag_enhanced_agent"] = {"status": "error", "error": str(e)}
    
    async def test_complete_workflow(self):
        """测试完整业务流程"""
        print("\n📋 阶段4：完整业务流程测试")
        print("=" * 60)
        
        try:
            print("\n🔄 4.1 端到端完整流程测试")
            print("-" * 40)
            
            # 创建RAG增强Agent（最完整的功能）
            rag_agent = create_traffic_rag_agent(enable_rag=True)
            
            # 模拟真实用户场景
            real_scenarios = [
                {
                    "name": "日常交通咨询",
                    "query": "请帮我分析这张交通图片，看看有什么问题需要注意的。",
                    "expected_tools": ["analyze_traffic_image", "detect_traffic_violations", "assess_safety_risk"],
                    "expected_content": ["违规", "安全", "建议"]
                },
                {
                    "name": "专业安全评估",
                    "query": "我需要对这个路口进行专业的安全评估，请提供详细的分析报告和改进建议。",
                    "expected_tools": ["analyze_traffic_image", "detect_traffic_violations", "assess_safety_risk", "generate_improvement_suggestions"],
                    "expected_content": ["安全等级", "风险", "改进", "建议"]
                }
            ]
            
            workflow_results = []
            
            for i, scenario in enumerate(real_scenarios, 1):
                print(f"\n📋 测试场景{i}: {scenario['name']}")
                print(f"   用户查询: {scenario['query']}")
                
                # 执行完整流程
                start_time = time.time()
                result = await rag_agent.process_query(
                    user_input=scenario['query'],
                    image_path=TEST_IMAGE_PATH
                )
                end_time = time.time()
                
                processing_time = end_time - start_time
                
                if isinstance(result, str):
                    # RAG Agent返回字符串结果
                    response_content = result
                    success = len(response_content) > 100  # 基本长度检查
                    
                    # 检查是否包含预期内容
                    content_coverage = sum(1 for content in scenario['expected_content'] 
                                         if content in response_content) / len(scenario['expected_content'])
                    
                    print(f"   ✅ 流程完成，响应时间: {processing_time:.2f}秒")
                    print(f"   📊 回答长度: {len(response_content)}字符")
                    print(f"   🎯 内容覆盖率: {content_coverage*100:.1f}%")
                    
                    # 检查专业性指标
                    has_legal_reference = any(keyword in response_content for keyword in ['法', '条', '规定', '处罚'])
                    has_safety_assessment = any(keyword in response_content for keyword in ['安全', '风险', '等级'])
                    has_suggestions = any(keyword in response_content for keyword in ['建议', '改进', '措施'])
                    
                    print(f"   ⚖️ 法规引用: {'✅' if has_legal_reference else '❌'}")
                    print(f"   🛡️ 安全评估: {'✅' if has_safety_assessment else '❌'}")
                    print(f"   💡 改进建议: {'✅' if has_suggestions else '❌'}")
                    
                    workflow_results.append({
                        "scenario": scenario['name'],
                        "success": success,
                        "processing_time": processing_time,
                        "content_coverage": content_coverage,
                        "professional_quality": {
                            "legal_reference": has_legal_reference,
                            "safety_assessment": has_safety_assessment,
                            "suggestions": has_suggestions
                        }
                    })
                else:
                    print(f"   ❌ 流程失败，返回格式异常")
                    workflow_results.append({
                        "scenario": scenario['name'],
                        "success": False,
                        "error": "返回格式异常"
                    })
            
            # 4.2 工具协作流程验证
            print(f"\n🔄 4.2 工具协作流程验证")
            print("-" * 40)
            
            # 使用基础多模态Agent测试工具协作
            base_agent = TrafficMultimodalAgent()
            await base_agent.initialize()
            
            test_query = "请使用所有工具对这张图片进行全面分析"
            
            result = await base_agent.process_query(
                query=test_query,
                image_path=TEST_IMAGE_PATH
            )
            
            if result.get("success"):
                tools_used = result.get('tools_used', [])
                reasoning_steps = result.get('reasoning_steps', [])
                
                print(f"   ✅ 工具协作成功")
                print(f"   🔧 调用工具数: {len(tools_used)}")
                print(f"   🧠 推理步骤: {len(reasoning_steps)}")
                print(f"   🎯 置信度: {result.get('confidence', 0):.2f}")
                
                # 验证工具调用完整性
                expected_tools = ["detect_traffic_violations", "assess_safety_risk", "generate_improvement_suggestions"]
                tool_coverage = len([tool for tool in expected_tools if tool in tools_used]) / len(expected_tools)
                
                print(f"   📊 工具覆盖率: {tool_coverage*100:.1f}%")
                
                workflow_results.append({
                    "scenario": "工具协作验证",
                    "success": True,
                    "tools_coverage": tool_coverage,
                    "reasoning_quality": len(reasoning_steps) >= 3
                })
            else:
                print(f"   ❌ 工具协作失败: {result.get('error', '未知错误')}")
                workflow_results.append({
                    "scenario": "工具协作验证",
                    "success": False,
                    "error": result.get('error')
                })
            
            # 4.3 业务价值验证
            print(f"\n🎯 4.3 业务价值验证")
            print("-" * 40)
            
            # 统计整体流程表现
            successful_workflows = len([r for r in workflow_results if r.get('success', False)])
            total_workflows = len(workflow_results)
            success_rate = successful_workflows / total_workflows * 100 if total_workflows > 0 else 0
            
            avg_processing_time = sum([r.get('processing_time', 0) for r in workflow_results if r.get('processing_time')]) / max(1, len([r for r in workflow_results if r.get('processing_time')]))
            avg_content_coverage = sum([r.get('content_coverage', 0) for r in workflow_results if r.get('content_coverage')]) / max(1, len([r for r in workflow_results if r.get('content_coverage')]))
            
            print(f"   📊 流程成功率: {success_rate:.1f}%")
            print(f"   ⏱️ 平均处理时间: {avg_processing_time:.2f}秒")
            print(f"   🎯 平均内容覆盖率: {avg_content_coverage*100:.1f}%")
            
            # 专业质量评估
            quality_metrics = {
                "legal_reference": 0,
                "safety_assessment": 0,
                "suggestions": 0
            }
            
            for result in workflow_results:
                if result.get('professional_quality'):
                    for metric, value in result['professional_quality'].items():
                        if value:
                            quality_metrics[metric] += 1
            
            total_quality_tests = len([r for r in workflow_results if r.get('professional_quality')])
            if total_quality_tests > 0:
                print(f"   ⚖️ 法规引用率: {quality_metrics['legal_reference']/total_quality_tests*100:.1f}%")
                print(f"   🛡️ 安全评估率: {quality_metrics['safety_assessment']/total_quality_tests*100:.1f}%")
                print(f"   💡 建议生成率: {quality_metrics['suggestions']/total_quality_tests*100:.1f}%")
            
            # 保存完整流程测试结果
            self.test_results["complete_workflow"] = {
                "status": "success",
                "workflow_success_rate": success_rate,
                "avg_processing_time": avg_processing_time,
                "avg_content_coverage": avg_content_coverage,
                "professional_quality": quality_metrics,
                "business_value": "high" if success_rate >= 80 and avg_content_coverage >= 0.7 else "medium" if success_rate >= 60 else "low"
            }
            
            print(f"\n💼 业务价值评估: {self.test_results['complete_workflow']['business_value'].upper()}")
            
        except Exception as e:
            print(f"❌ 完整业务流程测试异常: {e}")
            self.test_results["complete_workflow"] = {"status": "error", "error": str(e)}
    
    async def test_performance_reliability(self):
        """测试性能和可靠性"""
        print("\n📋 阶段5：性能和可靠性测试")
        print("=" * 60)
        
        try:
            # 5.1 响应时间测试
            print("\n⏱️ 5.1 响应时间测试")
            
            agent = TrafficMultimodalAgent()
            await agent.initialize()
            
            response_times = []
            for i in range(3):
                start_time = time.time()
                
                result = await agent.process_query(
                    query="这是一个简单的测试查询",
                    image_path=TEST_IMAGE_PATH
                )
                
                end_time = time.time()
                response_time = end_time - start_time
                response_times.append(response_time)
                
                print(f"   测试{i+1}: {response_time:.2f}秒")
            
            avg_response_time = sum(response_times) / len(response_times)
            print(f"   平均响应时间: {avg_response_time:.2f}秒")
            
            # 5.2 错误处理测试
            print("\n🛡️ 5.2 错误处理测试")
            
            # 测试无效图像路径
            result_invalid = await agent.process_query(
                query="测试查询",
                image_path="/invalid/path/image.jpg"
            )
            
            handles_errors = not result_invalid.get("success", True)
            print(f"   无效图像处理: {'✅ 正确处理' if handles_errors else '❌ 未正确处理'}")
            
            # 测试空查询
            result_empty = await agent.process_query(query="")
            handles_empty = result_empty.get("success", False)
            print(f"   空查询处理: {'✅ 正确处理' if handles_empty else '❌ 未正确处理'}")
            
            self.test_results["performance_reliability"] = {
                "avg_response_time": avg_response_time,
                "response_time_stability": max(response_times) - min(response_times) < 2.0,
                "error_handling": handles_errors and handles_empty,
                "performance_level": "excellent" if avg_response_time < 5 else "good" if avg_response_time < 10 else "needs_improvement"
            }
            
        except Exception as e:
            print(f"❌ 性能可靠性测试异常: {e}")
            self.test_results["performance_reliability"] = {"status": "error", "error": str(e)}
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n📋 综合测试报告")
        print("=" * 80)
        
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results.values() if r.get("status") == "success"])
        success_rate = successful_tests / total_tests * 100 if total_tests > 0 else 0
        
        print(f"\n📊 总体测试结果:")
        print(f"   - 测试项目: {total_tests}")
        print(f"   - 成功项目: {successful_tests}")
        print(f"   - 成功率: {success_rate:.1f}%")
        print(f"   - 整体评级: {self._get_overall_grade(success_rate)}")
        
        print(f"\n🔍 详细测试结果:")
        for test_name, result in self.test_results.items():
            status = result.get("status", "unknown")
            status_icon = "✅" if status == "success" else "❌" if status == "failed" else "⚠️"
            print(f"   {status_icon} {test_name}: {status}")
            
            # 显示关键指标
            if status == "success":
                if test_name == "image_analyzer":
                    print(f"      处理时间: {result.get('processing_time', 'N/A')}秒")
                elif test_name == "violation_detector":
                    print(f"      风险等级: {result.get('risk_level', 'N/A')}")
                elif test_name == "multimodal_agent":
                    print(f"      工具覆盖率: {result.get('tools_coverage', 0)*100:.1f}%")
                    print(f"      置信度: {result.get('confidence', 0):.2f}")
                elif test_name == "rag_enhanced_agent":
                    print(f"      知识库规模: {result.get('knowledge_base_size', 0)}文档")
                    print(f"      检索效果: {result.get('avg_retrieval', 0):.1f}条/查询")
                elif test_name == "complete_workflow":
                    print(f"      流程成功率: {result.get('workflow_success_rate', 0):.1f}%")
                    print(f"      业务价值: {result.get('business_value', 'unknown')}")
                elif test_name == "performance_reliability":
                    print(f"      平均响应时间: {result.get('avg_response_time', 0):.2f}秒")
                    print(f"      性能等级: {result.get('performance_level', 'unknown')}")
        
        # 理想效果对比
        print(f"\n🎯 理想效果对比:")
        ideal_expectations = self._get_ideal_expectations()
        for component, expectation in ideal_expectations.items():
            actual_result = self.test_results.get(component, {})
            meets_expectation = self._evaluate_expectation(component, actual_result, expectation)
            status_icon = "✅" if meets_expectation else "📋"
            print(f"   {status_icon} {component}:")
            print(f"      理想效果: {expectation['description']}")
            print(f"      实际表现: {expectation['actual_assessment']}")
        
        # 保存测试结果
        self._save_test_results()
    
    def _get_overall_grade(self, success_rate):
        """获取整体评级"""
        if success_rate >= 90:
            return "优秀 (A)"
        elif success_rate >= 80:
            return "良好 (B)"
        elif success_rate >= 70:
            return "及格 (C)"
        elif success_rate >= 60:
            return "需改进 (D)"
        else:
            return "不及格 (F)"
    
    def _get_ideal_expectations(self):
        """获取理想效果期望"""
        return {
            "image_analyzer": {
                "description": "2秒内完成图像分析，准确识别道路环境、交通参与者和关键观察点",
                "actual_assessment": "基本达到预期" if self.test_results.get("image_analyzer", {}).get("status") == "success" else "未达到预期"
            },
            "violation_detector": {
                "description": "准确检测各类交通违规，提供法规依据和风险等级评估",
                "actual_assessment": "检测功能正常" if self.test_results.get("violation_detector", {}).get("status") == "success" else "检测功能异常"
            },
            "safety_assessor": {
                "description": "综合评估交通安全风险，提供A-E级安全等级和改进建议",
                "actual_assessment": "安全评估功能正常" if self.test_results.get("safety_assessor", {}).get("status") == "success" else "安全评估异常"
            },
            "suggestion_generator": {
                "description": "基于分析结果生成分类改进建议，提供可操作的解决方案",
                "actual_assessment": "建议生成功能正常" if self.test_results.get("suggestion_generator", {}).get("status") == "success" else "建议生成异常"
            },
            "multimodal_agent": {
                "description": "智能协调4个专业工具，提供全面的交通场景分析和专业建议",
                "actual_assessment": f"工具协调{self.test_results.get('multimodal_agent', {}).get('tools_coverage', 0)*100:.0f}%覆盖率" if self.test_results.get("multimodal_agent", {}).get("status") == "success" else "协调功能异常"
            },
            "rag_enhanced_agent": {
                "description": "图像先行RAG架构，结合专业知识库提供权威法规引用",
                "actual_assessment": f"知识检索{self.test_results.get('rag_enhanced_agent', {}).get('avg_retrieval', 0):.1f}条/查询" if self.test_results.get("rag_enhanced_agent", {}).get("status") == "success" else "RAG功能异常"
            },
            "complete_workflow": {
                "description": "端到端完整业务流程，从图像输入到专业分析报告生成",
                "actual_assessment": f"流程成功率{self.test_results.get('complete_workflow', {}).get('workflow_success_rate', 0):.0f}%" if self.test_results.get("complete_workflow", {}).get("status") == "success" else "业务流程异常"
            },
            "performance_reliability": {
                "description": "10秒内响应，稳定的错误处理和高可靠性",
                "actual_assessment": f"{self.test_results.get('performance_reliability', {}).get('performance_level', 'unknown')}性能表现" if self.test_results.get("performance_reliability", {}).get("status") == "success" else "性能测试异常"
            }
        }
    
    def _evaluate_expectation(self, component, actual_result, expectation):
        """评估是否达到期望"""
        if actual_result.get("status") != "success":
            return False
        
        if component == "image_analyzer":
            return actual_result.get("processing_time", 10) < 3
        elif component == "violation_detector":
            return actual_result.get("violations_found", 0) >= 0
        elif component == "safety_assessor":
            return actual_result.get("safety_score", 0) > 60
        elif component == "suggestion_generator":
            return actual_result.get("total_suggestions", 0) > 3
        elif component == "multimodal_agent":
            return actual_result.get("tools_coverage", 0) > 0.7
        elif component == "rag_enhanced_agent":
            return actual_result.get("avg_retrieval", 0) > 1
        elif component == "complete_workflow":
            return actual_result.get("workflow_success_rate", 0) > 70
        elif component == "performance_reliability":
            return actual_result.get("avg_response_time", 10) < 10
        
        return True
    
    def _save_test_results(self):
        """保存测试结果到文件"""
        try:
            results_file = project_root / "test_results_comprehensive.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, ensure_ascii=False, indent=2)
            print(f"\n💾 测试结果已保存到: {results_file}")
        except Exception as e:
            print(f"⚠️ 保存测试结果失败: {e}")

async def main():
    """主测试函数"""
    tester = MultimodalAgentTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    print("启动多模态Agent系统全功能综合测试...")
    asyncio.run(main())