#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入交通知识库数据到RAG系统
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.rag.langchain_rag import LangChainRAGSystem
from data.knowledge.traffic_regulations import get_all_documents

def main():
    """导入知识库数据"""
    print("🚀 开始导入交通知识库数据")
    print("=" * 60)
    
    try:
        # 1. 创建RAG系统
        print("📦 创建RAG系统...")
        rag_system = LangChainRAGSystem(
            model_name="BAAI/bge-large-zh-v1.5",
            persist_directory="./data/langchain_vectors",
            collection_name="traffic_knowledge"
        )
        
        # 2. 获取知识库数据
        print("📚 加载知识库数据...")
        documents = get_all_documents()
        print(f"加载到 {len(documents)} 个文档")
        
        # 3. 导入到向量数据库
        print("🔄 导入到向量数据库...")
        success = rag_system.add_documents(documents)
        
        if success:
            print("✅ 知识库导入成功!")
            
            # 4. 验证导入结果
            print("\n📊 验证导入结果...")
            collection_info = rag_system.get_collection_info()
            print(f"集合状态: {collection_info['status']}")
            print(f"文档数量: {collection_info['document_count']}")
            
            # 5. 测试检索功能
            print("\n🔍 测试检索功能...")
            test_queries = [
                "超速违法怎么处罚？",
                "机动车如何礼让行人？",
                "哪些地方禁止停车？"
            ]
            
            for query in test_queries:
                results = rag_system.search_knowledge(query, k=2)
                print(f"\n查询: {query}")
                print(f"找到 {len(results)} 条相关知识")
                if results:
                    for i, result in enumerate(results, 1):
                        content = result['content'][:100] + "..."
                        print(f"  {i}. {content}")
            
            print("\n🎉 知识库构建完成!")
            return True
            
        else:
            print("❌ 知识库导入失败!")
            return False
            
    except Exception as e:
        print(f"❌ 导入过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)