# 测试验证流程 - 智能交通多模态RAG助手

## 🎯 测试目标

构建完整的测试验证体系，确保系统的：
- **功能完整性**：所有功能模块正常工作
- **性能达标**：响应时间、准确率等指标符合要求
- **稳定可靠**：在各种条件下稳定运行
- **用户友好**：界面交互体验良好

## 🧪 测试体系架构

### 测试层级结构
```
测试验证体系
├── 单元测试 (Unit Tests)
│   ├── LangChain Agent核心逻辑
│   ├── RAG检索功能
│   ├── 多模态处理
│   └── 工具函数
├── 集成测试 (Integration Tests)
│   ├── Agent与RAG系统集成
│   ├── 多模态模型集成
│   ├── Web界面与后端集成
│   └── 外部服务集成(Ollama)
├── 系统测试 (System Tests)
│   ├── 端到端功能测试
│   ├── 性能压力测试
│   ├── 错误处理测试
│   └── 用户场景测试
└── 验收测试 (Acceptance Tests)
    ├── 业务场景验证
    ├── 用户体验测试
    └── 部署验证测试
```

## 🔬 单元测试

### 1. Agent核心功能测试
```python
import pytest
import asyncio
from unittest.mock import Mock, patch
from src.agent.traffic_multimodal_agent import TrafficMultimodalAgent
from src.tools.image_analyzer import TrafficImageAnalyzer
from src.tools.violation_detector import TrafficViolationDetector

class TestTrafficMultimodalAgent:
    """交通多模态Agent单元测试"""
    
    @pytest.fixture
    def agent(self):
        """创建测试用的Agent实例"""
        return TrafficMultimodalAgent()
    
    @pytest.fixture
    def sample_image_analysis(self):
        """示例图像分析结果"""
        return {
            "road_type": "城市道路",
            "participants": {
                "motor_vehicles": 3,
                "non_motor_vehicles": 2,
                "pedestrians": 1
            },
            "environment": {
                "weather": "晴天",
                "lighting": "白天",
                "road_surface": "干燥"
            },
            "key_observations": [
                "有电动车在机动车道行驶",
                "人行横道处有行人通过"
            ]
        }
    
    def test_agent_initialization(self, agent):
        """测试Agent初始化"""
        assert agent is not None
        assert agent.llm is not None
        assert agent.tools is not None
        assert agent.memory is not None
        assert agent.agent_executor is not None
    
    def test_tools_initialization(self, agent):
        """测试工具初始化"""
        tool_names = [tool.name for tool in agent.tools]
        
        expected_tools = [
            "analyze_traffic_image",
            "detect_traffic_violations", 
            "search_traffic_regulations",
            "assess_safety_risk",
            "generate_improvement_suggestions"
        ]
        
        for expected_tool in expected_tools:
            assert expected_tool in tool_names
    
    @pytest.mark.asyncio
    async def test_process_text_query(self, agent):
        """测试纯文本查询处理"""
        query = "电动车可以在机动车道行驶吗？"
        
        with patch.object(agent.agent_executor, 'ainvoke') as mock_invoke:
            mock_invoke.return_value = {
                "output": "根据《道路交通安全法》第57条规定，非机动车应当在非机动车道内行驶。电动车属于非机动车，不得在机动车道行驶。",
                "intermediate_steps": []
            }
            
            result = await agent.process_query(query)
            
            assert result["success"] is True
            assert "第57条" in result["answer"]
            assert "非机动车道" in result["answer"]
    
    @pytest.mark.asyncio
    async def test_process_image_query(self, agent, sample_image_analysis):
        """测试图像查询处理"""
        query = "分析这个路口的交通安全问题"
        image_path = "test_images/intersection.jpg"
        
        with patch.object(agent.agent_executor, 'ainvoke') as mock_invoke:
            mock_invoke.return_value = {
                "output": "检测到电动车占用机动车道违规行为，违反《道路交通安全法》第57条规定。",
                "intermediate_steps": []
            }
            
            result = await agent.process_query(query, image_path=image_path)
            
            assert result["success"] is True
            assert "违规行为" in result["answer"]
    
    def test_extract_reasoning_steps(self, agent):
        """测试推理步骤提取"""
        mock_result = {
            "intermediate_steps": [
                ("analyze_traffic_image", "图像分析完成"),
                ("detect_traffic_violations", "检测到1个违规行为"),
                ("search_traffic_regulations", "找到相关法规条文")
            ]
        }
        
        steps = agent._extract_reasoning_steps(mock_result)
        
        assert len(steps) == 3
        assert steps[0]["tool"] == "analyze_traffic_image"
        assert steps[1]["tool"] == "detect_traffic_violations"
        assert steps[2]["tool"] == "search_traffic_regulations"

class TestTrafficImageAnalyzer:
    """交通图像分析器测试"""
    
    @pytest.fixture
    def analyzer(self):
        """创建测试用的图像分析器"""
        mock_ollama_client = Mock()
        return TrafficImageAnalyzer(mock_ollama_client)
    
    @pytest.mark.asyncio
    async def test_analyze_valid_image(self, analyzer):
        """测试有效图像分析"""
        image_input = "test_images/traffic_scene.jpg"
        
        mock_response = """
        ## 道路环境分析
        - 道路类型: 城市道路
        - 车道配置: 双向四车道
        - 路面状况: 干燥
        - 环境条件: 白天晴天
        
        ## 交通参与者识别
        - 机动车: 3辆轿车
        - 非机动车: 2辆电动车
        - 行人: 1名行人在人行横道
        """
        
        analyzer.ollama_client.generate.return_value = mock_response
        
        result = await analyzer.analyze(image_input)
        
        assert result is not None
        assert result["road_type"] == "城市道路"
        assert result["participants"]["motor_vehicles"] == 3
    
    def test_parse_analysis_result(self, analyzer):
        """测试分析结果解析"""
        raw_result = """
        ## 道路环境分析
        - 道路类型: 高速公路
        - 车道配置: 三车道
        
        ## 交通参与者识别
        - 机动车: 5辆车
        """
        
        parsed = analyzer._parse_analysis_result(raw_result)
        
        assert parsed["road_type"] == "高速公路"
        assert "三车道" in parsed["lane_config"]

class TestTrafficViolationDetector:
    """交通违规检测器测试"""
    
    @pytest.fixture
    def detector(self):
        """创建测试用的违规检测器"""
        mock_regulation_db = Mock()
        return TrafficViolationDetector(mock_regulation_db)
    
    @pytest.mark.asyncio
    async def test_detect_lane_violation(self, detector):
        """测试车道违规检测"""
        image_analysis = {
            "participants": {
                "non_motor_vehicles": 1,
                "motor_vehicles": 2
            },
            "key_observations": [
                "电动车在机动车道行驶",
                "占用最右侧机动车道"
            ]
        }
        
        violations = await detector.detect(image_analysis)
        
        assert len(violations) > 0
        lane_violations = [v for v in violations if v["type"] == "lane_violation"]
        assert len(lane_violations) > 0
        assert "第57条" in lane_violations[0]["legal_reference"]
    
    def test_load_violation_patterns(self, detector):
        """测试违规模式加载"""
        patterns = detector._load_violation_patterns()
        
        assert "lane_violation" in patterns
        assert "pedestrian_violation" in patterns
        assert "signal_violation" in patterns
        assert "parking_violation" in patterns
        
        # 验证模式结构
        lane_pattern = patterns["lane_violation"]
        assert "description" in lane_pattern
        assert "indicators" in lane_pattern
        assert "legal_reference" in lane_pattern
        assert "severity" in lane_pattern

# 运行单元测试
if __name__ == "__main__":
    pytest.main(["-v", "tests/unit/"])
```

### 2. RAG系统测试
```python
class TestTrafficKnowledgeBase:
    """交通知识库测试"""
    
    @pytest.fixture
    def knowledge_base(self, tmp_path):
        """创建测试用的知识库"""
        return TrafficKnowledgeBase(persist_directory=str(tmp_path))
    
    def test_initialize_collections(self, knowledge_base):
        """测试集合初始化"""
        collections = knowledge_base.collections
        
        expected_collections = [
            "traffic_laws",
            "technical_standards", 
            "case_studies",
            "real_time_updates"
        ]
        
        for expected in expected_collections:
            assert expected in collections
    
    def test_add_traffic_law(self, knowledge_base):
        """测试添加交通法规"""
        law_doc = TrafficLawDocument()
        law_doc.law_id = "test_001"
        law_doc.content = "测试法规内容"
        law_doc.article_number = "第1条"
        
        knowledge_base.add_traffic_law(law_doc)
        
        # 验证添加成功
        results = knowledge_base.search_relevant_laws("测试法规")
        assert len(results) > 0
        assert "测试法规内容" in results[0]["content"]
    
    def test_search_relevant_laws(self, knowledge_base):
        """测试法规搜索"""
        # 添加测试数据
        law_doc = TrafficLawDocument()
        law_doc.content = "非机动车应当在非机动车道内行驶"
        law_doc.category = "lane_violation"
        
        knowledge_base.add_traffic_law(law_doc)
        
        # 测试搜索
        results = knowledge_base.search_relevant_laws("电动车车道", top_k=5)
        
        assert len(results) > 0
        assert any("非机动车道" in result["content"] for result in results)
    
    def test_category_filter(self, knowledge_base):
        """测试分类过滤"""
        # 添加不同类别的测试数据
        law_doc1 = TrafficLawDocument()
        law_doc1.content = "机动车违规停车处罚规定"
        law_doc1.category = "parking_violation"
        
        law_doc2 = TrafficLawDocument()
        law_doc2.content = "闯红灯违规行为处罚"
        law_doc2.category = "signal_violation"
        
        knowledge_base.add_traffic_law(law_doc1)
        knowledge_base.add_traffic_law(law_doc2)
        
        # 测试分类过滤
        parking_results = knowledge_base.search_relevant_laws(
            "违规", category_filter="parking_violation"
        )
        
        assert all(result["metadata"]["category"] == "parking_violation" 
                  for result in parking_results)

class TestAdvancedTrafficRetriever:
    """高级检索器测试"""
    
    @pytest.fixture
    def retriever(self):
        """创建测试用的检索器"""
        mock_kb = Mock()
        return AdvancedTrafficRetriever(mock_kb)
    
    @pytest.mark.asyncio
    async def test_retrieve_knowledge(self, retriever):
        """测试知识检索"""
        query = "电动车占用机动车道"
        query_type = "violation_query"
        
        # Mock查询处理器
        mock_processed_query = Mock()
        mock_processed_query.main_query = query
        mock_processed_query.expanded_queries = ["非机动车道使用规定"]
        mock_processed_query.detected_categories = ["lane_violation"]
        
        retriever.query_processor.process_query = Mock(return_value=mock_processed_query)
        
        # Mock检索结果
        mock_results = [
            {"content": "非机动车应在非机动车道行驶", "score": 0.9},
            {"content": "违反车道使用规定的处罚", "score": 0.8}
        ]
        
        retriever._initial_retrieval = Mock(return_value=mock_results)
        retriever._rerank_results = Mock(return_value=mock_results)
        retriever._post_process_results = Mock(return_value=mock_results)
        
        results = await retriever.retrieve_knowledge(query, query_type)
        
        assert len(results) > 0
        assert all("score" in result for result in results)
    
    def test_deduplicate_results(self, retriever):
        """测试结果去重"""
        duplicate_results = [
            {"content": "相同内容", "id": "1"},
            {"content": "相同内容", "id": "2"},
            {"content": "不同内容", "id": "3"}
        ]
        
        unique_results = retriever._deduplicate_results(duplicate_results)
        
        assert len(unique_results) == 2
        contents = [result["content"] for result in unique_results]
        assert contents.count("相同内容") == 1
        assert "不同内容" in contents
```

## 🔗 集成测试

### 1. Agent-RAG集成测试
```python
class TestAgentRAGIntegration:
    """Agent与RAG系统集成测试"""
    
    @pytest.fixture
    async def integrated_system(self):
        """创建完整集成系统"""
        # 初始化真实的组件
        agent = TrafficMultimodalAgent()
        knowledge_base = TrafficKnowledgeBase()
        
        # 加载测试数据
        await self.load_test_knowledge_base(knowledge_base)
        
        return {
            "agent": agent,
            "knowledge_base": knowledge_base
        }
    
    async def load_test_knowledge_base(self, kb):
        """加载测试知识库数据"""
        test_laws = [
            {
                "content": "机动车行经人行横道时，应当减速行驶；遇行人正在通过人行横道，应当停车让行。",
                "law_name": "道路交通安全法",
                "article_number": "第47条",
                "category": "pedestrian_safety"
            },
            {
                "content": "非机动车应当在非机动车道内行驶；在没有非机动车道的道路上，应当靠车行道的右侧行驶。",
                "law_name": "道路交通安全法", 
                "article_number": "第57条",
                "category": "lane_violation"
            }
        ]
        
        for law_data in test_laws:
            law_doc = TrafficLawDocument()
            law_doc.__dict__.update(law_data)
            kb.add_traffic_law(law_doc)
    
    @pytest.mark.asyncio
    async def test_end_to_end_query(self, integrated_system):
        """测试端到端查询处理"""
        agent = integrated_system["agent"]
        
        query = "行人正在人行横道通过时，机动车应该怎么做？"
        
        result = await agent.process_query(query)
        
        assert result["success"] is True
        assert "停车让行" in result["answer"]
        assert "第47条" in result["answer"]
        
        # 验证推理步骤
        reasoning_steps = result["reasoning_steps"]
        tool_names = [step["tool"] for step in reasoning_steps]
        assert "search_traffic_regulations" in tool_names
    
    @pytest.mark.asyncio
    async def test_multimodal_integration(self, integrated_system):
        """测试多模态集成"""
        agent = integrated_system["agent"]
        
        query = "分析这个路口是否存在违规行为"
        image_path = "test_images/crosswalk_scene.jpg"
        
        # Mock图像分析结果
        with patch('src.tools.image_analyzer.TrafficImageAnalyzer.analyze') as mock_analyze:
            mock_analyze.return_value = {
                "participants": {"pedestrians": 1, "motor_vehicles": 2},
                "key_observations": ["行人在人行横道通过", "机动车未停车让行"]
            }
            
            result = await agent.process_query(query, image_path=image_path)
            
            assert result["success"] is True
            assert "人行横道" in result["answer"]
            
            # 验证多个工具被调用
            reasoning_steps = result["reasoning_steps"]
            tool_names = [step["tool"] for step in reasoning_steps]
            assert "analyze_traffic_image" in tool_names
            assert "detect_traffic_violations" in tool_names
```

### 2. Web界面集成测试
```python
import streamlit as st
from streamlit.testing import StreamlitTestSession

class TestWebInterfaceIntegration:
    """Web界面集成测试"""
    
    def test_main_page_load(self):
        """测试主页面加载"""
        with StreamlitTestSession() as session:
            session.run("src/web/app.py")
            
            # 验证页面元素
            assert "智能交通多模态RAG助手" in session.get_text()
            assert "图像上传" in session.get_text()
            assert "开始分析" in session.get_text()
    
    def test_file_upload_functionality(self):
        """测试文件上传功能"""
        with StreamlitTestSession() as session:
            session.run("src/web/app.py")
            
            # 模拟文件上传
            test_image = create_test_image()
            session.upload_file("uploaded_image", test_image)
            
            # 验证图像预览显示
            assert "图像预览" in session.get_text()
            assert "图像质量" in session.get_text()
    
    def test_analysis_button_interaction(self):
        """测试分析按钮交互"""
        with StreamlitTestSession() as session:
            session.run("src/web/app.py")
            
            # 输入查询文本
            session.set_text_input("query_text", "这是一个测试查询")
            
            # 点击分析按钮
            session.click_button("开始分析")
            
            # 验证分析开始
            assert "处理中" in session.get_text() or "分析结果" in session.get_text()

def create_test_image():
    """创建测试图像"""
    from PIL import Image
    import io
    
    # 创建简单的测试图像
    img = Image.new('RGB', (800, 600), color='blue')
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    
    return img_bytes
```

## 🚀 系统测试

### 1. 性能压力测试
```python
import asyncio
import time
import statistics
from concurrent.futures import ThreadPoolExecutor

class TestSystemPerformance:
    """系统性能测试"""
    
    @pytest.mark.asyncio
    async def test_response_time_single_query(self):
        """测试单查询响应时间"""
        agent = TrafficMultimodalAgent()
        
        query = "电动车在机动车道行驶是否违法？"
        
        start_time = time.time()
        result = await agent.process_query(query)
        end_time = time.time()
        
        response_time = end_time - start_time
        
        # 验证响应时间在3秒内
        assert response_time < 3.0
        assert result["success"] is True
    
    @pytest.mark.asyncio
    async def test_concurrent_queries_performance(self):
        """测试并发查询性能"""
        agent = TrafficMultimodalAgent()
        
        queries = [
            "行人横穿马路是否违法？",
            "机动车如何正确礼让行人？",
            "什么情况下可以变更车道？",
            "红灯时右转是否允许？",
            "电动车需要遵守哪些交通规则？"
        ]
        
        # 并发执行查询
        start_time = time.time()
        tasks = [agent.process_query(query) for query in queries]
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        total_time = end_time - start_time
        
        # 验证所有查询都成功
        assert all(result["success"] for result in results)
        
        # 验证平均响应时间
        avg_time_per_query = total_time / len(queries)
        assert avg_time_per_query < 5.0  # 并发情况下允许更长时间
    
    @pytest.mark.asyncio
    async def test_memory_usage_stability(self):
        """测试内存使用稳定性"""
        import psutil
        import gc
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        agent = TrafficMultimodalAgent()
        
        # 执行多次查询
        for i in range(10):
            query = f"测试查询 #{i+1}: 这是一个长查询" * 10
            result = await agent.process_query(query)
            
            # 强制垃圾回收
            gc.collect()
        
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # 验证内存增长控制在合理范围内（100MB）
        assert memory_increase < 100 * 1024 * 1024
    
    def test_image_processing_performance(self):
        """测试图像处理性能"""
        analyzer = TrafficImageAnalyzer(Mock())
        
        # 创建不同尺寸的测试图像
        test_sizes = [(800, 600), (1920, 1080), (4000, 3000)]
        processing_times = []
        
        for width, height in test_sizes:
            test_image = create_test_image_with_size(width, height)
            
            start_time = time.time()
            # 模拟图像预处理
            processed_image = analyzer._preprocess_image(test_image)
            end_time = time.time()
            
            processing_time = end_time - start_time
            processing_times.append(processing_time)
            
            # 验证处理时间合理
            assert processing_time < 5.0
        
        # 验证处理时间随图像尺寸的增长是合理的
        assert processing_times[0] < processing_times[1] < processing_times[2]

def create_test_image_with_size(width, height):
    """创建指定尺寸的测试图像"""
    from PIL import Image
    return Image.new('RGB', (width, height), color='red')
```

### 2. 错误处理测试
```python
class TestErrorHandling:
    """错误处理测试"""
    
    @pytest.mark.asyncio
    async def test_ollama_service_unavailable(self):
        """测试Ollama服务不可用的情况"""
        agent = TrafficMultimodalAgent()
        
        # Mock Ollama服务异常
        with patch.object(agent.llm, '_agenerate') as mock_generate:
            mock_generate.side_effect = Exception("Ollama service unavailable")
            
            query = "测试查询"
            result = await agent.process_query(query)
            
            assert result["success"] is False
            assert "error" in result
            assert "Ollama" in result["error"] or "服务" in result["error"]
    
    @pytest.mark.asyncio
    async def test_invalid_image_input(self):
        """测试无效图像输入"""
        agent = TrafficMultimodalAgent()
        
        query = "分析这个图像"
        invalid_image_path = "nonexistent_image.jpg"
        
        result = await agent.process_query(query, image_path=invalid_image_path)
        
        assert result["success"] is False
        assert "图像" in result["error"] or "文件" in result["error"]
    
    @pytest.mark.asyncio
    async def test_empty_query_input(self):
        """测试空查询输入"""
        agent = TrafficMultimodalAgent()
        
        result = await agent.process_query("")
        
        # 系统应该能处理空查询并给出合适提示
        assert result["success"] is False
        assert "查询" in result["error"] or "输入" in result["error"]
    
    @pytest.mark.asyncio
    async def test_knowledge_base_unavailable(self):
        """测试知识库不可用"""
        agent = TrafficMultimodalAgent()
        
        # Mock知识库连接异常
        with patch('src.rag.traffic_knowledge_base.TrafficKnowledgeBase') as mock_kb:
            mock_kb.side_effect = Exception("Knowledge base connection failed")
            
            query = "相关法规查询"
            result = await agent.process_query(query)
            
            # 系统应该优雅处理知识库异常
            assert "error" in result
            assert result["success"] is False
    
    @pytest.mark.asyncio
    async def test_agent_timeout_handling(self):
        """测试Agent超时处理"""
        agent = TrafficMultimodalAgent()
        
        # Mock长时间运行的操作
        with patch.object(agent.agent_executor, 'ainvoke') as mock_invoke:
            mock_invoke.side_effect = asyncio.TimeoutError("Operation timed out")
            
            query = "复杂查询"
            result = await agent.process_query(query)
            
            assert result["success"] is False
            assert "超时" in result["error"] or "timeout" in result["error"].lower()
```

### 3. 边界条件测试
```python
class TestBoundaryConditions:
    """边界条件测试"""
    
    @pytest.mark.asyncio
    async def test_extremely_long_query(self):
        """测试极长查询"""
        agent = TrafficMultimodalAgent()
        
        # 创建极长查询（超过通常限制）
        long_query = "这是一个非常长的查询" * 1000
        
        result = await agent.process_query(long_query)
        
        # 系统应该能处理或优雅地拒绝极长查询
        assert "error" in result or result["success"] is True
    
    @pytest.mark.asyncio
    async def test_special_characters_in_query(self):
        """测试查询中的特殊字符"""
        agent = TrafficMultimodalAgent()
        
        special_queries = [
            "查询包含特殊字符: @#$%^&*()",
            "包含Unicode字符: 🚗🚦🚸",
            "包含HTML标签: <script>alert('test')</script>",
            "包含SQL注入: '; DROP TABLE laws; --"
        ]
        
        for query in special_queries:
            result = await agent.process_query(query)
            
            # 验证系统安全处理特殊字符
            assert "script" not in result["answer"].lower()
            assert "DROP" not in result["answer"]
    
    def test_large_image_handling(self):
        """测试大图像处理"""
        analyzer = TrafficImageAnalyzer(Mock())
        
        # 创建大尺寸图像（10MB+）
        large_image = create_large_test_image()
        
        # 验证系统能处理大图像或给出合适提示
        try:
            result = analyzer._preprocess_image(large_image)
            # 如果处理成功，验证结果
            assert result is not None
        except Exception as e:
            # 如果拒绝处理，验证错误信息合理
            assert "大小" in str(e) or "尺寸" in str(e)
    
    @pytest.mark.asyncio
    async def test_rapid_sequential_queries(self):
        """测试快速连续查询"""
        agent = TrafficMultimodalAgent()
        
        queries = ["查询1", "查询2", "查询3", "查询4", "查询5"]
        
        # 快速连续发送查询
        results = []
        for query in queries:
            result = await agent.process_query(query)
            results.append(result)
        
        # 验证所有查询都得到了处理
        assert len(results) == len(queries)
        
        # 验证没有查询被错误地混合
        for i, result in enumerate(results):
            if result["success"]:
                assert f"查询{i+1}" in str(result) or len(result["answer"]) > 0

def create_large_test_image():
    """创建大尺寸测试图像"""
    from PIL import Image
    import io
    
    # 创建5000x5000的大图像
    img = Image.new('RGB', (5000, 5000), color='green')
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG', quality=95)
    img_bytes.seek(0)
    
    return img_bytes
```

## ✅ 验收测试

### 1. 业务场景验证
```python
class TestBusinessScenarios:
    """业务场景验证测试"""
    
    @pytest.mark.asyncio
    async def test_traffic_violation_detection_scenario(self):
        """测试交通违规检测场景"""
        agent = TrafficMultimodalAgent()
        
        # 场景：电动车占道
        scenario_query = "图片中的电动车行为是否违规？应该如何处罚？"
        scenario_image = "test_images/electric_bike_in_motor_lane.jpg"
        
        result = await agent.process_query(scenario_query, image_path=scenario_image)
        
        # 验证业务需求
        assert result["success"] is True
        assert "违规" in result["answer"]
        assert "第57条" in result["answer"]  # 应该引用正确法规
        assert "处罚" in result["answer"] or "罚款" in result["answer"]
        
        # 验证推理过程
        tools_used = result.get("tools_used", [])
        assert "analyze_traffic_image" in tools_used
        assert "detect_traffic_violations" in tools_used
        assert "search_traffic_regulations" in tools_used
    
    @pytest.mark.asyncio
    async def test_traffic_safety_assessment_scenario(self):
        """测试交通安全评估场景"""
        agent = TrafficMultimodalAgent()
        
        # 场景：路口安全评估
        scenario_query = "评估这个路口的安全状况，给出改进建议"
        scenario_image = "test_images/busy_intersection.jpg"
        
        result = await agent.process_query(scenario_query, image_path=scenario_image)
        
        # 验证安全评估功能
        assert result["success"] is True
        assert "安全" in result["answer"]
        assert "建议" in result["answer"]
        
        # 应该包含风险等级评估
        assert any(grade in result["answer"] for grade in ["A", "B", "C", "D", "E"])
        
        # 应该使用安全评估工具
        tools_used = result.get("tools_used", [])
        assert "assess_safety_risk" in tools_used
        assert "generate_improvement_suggestions" in tools_used
    
    @pytest.mark.asyncio
    async def test_regulation_consultation_scenario(self):
        """测试法规咨询场景"""
        agent = TrafficMultimodalAgent()
        
        # 场景：法规咨询
        queries = [
            "非机动车在什么情况下可以进入机动车道？",
            "机动车礼让行人的具体规定是什么？",
            "违反交通信号灯有什么处罚？"
        ]
        
        for query in queries:
            result = await agent.process_query(query)
            
            # 验证法规咨询功能
            assert result["success"] is True
            
            # 应该包含法规引用
            assert any(article in result["answer"] 
                      for article in ["条", "法规", "规定"])
            
            # 应该使用法规检索工具
            tools_used = result.get("tools_used", [])
            assert "search_traffic_regulations" in tools_used
```

### 2. 用户体验测试
```python
class TestUserExperience:
    """用户体验测试"""
    
    def test_response_clarity_and_professionalism(self):
        """测试回答清晰度和专业性"""
        agent = TrafficMultimodalAgent()
        
        test_cases = [
            {
                "query": "电动车闯红灯怎么处罚？",
                "expected_elements": ["法规", "处罚", "条文"],
                "forbidden_elements": ["可能", "大概", "估计"]
            },
            {
                "query": "行人横穿马路被撞谁的责任？",
                "expected_elements": ["责任", "法规依据", "具体情况"],
                "forbidden_elements": ["都有错", "各打五十大板"]
            }
        ]
        
        for case in test_cases:
            result = asyncio.run(agent.process_query(case["query"]))
            
            answer = result["answer"].lower()
            
            # 验证必要元素
            for element in case["expected_elements"]:
                assert element in answer
            
            # 验证不应出现的模糊表述
            for forbidden in case["forbidden_elements"]:
                assert forbidden not in answer
    
    def test_multilingual_query_handling(self):
        """测试多语言查询处理"""
        agent = TrafficMultimodalAgent()
        
        # 英文查询测试
        english_query = "What are the traffic rules for electric bikes?"
        result = asyncio.run(agent.process_query(english_query))
        
        # 系统应该能理解并用中文回答
        assert result["success"] is True
        assert len(result["answer"]) > 0
    
    def test_response_completeness(self):
        """测试回答完整性"""
        agent = TrafficMultimodalAgent()
        
        query = "电动车违规行为分析"
        result = asyncio.run(agent.process_query(query))
        
        if result["success"]:
            answer = result["answer"]
            
            # 验证回答结构完整性
            expected_sections = ["违规行为", "法规依据", "处罚标准"]
            section_found = sum(1 for section in expected_sections 
                               if section in answer)
            
            # 至少应该包含大部分预期内容
            assert section_found >= 2
```

### 3. 部署验证测试
```python
class TestDeploymentValidation:
    """部署验证测试"""
    
    def test_docker_container_startup(self):
        """测试Docker容器启动"""
        import subprocess
        
        # 构建Docker镜像
        build_result = subprocess.run([
            "docker", "build", "-t", "traffic-rag-assistant", "."
        ], capture_output=True, text=True)
        
        assert build_result.returncode == 0
        
        # 启动容器
        run_result = subprocess.run([
            "docker", "run", "-d", "-p", "8501:8501", 
            "--name", "traffic-test", "traffic-rag-assistant"
        ], capture_output=True, text=True)
        
        assert run_result.returncode == 0
        
        # 等待服务启动
        import time
        time.sleep(10)
        
        # 测试服务可访问性
        import requests
        try:
            response = requests.get("http://localhost:8501", timeout=10)
            assert response.status_code == 200
        finally:
            # 清理容器
            subprocess.run(["docker", "stop", "traffic-test"], 
                          capture_output=True)
            subprocess.run(["docker", "rm", "traffic-test"], 
                          capture_output=True)
    
    def test_service_health_check(self):
        """测试服务健康检查"""
        import requests
        
        # 假设服务已经运行在localhost:8501
        health_endpoints = [
            "/health",
            "/_stcore/health", 
            "/"
        ]
        
        for endpoint in health_endpoints:
            try:
                response = requests.get(f"http://localhost:8501{endpoint}", 
                                      timeout=5)
                if response.status_code == 200:
                    assert True
                    return
            except requests.RequestException:
                continue
        
        # 如果没有健康检查端点响应，至少主页应该可访问
        try:
            response = requests.get("http://localhost:8501", timeout=10)
            assert response.status_code == 200
        except requests.RequestException:
            pytest.fail("Service is not accessible")
    
    def test_environment_compatibility(self):
        """测试环境兼容性"""
        import platform
        import sys
        
        # 验证Python版本
        assert sys.version_info >= (3, 8)
        
        # 验证关键依赖
        required_packages = [
            "streamlit", "langchain", "chromadb", 
            "pillow", "requests", "asyncio"
        ]
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                pytest.fail(f"Required package {package} is not installed")
        
        # 验证操作系统兼容性
        supported_platforms = ["Linux", "Darwin", "Windows"]
        assert platform.system() in supported_platforms
```

## 📊 测试执行和报告

### 测试执行脚本
```bash
#!/bin/bash
# run_tests.sh - 完整测试执行脚本

echo "🧪 开始执行智能交通RAG助手测试套件"

# 1. 环境检查
echo "📋 检查测试环境..."
python -c "import pytest, streamlit, langchain, chromadb; print('✅ 依赖检查通过')"

# 2. 单元测试
echo "🔬 执行单元测试..."
pytest tests/unit/ -v --cov=src --cov-report=html --cov-report=term

# 3. 集成测试  
echo "🔗 执行集成测试..."
pytest tests/integration/ -v

# 4. 系统测试
echo "🚀 执行系统测试..."
pytest tests/system/ -v --timeout=300

# 5. 验收测试
echo "✅ 执行验收测试..."
pytest tests/acceptance/ -v

# 6. 性能基准测试
echo "📊 执行性能基准测试..."
python tests/benchmarks/performance_benchmark.py

# 7. 生成测试报告
echo "📝 生成测试报告..."
python -m pytest --html=reports/test_report.html --self-contained-html

echo "🎉 测试执行完成！查看报告: reports/test_report.html"
```

### 测试指标监控
```python
class TestMetrics:
    """测试指标收集和监控"""
    
    def __init__(self):
        self.metrics = {
            "test_coverage": 0.0,
            "pass_rate": 0.0,
            "avg_response_time": 0.0,
            "error_rate": 0.0,
            "performance_score": 0.0
        }
    
    def collect_test_metrics(self, test_results):
        """收集测试指标"""
        total_tests = len(test_results)
        passed_tests = sum(1 for result in test_results if result.passed)
        
        self.metrics["pass_rate"] = passed_tests / total_tests if total_tests > 0 else 0
        
        # 收集响应时间数据
        response_times = [result.duration for result in test_results 
                         if hasattr(result, 'duration')]
        
        if response_times:
            self.metrics["avg_response_time"] = sum(response_times) / len(response_times)
        
        return self.metrics
    
    def generate_metrics_report(self):
        """生成指标报告"""
        report = f"""
        📊 测试指标报告
        ================
        
        🎯 测试通过率: {self.metrics['pass_rate']:.2%}
        ⚡ 平均响应时间: {self.metrics['avg_response_time']:.2f}秒
        🔍 代码覆盖率: {self.metrics['test_coverage']:.2%}
        ❌ 错误率: {self.metrics['error_rate']:.2%}
        ⭐ 综合性能分: {self.metrics['performance_score']:.1f}/10.0
        """
        
        return report
```

## 🎯 质量门禁标准

### 发布标准
```yaml
release_criteria:
  unit_tests:
    pass_rate: ">= 95%"
    coverage: ">= 80%"
  
  integration_tests:
    pass_rate: ">= 90%"
    max_response_time: "<= 3s"
  
  system_tests:
    availability: ">= 99%"
    error_rate: "<= 1%"
    performance_degradation: "<= 10%"
  
  acceptance_tests:
    user_scenarios: "100% pass"
    business_requirements: "100% covered"
```

### 持续监控
- **自动化测试**：每次代码提交触发测试套件
- **性能监控**：持续监控响应时间和资源使用
- **错误跟踪**：实时收集和分析错误日志
- **用户反馈**：收集真实用户使用反馈

---

**测试验证体系确保系统质量可靠，功能完备，性能达标，用户体验良好。**