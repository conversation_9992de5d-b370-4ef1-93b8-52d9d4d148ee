# -*- coding: utf-8 -*-
"""
系统配置文件
"""
import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.absolute()

# Ollama配置
OLLAMA_CONFIG = {
    "base_url": "http://localhost:11434",
    "model_name": "qwen2.5vl:7b",
    "timeout": 60,
    "temperature": 0.1,
    "max_tokens": 512
}

# ChromaDB配置
CHROMA_CONFIG = {
    "persist_directory": PROJECT_ROOT / "data" / "knowledge_base",
    "collection_name": "traffic_knowledge",
    "embedding_model": "BAAI/bge-large-zh-v1.5"
}

# RAG系统配置
RAG_CONFIG = {
    "model_name": "BAAI/bge-large-zh-v1.5",
    "persist_directory": "./data/langchain_vectors",  # 保持相对路径，因为代码中就是这样使用的
    "collection_name": "traffic_knowledge",
    "search_k": 3,
    "similarity_threshold": 0.3
}

# Web界面配置
STREAMLIT_CONFIG = {
    "page_title": "智能交通多模态RAG助手",
    "page_icon": "🚗",
    "layout": "wide",
    "initial_sidebar_state": "expanded"
}

# 日志配置
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {
            "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
        }
    },
    "handlers": {
        "default": {
            "level": "INFO",
            "formatter": "standard",
            "class": "logging.StreamHandler"
        },
        "file": {
            "level": "DEBUG",
            "formatter": "standard",
            "class": "logging.FileHandler",
            "filename": PROJECT_ROOT / "logs" / "app.log",
            "mode": "a"
        }
    },
    "loggers": {
        "": {
            "handlers": ["default", "file"],
            "level": "DEBUG",
            "propagate": False
        }
    }
}

# Agent配置
AGENT_CONFIG = {
    "max_iterations": 8,
    "early_stopping_method": "force", 
    "verbose": True,
    "handle_parsing_errors": "Check your output and make sure to conform!",
    "memory_key": "chat_history",
    "memory_window": 10
}

# 交通违规检测配置
VIOLATION_PATTERNS = {
    "lane_violation": {
        "description": "非机动车占用机动车道",
        "keywords": ["电动车", "自行车", "机动车道", "占用"],
        "legal_reference": "《道路交通安全法》第57条",
        "severity": "medium"
    },
    "pedestrian_violation": {
        "description": "机动车未礼让行人",
        "keywords": ["人行横道", "行人", "机动车", "未让行"],
        "legal_reference": "《道路交通安全法》第47条",
        "severity": "high"
    },
    "signal_violation": {
        "description": "违反交通信号",
        "keywords": ["红灯", "闯红灯", "信号灯"],
        "legal_reference": "《道路交通安全法》第38条",
        "severity": "high"
    },
    "parking_violation": {
        "description": "违法停车",
        "keywords": ["停车", "禁停", "人行道", "黄线"],
        "legal_reference": "《道路交通安全法》第56条",
        "severity": "medium"
    }
}

# ========== 法规检索配置 ==========
REGULATION_SEARCH_CONFIG = {
    "max_results": 10,
    "similarity_threshold": 0.7,
    "rerank_enabled": True
}

# ========== 安全评估配置 ==========
SAFETY_ASSESSMENT_CONFIG = {
    "score_weights": {
        "environmental": 0.25,
        "infrastructure": 0.20,
        "behavioral": 0.30,
        "traffic_flow": 0.15,
        "equipment": 0.10
    },
    "risk_thresholds": {
        "high_risk": 60,
        "medium_risk": 70,
        "low_risk": 80
    }
}

# ========== 建议生成配置 ==========
SUGGESTION_CONFIG = {
    "max_per_category": 3,
    "min_feasibility": 0.3,
    "priority_weights": {
        "safety_impact": 0.4,
        "implementation_ease": 0.3,
        "cost_effectiveness": 0.2,
        "urgency": 0.1
    }
}