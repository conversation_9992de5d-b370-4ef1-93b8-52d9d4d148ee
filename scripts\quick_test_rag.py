#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试RAG系统基本功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

def test_rag_basic():
    """快速测试RAG基本功能"""
    print("🚀 快速测试RAG系统")
    print("=" * 50)
    
    try:
        # 1. 导入RAG系统
        print("📦 导入RAG系统...")
        from src.rag.langchain_rag import LangChainRAGSystem
        
        # 2. 创建RAG实例
        print("🧠 创建RAG实例...")
        rag = LangChainRAGSystem()
        
        # 3. 检查集合信息
        print("📊 检查集合信息...")
        info = rag.get_collection_info()
        print(f"状态: {info.get('status', '未知')}")
        print(f"文档数量: {info.get('document_count', 0)}")
        
        # 4. 测试检索功能
        if info.get('document_count', 0) > 0:
            print("\n🔍 测试检索功能...")
            test_query = "超速违法怎么处罚？"
            results = rag.search_knowledge(test_query, k=2)
            
            print(f"查询: {test_query}")
            print(f"找到: {len(results)} 条结果")
            
            for i, result in enumerate(results, 1):
                content = result['content'][:100] + "..."
                print(f"  {i}. {content}")
        else:
            print("⚠️ 知识库为空，需要先导入数据")
            
            # 尝试导入数据
            print("\n📚 尝试导入知识库数据...")
            from data.knowledge.traffic_regulations import get_all_documents
            
            docs = get_all_documents()
            print(f"准备导入 {len(docs)} 个文档...")
            
            success = rag.add_documents(docs)
            if success:
                print("✅ 数据导入成功!")
                
                # 再次测试检索
                print("\n🔍 重新测试检索...")
                results = rag.search_knowledge("超速违法怎么处罚？", k=2)
                print(f"找到 {len(results)} 条结果")
            else:
                print("❌ 数据导入失败")
        
        print("\n✅ RAG系统测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_rag_basic()
    sys.exit(0 if success else 1)