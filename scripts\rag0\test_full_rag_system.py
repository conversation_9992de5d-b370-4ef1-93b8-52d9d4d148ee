#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整RAG系统测试：导入知识 + 测试检索
"""

import sys
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from data.knowledge.traffic_regulations import ALL_KNOWLEDGE_DATA
from src.rag.vector_store import TrafficVectorStore
from src.rag.embeddings import BGEEmbeddings
from src.rag.retrieval import TrafficKnowledgeRetriever

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_full_rag_system():
    """完整RAG系统测试"""
    print("🚀 完整RAG系统测试")
    print("=" * 60)
    
    try:
        # 1. 初始化所有组件
        print("📦 初始化RAG系统组件...")
        vector_store = TrafficVectorStore()
        embeddings = BGEEmbeddings()  # 使用BGE中文模型
        retriever = TrafficKnowledgeRetriever(vector_store, embeddings)
        print("✅ 组件初始化成功!")
        
        # 2. 检查当前数据库状态
        print(f"\n📊 检查向量数据库状态...")
        initial_info = vector_store.get_collection_info()
        total_docs = initial_info.get("summary", {}).get("total_documents", 0)
        print(f"当前数据库中有 {total_docs} 个文档")
        
        # 3. 如果数据库为空，导入知识数据
        if total_docs == 0:
            print(f"\n📚 导入交通知识数据...")
            
            total_imported = 0
            for collection_name, documents in ALL_KNOWLEDGE_DATA.items():
                print(f"\n正在导入集合: {collection_name} ({len(documents)} 个文档)")
                
                result = vector_store.add_documents(
                    collection_name=collection_name,
                    documents=documents,
                    batch_size=10
                )
                
                if result["success"]:
                    print(f"✅ 导入成功: {result['success_count']}/{result['total_documents']}")
                    total_imported += result['success_count']
                else:
                    print(f"❌ 导入失败: {result.get('errors', [])}")
            
            print(f"\n📈 总计导入: {total_imported} 个文档")
        else:
            print(f"✅ 数据库已有数据，跳过导入")
        
        # 4. 验证导入结果
        print(f"\n🔍 验证数据库状态...")
        final_info = vector_store.get_collection_info()
        print("最终集合状态:")
        for name, info in final_info.items():
            if name != "summary":
                print(f"   - {name}: {info['document_count']} 个文档")
            else:
                print(f"   - 总结: {info}")
        
        # 5. 测试智能检索功能
        print(f"\n🧪 测试智能检索功能...")
        test_queries = [
            "超速违法的处罚标准是什么？",
            "酒后驾驶会有什么后果？",
            "雨天驾驶需要注意什么？",
            "在人行横道前应该怎么做？",
            "机动车载物有什么要求？",
            "新手驾驶员有什么特殊规定？"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n--- 测试 {i}: {query} ---")
            
            result = await retriever.retrieve(query, top_k=3, enable_rerank=True)
            
            if result["success"]:
                print(f"✅ 检索成功!")
                print(f"   - 找到结果: {result['returned_count']}/{result['total_found']}")
                print(f"   - 检索耗时: {result['processing_time']:.3f}秒")
                print(f"   - 搜索集合: {result['collections_searched']}")
                print(f"   - 查询分析: {result['query_info']}")
                
                # 显示前2个最相关的结果
                for j, doc in enumerate(result["results"][:2], 1):
                    similarity = doc.get('similarity_score', doc.get('rerank_score', 0))
                    print(f"   结果 {j} (相似度: {similarity:.3f}):")
                    print(f"      来源: {doc.get('source_collection', 'unknown')}")
                    print(f"      内容: {doc['content'][:150]}...")
                    
            else:
                print(f"❌ 检索失败: {result.get('error', 'Unknown error')}")
        
        # 6. 显示系统统计信息
        print(f"\n📈 系统统计信息:")
        rag_stats = retriever.get_retrieval_stats()
        print(f"   - 总查询数: {rag_stats['total_queries']}")
        print(f"   - 成功查询数: {rag_stats['successful_queries']}")
        print(f"   - 平均响应时间: {rag_stats['avg_response_time']:.3f}秒")
        print(f"   - 缓存命中率: {rag_stats['cache_hit_rate']:.2%}")
        print(f"   - 成功率: {rag_stats['success_rate']:.2%}")
        
        # 7. 性能基准测试
        print(f"\n⚡ 嵌入模型性能测试...")
        benchmark = embeddings.benchmark_performance(num_samples=10)
        if "error" not in benchmark:
            print(f"   - 批量处理: {benchmark['batch_embedding']['docs_per_second']:.1f} docs/sec")
            print(f"   - 单次查询: {benchmark['single_query']['queries_per_second']:.1f} queries/sec")
        
        print(f"\n🎉 完整RAG系统测试成功!")
        print(f"💡 系统已准备就绪，可以进行Agent集成!")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整RAG系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_full_rag_system())
    if not success:
        sys.exit(1)