#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试RAG增强的交通多模态Agent
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.agent.traffic_multimodal_agent import TrafficMultimodalAgent
from src.rag.langchain_rag import LangChainRAGSystem

async def test_traffic_rag_agent():
    """测试RAG增强的交通Agent"""
    print("🚀 测试RAG增强的交通多模态Agent")
    print("=" * 70)
    
    try:
        # 1. 创建原始Agent
        print("📦 创建原始Agent...")
        agent = TrafficMultimodalAgent()
        await agent.initialize()
        
        # 2. 创建RAG系统
        print("🧠 创建RAG知识增强系统...")
        rag_system = LangChainRAGSystem()
        
        # 3. 检查RAG系统状态
        print(f"\n📊 检查RAG系统状态...")
        rag_status = rag_system.get_collection_info()
        print(f"RAG状态: {rag_status.get('status', 'unknown')}")
        print(f"知识库信息: {rag_status}")
        
        # 4. 测试RAG知识检索功能
        print(f"\n🔍 测试RAG知识检索...")
        test_knowledge_query = "超速违法怎么处罚？"
        
        knowledge_results = rag_system.search_knowledge(test_knowledge_query, k=3)
        print(f"测试查询: {test_knowledge_query}")
        print(f"找到知识: {len(knowledge_results)} 条")
        
        if knowledge_results:
            print("知识预览:")
            for i, result in enumerate(knowledge_results[:2], 1):
                content = result['content'][:100] + "..." if len(result['content']) > 100 else result['content']
                print(f"  {i}. {content}")
        
        # 5. 测试RAG增强的查询处理
        print(f"\n🎯 测试RAG增强的查询处理...")
        test_query = "超速违法的处罚标准是什么？"
        
        print(f"\n查询: {test_query}")
        
        # 1. 先用RAG检索相关知识
        knowledge_results = rag_system.search_knowledge(test_query, k=3)
        print(f"检索到 {len(knowledge_results)} 条相关知识")
        
        # 2. 格式化知识上下文
        if knowledge_results:
            knowledge_context = "根据专业知识库:\n"
            for i, result in enumerate(knowledge_results, 1):
                knowledge_context += f"{i}. {result['content']}\n"
                if result['law']:
                    knowledge_context += f"   法律依据: {result['law']}\n"
                if result['article']:
                    knowledge_context += f"   条文: {result['article']}\n"
                knowledge_context += "\n"
            
            # 3. 构建增强查询
            enhanced_query = f"""基于以下专业知识回答问题：

{knowledge_context}

用户问题：{test_query}

请优先参考上述专业知识库的权威信息，并在回答时明确引用相关法规条文。"""
            
            print(f"\n📚 增强后的查询长度: {len(enhanced_query)} 字符")
            
            # 4. 调用Agent处理增强查询
            print(f"\n🤖 调用Agent处理增强查询...")
            try:
                response = await agent.process_query(enhanced_query)
                print("RAG增强Agent回复:")
                print(response)
                print(f"\n✅ RAG增强查询处理成功")
            except Exception as e:
                print(f"❌ RAG增强查询处理失败: {e}")
                import traceback
                traceback.print_exc()
        
        # 6. 对比测试：普通查询 vs RAG增强查询
        print(f"\n🔄 对比测试...")
        
        print(f"\n--- 普通模式回答 ---")
        try:
            normal_response = await agent.process_query(test_query)
            print(normal_response[:200] + "..." if len(normal_response) > 200 else normal_response)
        except Exception as e:
            print(f"普通查询失败: {e}")
        
        print(f"\n🎉 RAG增强Agent测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 70)
    print("🚗 交通多模态智能助手 - RAG增强Agent测试")
    print("=" * 70)
    
    success = asyncio.run(test_traffic_rag_agent())
    
    if success:
        print("\n✅ RAG增强Agent测试成功!")
        print("💡 Agent已成功集成RAG知识增强功能!")
    else:
        print("\n❌ RAG增强Agent测试失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()