# -*- coding: utf-8 -*-
"""
图像处理工具模块
"""
import base64
import io
from pathlib import Path
from typing import Union, Tuple
from PIL import Image
import cv2
import numpy as np
from src.utils.logger import get_logger

logger = get_logger(__name__)

class ImageProcessor:
    """图像处理器"""
    
    def __init__(self):
        self.max_image_size = (1920, 1080)  # 最大图像尺寸
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.gif'}
    
    def process_uploaded_image(self, image_file) -> dict:
        """
        处理上传的图像文件
        
        Args:
            image_file: 上传的图像文件
            
        Returns:
            处理结果字典
        """
        try:
            # 打开图像
            image = Image.open(image_file)
            
            # 基本信息
            info = {
                'width': image.width,
                'height': image.height,
                'format': image.format,
                'mode': image.mode,
                'size_mb': len(image_file.getvalue()) / (1024 * 1024) if hasattr(image_file, 'getvalue') else 0
            }
            
            # 质量评估
            quality_score = self.assess_image_quality(image)
            info['quality_score'] = quality_score
            
            # 图像预处理
            processed_image = self.preprocess_image(image)
            
            # 转换为base64编码
            base64_image = self.image_to_base64(processed_image)
            
            return {
                'success': True,
                'image_info': info,
                'processed_image': processed_image,
                'base64_image': base64_image,
                'quality_assessment': self.get_quality_assessment(quality_score)
            }
            
        except Exception as e:
            logger.error(f"图像处理失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def preprocess_image(self, image: Image.Image) -> Image.Image:
        """
        预处理图像
        
        Args:
            image: PIL图像对象
            
        Returns:
            处理后的图像
        """
        # 转换为RGB模式
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 调整图像尺寸
        if image.width > self.max_image_size[0] or image.height > self.max_image_size[1]:
            image.thumbnail(self.max_image_size, Image.Resampling.LANCZOS)
            logger.info(f"图像尺寸调整为: {image.width}x{image.height}")
        
        return image
    
    def assess_image_quality(self, image: Image.Image) -> float:
        """
        评估图像质量
        
        Args:
            image: PIL图像对象
            
        Returns:
            质量分数 (0-1)
        """
        try:
            # 转换为OpenCV格式
            img_array = np.array(image)
            if len(img_array.shape) == 3:
                img_gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                img_gray = img_array
            
            # 计算拉普拉斯方差（清晰度指标）
            laplacian_var = cv2.Laplacian(img_gray, cv2.CV_64F).var()
            
            # 计算亮度和对比度
            brightness = np.mean(img_gray)
            contrast = np.std(img_gray)
            
            # 综合质量评分
            sharpness_score = min(1.0, laplacian_var / 1000)
            brightness_score = 1.0 - abs(brightness - 128) / 128
            contrast_score = min(1.0, contrast / 64)
            
            quality_score = (sharpness_score * 0.5 + brightness_score * 0.3 + contrast_score * 0.2)
            
            return round(quality_score, 3)
            
        except Exception as e:
            logger.warning(f"质量评估失败: {str(e)}")
            return 0.5  # 默认中等质量
    
    def get_quality_assessment(self, score: float) -> dict:
        """
        获取质量评估结果
        
        Args:
            score: 质量分数
            
        Returns:
            评估结果
        """
        if score >= 0.8:
            return {
                'level': 'excellent',
                'message': '图像质量优秀，适合分析',
                'color': 'success'
            }
        elif score >= 0.6:
            return {
                'level': 'good',
                'message': '图像质量良好',
                'color': 'info'
            }
        elif score >= 0.4:
            return {
                'level': 'fair',
                'message': '图像质量一般，可能影响分析精度',
                'color': 'warning'
            }
        else:
            return {
                'level': 'poor',
                'message': '图像质量较差，建议重新上传',
                'color': 'error'
            }
    
    def image_to_base64(self, image: Image.Image) -> str:
        """
        将图像转换为base64编码
        
        Args:
            image: PIL图像对象
            
        Returns:
            base64编码字符串
        """
        buffered = io.BytesIO()
        image.save(buffered, format="JPEG", quality=85)
        img_base64 = base64.b64encode(buffered.getvalue()).decode()
        return img_base64
    
    def validate_image_file(self, filename: str) -> bool:
        """
        验证图像文件格式
        
        Args:
            filename: 文件名
            
        Returns:
            是否为支持的格式
        """
        suffix = Path(filename).suffix.lower()
        return suffix in self.supported_formats