#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试RAG检索系统功能
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.rag.vector_store import TrafficVectorStore
from src.rag.embeddings import BGEEmbeddings
from src.rag.retrieval import TrafficKnowledgeRetriever

async def test_rag_retrieval():
    """测试RAG检索系统的各项功能"""
    print("🔍 测试RAG检索系统功能")
    print("=" * 50)
    
    try:
        # 1. 初始化组件
        print("📦 初始化RAG组件...")
        vector_store = TrafficVectorStore()
        embeddings = BGEEmbeddings()
        retriever = TrafficKnowledgeRetriever(vector_store, embeddings)
        print("✅ RAG组件初始化成功!")
        
        # 2. 检查向量数据库状态
        print(f"\n📊 检查向量数据库状态...")
        collection_info = vector_store.get_collection_info()
        print("当前集合状态:")
        for name, info in collection_info.items():
            if name != "summary":
                print(f"   - {name}: {info['document_count']} 个文档")
            else:
                print(f"   - 总结: {info}")
        
        # 3. 测试查询预处理
        print(f"\n🔧 测试查询预处理...")
        test_queries = [
            "超速违法怎么处罚？",
            "雨天开车注意什么",
            "酒驾的后果",
            "如何正确使用安全带"
        ]
        
        for query in test_queries:
            processed = retriever._preprocess_query(query)
            analysis = retriever._analyze_query(processed)
            print(f"   - 原查询: '{query}'")
            print(f"     处理后: '{processed}'")
            print(f"     分类: {analysis['categories']}")
            print(f"     意图: {analysis['intent']}")
        
        # 4. 测试集合选择
        print(f"\n🎯 测试集合选择策略...")
        for query in test_queries:
            processed = retriever._preprocess_query(query)
            analysis = retriever._analyze_query(processed)
            collections = retriever._select_collections(analysis)
            print(f"   - 查询: '{query}' -> 选择集合: {collections}")
        
        # 5. 如果数据库有数据，测试检索功能
        total_docs = collection_info.get("summary", {}).get("total_documents", 0)
        if total_docs > 0:
            print(f"\n🔍 测试知识检索功能...")
            for query in test_queries:
                print(f"\n   查询: '{query}'")
                result = await retriever.retrieve(query, top_k=3)
                
                if result["success"]:
                    print(f"   ✅ 检索成功: 找到 {result['returned_count']} 个结果")
                    print(f"   ⏱️ 耗时: {result['processing_time']:.3f}秒")
                    print(f"   📚 搜索集合: {result['collections_searched']}")
                    
                    # 显示最相关的结果
                    if result["results"]:
                        top_result = result["results"][0]
                        print(f"   🎯 最相关结果:")
                        print(f"      相似度: {top_result.get('similarity_score', 0):.3f}")
                        print(f"      来源: {top_result.get('source_collection', 'unknown')}")
                        print(f"      内容: {top_result['content'][:100]}...")
                else:
                    print(f"   ❌ 检索失败: {result.get('error', 'Unknown error')}")
        else:
            print(f"\n⚠️ 向量数据库为空，跳过检索测试")
            print(f"   请先运行知识导入脚本填充数据")
        
        # 6. 获取检索统计
        print(f"\n📈 检索统计信息:")
        stats = retriever.get_retrieval_stats()
        for key, value in stats.items():
            if isinstance(value, float):
                print(f"   - {key}: {value:.3f}")
            else:
                print(f"   - {key}: {value}")
        
        print(f"\n🎉 RAG检索系统测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ RAG检索系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_rag_retrieval())
    if not success:
        sys.exit(1)