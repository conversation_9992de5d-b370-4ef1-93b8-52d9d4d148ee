# -*- coding: utf-8 -*-
"""
交通改进建议生成工具 - Traffic Suggestion Generator

这个模块实现了基于分析结果生成交通改进建议的功能，是整个系统的决策支持核心。

核心思路：
1. 综合分析场景分析、违规检测、安全评估结果
2. 基于问题类型和严重程度生成针对性建议
3. 分类组织建议（基础设施、管理措施、宣传教育等）
4. 提供实施指导和预期效果评估

主要功能：
1. 建议分类生成 - 按照不同类别组织改进建议
2. 优先级排序 - 根据紧急程度和影响程度排序
3. 可行性评估 - 评估建议的实施难度和成本
4. 效果预测 - 预测建议实施后的改进效果

设计特点：
- 系统性：从多个角度提供综合性改进方案
- 针对性：基于具体问题提供有针对性的建议
- 可操作性：提供具体的实施步骤和指导
- 科学性：基于交通工程理论和实践经验

学习要点：
- 决策支持系统的设计原理
- 多因素决策分析方法
- 建议生成算法的实现
- 实施指导的结构化表达
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from datetime import datetime

from src.utils.logger import get_logger
from config.settings import SUGGESTION_CONFIG

# 获取专用日志记录器
logger = get_logger(__name__)

def safe_str_lower(value):
    """
    安全地将值转换为小写字符串
    处理dict、list、None等类型，避免.lower()调用错误
    """
    if isinstance(value, str):
        return value.lower()
    elif isinstance(value, dict):
        # 如果是dict，尝试获取常见的描述性字段
        for key in ['condition', 'status', 'description', 'state']:
            if key in value and isinstance(value[key], str):
                return value[key].lower()
        return ""
    elif isinstance(value, list):
        # 如果是list，join成字符串
        return " ".join(str(item).lower() if isinstance(item, str) else str(item) for item in value)
    elif value is None:
        return ""
    else:
        return str(value).lower()

class SuggestionCategory(Enum):
    """
    建议类别枚举
    
    将改进建议按照实施主体和性质进行分类：
    - 基础设施：道路设计、设施完善等硬件改进
    - 管理措施：执法管理、交通组织等管理改进
    - 技术手段：智能化设施、科技应用等技术改进
    - 宣传教育：安全宣传、行为引导等软性措施
    - 应急预案：突发情况处理、应急响应等
    """
    INFRASTRUCTURE = "infrastructure"    # 基础设施改进
    MANAGEMENT = "management"           # 管理措施改进
    TECHNOLOGY = "technology"           # 技术手段改进
    EDUCATION = "education"             # 宣传教育改进
    EMERGENCY = "emergency"             # 应急预案改进

class SuggestionPriority(Enum):
    """
    建议优先级枚举
    
    根据问题紧急程度和影响范围确定优先级：
    - 紧急：需要立即实施的安全措施
    - 高：重要且紧迫的改进措施
    - 中：重要但不紧急的改进措施
    - 低：有益但非必需的优化措施
    """
    URGENT = "urgent"      # 紧急 (需要立即实施)
    HIGH = "high"          # 高 (1周内实施)
    MEDIUM = "medium"      # 中 (1月内实施)
    LOW = "low"           # 低 (3月内实施)

@dataclass
class ImprovementSuggestion:
    """
    改进建议数据类
    
    封装了完整的改进建议信息，包括：
    - 建议内容和分类
    - 优先级和紧急程度
    - 实施指导和成本估算
    - 效果预期和评估指标
    
    设计目的：
    - 统一建议信息的数据结构
    - 便于建议的排序和筛选
    - 支持建议的详细描述
    - 提供实施的具体指导
    """
    suggestion_id: str              # 建议唯一标识
    title: str                     # 建议标题
    description: str               # 详细描述
    category: SuggestionCategory   # 建议类别
    priority: SuggestionPriority   # 优先级
    target_problems: List[str]     # 针对的问题
    implementation_steps: List[str] # 实施步骤
    estimated_cost: str            # 成本估算
    implementation_time: str       # 实施周期
    expected_effect: str           # 预期效果
    success_indicators: List[str]  # 成功指标
    responsible_party: str         # 责任主体
    feasibility_score: float       # 可行性分数 (0-1)

class TrafficSuggestionGenerator:
    """
    交通改进建议生成器
    
    这个类实现了全面的交通改进建议生成功能。
    
    核心方法：
    - generate(): 主要生成接口
    - _analyze_problem_patterns(): 分析问题模式
    - _generate_category_suggestions(): 按类别生成建议
    - _prioritize_suggestions(): 建议优先级排序
    - _evaluate_feasibility(): 可行性评估
    
    设计原则：
    - 全面性：覆盖各个方面的改进需求
    - 针对性：基于具体问题生成精准建议
    - 实用性：提供可操作的实施指导
    - 科学性：基于交通工程和管理理论
    """
    
    def __init__(self):
        """
        初始化交通改进建议生成器
        
        初始化步骤：
        1. 设置日志记录器
        2. 加载建议模板和规则库
        3. 初始化生成配置参数
        4. 建立建议知识库
        """
        # 设置专用日志记录器
        self.logger = get_logger(self.__class__.__name__)
        
        # 生成统计信息
        self.generation_count = 0
        self.total_suggestions_generated = 0
        
        # 生成配置参数
        self.max_suggestions_per_category = SUGGESTION_CONFIG.get("max_per_category", 3)
        self.min_feasibility_score = SUGGESTION_CONFIG.get("min_feasibility", 0.3)
        self.priority_weights = SUGGESTION_CONFIG.get("priority_weights", {
            "safety_impact": 0.4,      # 安全影响权重
            "implementation_ease": 0.3, # 实施难易度权重
            "cost_effectiveness": 0.2,  # 成本效益权重
            "urgency": 0.1             # 紧急程度权重
        })
        
        # 加载建议知识库
        self.suggestion_templates = self._load_suggestion_templates()
        
        # 问题-建议映射规则
        self.problem_solution_mapping = self._build_problem_solution_mapping()
        
        # 初始化完成日志
        self.logger.info(f"💡 交通改进建议生成器初始化完成，加载了 {len(self.suggestion_templates)} 个建议模板")
    
    def _load_suggestion_templates(self) -> Dict[str, Dict]:
        """
        加载建议模板库
        
        建议模板包含了各种常见交通问题的标准化解决方案。
        在实际应用中，这些模板可以从数据库或配置文件中加载。
        
        Returns:
            Dict[str, Dict]: 建议模板库
            
        学习要点：
        - 知识库的结构化组织
        - 模板化方案的设计思路
        - 标准化建议的管理方法
        - 可扩展的知识体系构建
        """
        templates = {}
        
        try:
            # 基础设施类建议模板
            templates["infrastructure"] = {
                "lane_separation": {
                    "title": "完善车道隔离设施",
                    "description": "在机动车道与非机动车道之间增设物理隔离设施",
                    "steps": [
                        "现场勘测车道配置和空间条件",
                        "设计隔离设施方案（护栏、绿化带等）",
                        "采购符合标准的隔离设施",
                        "组织施工安装",
                        "验收并投入使用"
                    ],
                    "cost": "中等（5-20万元/公里）",
                    "time": "2-4周",
                    "effect": "显著减少机非混行，降低冲突概率60-80%"
                },
                "signal_upgrade": {
                    "title": "升级交通信号设施",
                    "description": "更新老旧信号设备，优化信号配时",
                    "steps": [
                        "检测现有信号设备状况",
                        "制定设备更新计划",
                        "采购新型智能信号设备",
                        "专业安装和调试",
                        "优化信号配时方案"
                    ],
                    "cost": "较高（20-50万元/路口）",
                    "time": "4-8周",
                    "effect": "提高通行效率30-50%，减少等待时间"
                },
                "road_marking": {
                    "title": "完善道路标识标线",
                    "description": "重新施划清晰的车道标线和交通标识",
                    "steps": [
                        "清理旧有模糊标线",
                        "按标准重新设计标线方案",
                        "使用高质量反光材料施工",
                        "安装必要的交通标志",
                        "质量验收和维护计划"
                    ],
                    "cost": "较低（1-5万元/公里）",
                    "time": "1-2周",
                    "effect": "提高道路辨识度，减少违规行为30%"
                }
            }
            
            # 管理措施类建议模板
            templates["management"] = {
                "enforcement_increase": {
                    "title": "加强执法巡查力度",
                    "description": "增加该路段的交警巡查频次和执法力度",
                    "steps": [
                        "分析该路段违规行为特点和高发时段",
                        "制定针对性巡查计划",
                        "增派执法人员定点执勤",
                        "对违规行为及时处罚和纠正",
                        "建立长效管理机制"
                    ],
                    "cost": "中等（人力成本）",
                    "time": "立即实施",
                    "effect": "快速降低违规率50-70%"
                },
                "traffic_organization": {
                    "title": "优化交通组织方案",
                    "description": "重新设计该路段的交通流组织方式",
                    "steps": [
                        "深入调研交通流特征",
                        "分析现有组织方案问题",
                        "设计优化的交通组织方案",
                        "试运行并收集反馈",
                        "正式实施并持续优化"
                    ],
                    "cost": "较低（主要是人力和时间成本）",
                    "time": "2-4周",
                    "effect": "改善交通秩序，提高通行效率20-40%"
                },
                "timing_optimization": {
                    "title": "优化信号配时方案",
                    "description": "根据实际交通流调整信号灯配时",
                    "steps": [
                        "收集不同时段交通流数据",
                        "分析现有配时方案效果",
                        "建立交通流模型",
                        "设计新的配时方案",
                        "实施并持续调优"
                    ],
                    "cost": "很低（技术服务费）",
                    "time": "1-2周",
                    "effect": "减少等待时间，提高通行效率25-45%"
                }
            }
            
            # 宣传教育类建议模板
            templates["education"] = {
                "safety_campaign": {
                    "title": "开展交通安全宣传活动",
                    "description": "针对该区域特点组织交通安全宣传教育",
                    "steps": [
                        "分析该区域主要违规行为类型",
                        "制定针对性宣传方案",
                        "制作宣传材料和警示标语",
                        "组织现场宣传活动",
                        "建立长期宣传机制"
                    ],
                    "cost": "较低（2-5万元）",
                    "time": "2-3周",
                    "effect": "提高安全意识，长期减少违规20-30%"
                },
                "warning_signs": {
                    "title": "增设安全警示标识",
                    "description": "在关键位置设置醒目的安全警示标识",
                    "steps": [
                        "识别需要警示的关键位置",
                        "设计醒目的警示标识",
                        "选择合适的材料和规格",
                        "专业安装并确保可见性",
                        "定期检查和维护"
                    ],
                    "cost": "很低（0.5-2万元）",
                    "time": "1周",
                    "effect": "提醒作用显著，减少违规15-25%"
                }
            }
            
            # 技术手段类建议模板
            templates["technology"] = {
                "intelligent_monitoring": {
                    "title": "安装智能监控系统",
                    "description": "部署智能交通监控和违规抓拍系统",
                    "steps": [
                        "现场勘察确定安装位置",
                        "选择适合的智能监控设备",
                        "专业安装和网络连接",
                        "系统调试和参数配置",
                        "投入使用并定期维护"
                    ],
                    "cost": "较高（10-30万元/套）",
                    "time": "3-6周",
                    "effect": "全天候监控，违规检出率90%以上"
                },
                "adaptive_signals": {
                    "title": "部署自适应信号控制",
                    "description": "安装能够根据实时交通流调整的智能信号系统",
                    "steps": [
                        "评估现有信号设备兼容性",
                        "设计自适应控制方案",
                        "采购和安装智能控制设备",
                        "系统集成和参数调优",
                        "试运行和正式启用"
                    ],
                    "cost": "高（30-80万元/路口）",
                    "time": "6-12周",
                    "effect": "动态优化通行，效率提升40-60%"
                }
            }
            
            # 应急预案类建议模板
            templates["emergency"] = {
                "emergency_response": {
                    "title": "建立应急响应机制",
                    "description": "制定突发交通事件的快速响应预案",
                    "steps": [
                        "分析可能的突发情况类型",
                        "制定分级响应预案",
                        "建立应急联动机制",
                        "配备必要的应急设备",
                        "定期演练和预案更新"
                    ],
                    "cost": "中等（应急设备和培训成本）",
                    "time": "4-6周",
                    "effect": "快速处置突发事件，减少影响时间70%"
                }
            }
            
            return templates
            
        except Exception as e:
            self.logger.error(f"❌ 建议模板加载失败: {e}")
            return {}
    
    def _build_problem_solution_mapping(self) -> Dict[str, List[str]]:
        """
        构建问题-解决方案映射规则
        
        这个映射规则定义了不同类型的交通问题应该采用哪些解决方案。
        基于交通工程理论和实践经验建立。
        
        Returns:
            Dict[str, List[str]]: 问题-解决方案映射
            
        学习要点：
        - 专家知识的规则化表示
        - 问题分类和解决方案匹配
        - 领域知识的结构化组织
        - 决策规则的设计原理
        """
        mapping = {}
        
        try:
            # 车道违规问题的解决方案
            mapping["lane_violation"] = [
                "lane_separation",        # 完善车道隔离
                "road_marking",          # 完善标识标线
                "enforcement_increase",   # 加强执法
                "warning_signs",         # 增设警示标识
                "safety_campaign"        # 安全宣传
            ]
            
            # 信号违规问题的解决方案
            mapping["signal_violation"] = [
                "signal_upgrade",        # 升级信号设施
                "timing_optimization",   # 优化配时
                "intelligent_monitoring", # 智能监控
                "enforcement_increase",  # 加强执法
                "safety_campaign"       # 安全宣传
            ]
            
            # 行人违规问题的解决方案
            mapping["pedestrian_violation"] = [
                "signal_upgrade",        # 信号设施
                "warning_signs",         # 警示标识
                "safety_campaign",       # 安全宣传
                "enforcement_increase",  # 执法管理
                "road_marking"          # 人行横道标线
            ]
            
            # 停车违规问题的解决方案
            mapping["parking_violation"] = [
                "enforcement_increase",  # 加强执法
                "warning_signs",         # 警示标识
                "intelligent_monitoring", # 智能监控
                "traffic_organization",  # 交通组织
                "safety_campaign"       # 宣传教育
            ]
            
            # 超速违规问题的解决方案
            mapping["speed_violation"] = [
                "intelligent_monitoring", # 智能监控
                "warning_signs",         # 警示标识
                "enforcement_increase",  # 加强执法
                "road_marking",          # 标识标线
                "safety_campaign"       # 安全宣传
            ]
            
            # 交通拥堵问题的解决方案
            mapping["traffic_congestion"] = [
                "traffic_organization",  # 交通组织优化
                "timing_optimization",   # 信号配时优化
                "adaptive_signals",      # 自适应信号
                "lane_separation",      # 车道优化
                "intelligent_monitoring" # 智能监控
            ]
            
            # 基础设施问题的解决方案
            mapping["infrastructure_issues"] = [
                "signal_upgrade",        # 设施升级
                "road_marking",          # 标识完善
                "lane_separation",       # 隔离设施
                "warning_signs",         # 警示标识
                "emergency_response"     # 应急预案
            ]
            
            # 环境安全问题的解决方案
            mapping["environmental_risks"] = [
                "warning_signs",         # 环境警示
                "intelligent_monitoring", # 智能监控
                "emergency_response",    # 应急预案
                "enforcement_increase",  # 加强管理
                "safety_campaign"       # 安全宣传
            ]
            
            return mapping
            
        except Exception as e:
            self.logger.error(f"❌ 问题-解决方案映射构建失败: {e}")
            return {}
    
    async def generate(self, 
                      scene_analysis: Dict[str, Any],
                      violation_results: Dict[str, Any],
                      safety_assessment: Dict[str, Any],
                      generation_options: Optional[Dict] = None) -> Dict[str, Any]:
        """
        生成交通改进建议的主要接口
        
        这是建议生成的核心方法，负责：
        1. 综合分析各种输入数据
        2. 识别主要问题和改进需求
        3. 生成分类的改进建议
        4. 评估建议的可行性和优先级
        5. 组织和格式化最终建议
        
        Args:
            scene_analysis: 交通场景分析结果
            violation_results: 违规检测结果
            safety_assessment: 安全评估结果
            generation_options: 生成选项配置
            
        Returns:
            Dict[str, Any]: 改进建议生成结果，包含：
            {
                "success": bool,                    # 生成是否成功
                "suggestions": Dict,                # 分类的改进建议
                "total_suggestions": int,           # 建议总数
                "priority_summary": Dict,           # 优先级汇总
                "implementation_timeline": Dict,    # 实施时间安排
                "cost_estimation": Dict,            # 成本估算汇总
                "generation_metadata": Dict         # 生成过程元数据
            }
            
        学习要点：
        - 多源数据的综合分析方法
        - 建议生成的系统化流程
        - 优先级排序的算法实现
        - 结果组织和格式化技巧
        """
        # 记录生成开始时间
        import time
        start_time = time.time()
        
        try:
            # 更新生成统计
            self.generation_count += 1
            
            self.logger.info(f"🔧 开始生成交通改进建议，第 {self.generation_count} 次生成")
            
            # 步骤1：验证输入数据
            if not self._validate_generation_inputs(scene_analysis, violation_results, safety_assessment):
                return {
                    "success": False,
                    "error": "输入数据不完整或格式错误",
                    "suggestions": {},
                    "total_suggestions": 0
                }
            
            # 步骤2：分析问题模式和严重程度
            problem_analysis = await self._analyze_problem_patterns(
                scene_analysis, violation_results, safety_assessment
            )
            
            # 步骤3：生成分类建议
            categorized_suggestions = await self._generate_categorized_suggestions(
                problem_analysis, generation_options
            )
            
            # 步骤4：评估建议可行性
            feasible_suggestions = await self._evaluate_suggestions_feasibility(
                categorized_suggestions, scene_analysis
            )
            
            # 步骤5：优先级排序
            prioritized_suggestions = await self._prioritize_suggestions(
                feasible_suggestions, problem_analysis
            )
            
            # 步骤6：生成实施时间安排
            implementation_timeline = self._generate_implementation_timeline(
                prioritized_suggestions
            )
            
            # 步骤7：成本估算汇总
            cost_estimation = self._calculate_cost_estimation(prioritized_suggestions)
            
            # 步骤8：构建完整的生成结果
            processing_time = time.time() - start_time
            total_suggestions = sum(len(suggestions) for suggestions in prioritized_suggestions.values())
            
            result = {
                "success": True,
                "suggestions": self._format_suggestions_output(prioritized_suggestions),
                "total_suggestions": total_suggestions,
                "priority_summary": self._generate_priority_summary(prioritized_suggestions),
                "implementation_timeline": implementation_timeline,
                "cost_estimation": cost_estimation,
                "generation_metadata": {
                    "processing_time": round(processing_time, 3),
                    "generation_timestamp": datetime.now().isoformat(),
                    "problems_identified": len(problem_analysis.get("identified_problems", [])),
                    "total_generations": self.generation_count,
                    "generation_version": "1.0"
                }
            }
            
            # 更新统计信息
            self.total_suggestions_generated += total_suggestions
            
            # 记录生成完成日志
            self.logger.info(f"✅ 建议生成完成，共生成 {total_suggestions} 条建议，耗时 {processing_time:.3f}秒")
            
            return result
            
        except Exception as e:
            # 异常处理：确保生成失败不会影响整个系统
            error_msg = f"建议生成过程中发生错误: {str(e)}"
            self.logger.error(f"❌ {error_msg}")
            
            return {
                "success": False,
                "error": error_msg,
                "suggestions": {},
                "total_suggestions": 0,
                "processing_time": time.time() - start_time
            }
    
    def _validate_generation_inputs(self, 
                                  scene_analysis: Dict[str, Any],
                                  violation_results: Dict[str, Any],
                                  safety_assessment: Dict[str, Any]) -> bool:
        """
        验证建议生成输入数据的有效性
        
        Args:
            scene_analysis: 场景分析数据
            violation_results: 违规检测结果
            safety_assessment: 安全评估结果
            
        Returns:
            bool: 数据是否有效
        """
        try:
            # 检查场景分析数据
            if not isinstance(scene_analysis, dict) or not scene_analysis:
                self.logger.error("❌ 场景分析数据无效")
                return False
            
            # 检查违规检测数据
            if not isinstance(violation_results, dict):
                self.logger.error("❌ 违规检测数据无效")
                return False
            
            # 检查安全评估数据
            if not isinstance(safety_assessment, dict):
                self.logger.error("❌ 安全评估数据无效")
                return False
            
            # 检查关键字段存在性
            if "violations_detected" not in violation_results:
                self.logger.error("❌ 违规检测结果缺少violations_detected字段")
                return False
            
            if "safety_assessment" not in safety_assessment:
                self.logger.warning("⚠️ 缺少详细的安全评估信息，将使用基础分析")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 输入验证失败: {e}")
            return False
    
    async def _analyze_problem_patterns(self, 
                                      scene_analysis: Dict[str, Any],
                                      violation_results: Dict[str, Any],
                                      safety_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析问题模式和严重程度
        
        通过综合分析各种输入数据，识别主要的交通问题类型
        和严重程度，为建议生成提供基础。
        
        Args:
            scene_analysis: 场景分析数据
            violation_results: 违规检测结果
            safety_assessment: 安全评估结果
            
        Returns:
            Dict[str, Any]: 问题分析结果
            
        学习要点：
        - 模式识别和问题分类方法
        - 多源信息的综合分析技术
        - 问题严重程度的量化评估
        - 因果关系的识别和分析
        """
        try:
            problem_analysis = {
                "identified_problems": [],
                "problem_severity": {},
                "root_causes": [],
                "affected_areas": [],
                "urgency_factors": []
            }
            
            # 分析违规问题
            violations = violation_results.get("violations_detected", [])
            violation_types = set()
            
            for violation in violations:
                violation_type = violation.get("violation_type", "unknown")
                violation_types.add(violation_type)
                
                # 记录问题和严重程度
                problem_analysis["identified_problems"].append({
                    "type": violation_type,
                    "name": violation.get("name", "未知违规"),
                    "severity": violation.get("severity", "medium"),
                    "source": "violation_detection"
                })
                
                # 分析严重程度
                severity = violation.get("severity", "medium")
                if violation_type not in problem_analysis["problem_severity"]:
                    problem_analysis["problem_severity"][violation_type] = []
                problem_analysis["problem_severity"][violation_type].append(severity)
            
            # 分析安全评估问题
            safety_data = safety_assessment.get("safety_assessment", {})
            risk_level = safety_data.get("safety_level", safety_assessment.get("risk_level", "C"))
            
            if risk_level in ["D", "E"]:
                problem_analysis["urgency_factors"].append(f"安全等级为{risk_level}级，需要紧急关注")
            
            # 分析主要风险点
            major_risks = safety_assessment.get("major_risks", [])
            for risk in major_risks:
                problem_analysis["identified_problems"].append({
                    "type": "safety_risk",
                    "name": risk,
                    "severity": "high" if risk_level in ["D", "E"] else "medium",
                    "source": "safety_assessment"
                })
            
            # 分析环境因素
            environment = scene_analysis.get("environment", {})
            weather = safe_str_lower(environment.get("weather", ""))
            lighting = safe_str_lower(environment.get("lighting", ""))
            
            if any(bad_condition in weather for bad_condition in ["雨", "雪", "雾"]):
                problem_analysis["identified_problems"].append({
                    "type": "environmental_risk",
                    "name": f"恶劣天气条件: {weather}",
                    "severity": "medium",
                    "source": "environmental_analysis"
                })
            
            if any(bad_light in lighting for bad_light in ["夜晚", "黄昏"]):
                problem_analysis["identified_problems"].append({
                    "type": "environmental_risk", 
                    "name": f"光照条件不佳: {lighting}",
                    "severity": "medium",
                    "source": "environmental_analysis"
                })
            
            # 分析基础设施问题
            facilities = scene_analysis.get("traffic_facilities", {})
            for facility_type, condition in facilities.items():
                condition_lower = safe_str_lower(condition)
                if any(problem in condition_lower for problem in ["故障", "损坏", "缺失"]):
                    problem_analysis["identified_problems"].append({
                        "type": "infrastructure_issues",
                        "name": f"{facility_type}设施问题: {condition}",
                        "severity": "high" if "故障" in condition_lower else "medium",
                        "source": "infrastructure_analysis"
                    })
            
            # 分析根本原因
            problem_analysis["root_causes"] = self._identify_root_causes(
                problem_analysis["identified_problems"]
            )
            
            # 识别受影响区域
            problem_analysis["affected_areas"] = self._identify_affected_areas(
                scene_analysis, problem_analysis["identified_problems"]
            )
            
            self.logger.debug(f"📊 问题分析完成，识别出 {len(problem_analysis['identified_problems'])} 个问题")
            
            return problem_analysis
            
        except Exception as e:
            self.logger.error(f"❌ 问题模式分析失败: {e}")
            return {
                "identified_problems": [],
                "problem_severity": {},
                "root_causes": [],
                "affected_areas": [],
                "urgency_factors": []
            }
    
    def _identify_root_causes(self, identified_problems: List[Dict]) -> List[str]:
        """
        识别问题的根本原因
        
        通过分析问题类型和模式，识别可能的根本原因。
        
        Args:
            identified_problems: 识别出的问题列表
            
        Returns:
            List[str]: 根本原因列表
        """
        root_causes = []
        
        try:
            problem_types = [problem["type"] for problem in identified_problems]
            
            # 违规行为多样化表明管理不足
            violation_types = [p["type"] for p in identified_problems if p["source"] == "violation_detection"]
            if len(set(violation_types)) >= 3:
                root_causes.append("交通管理执法力度不足，违规行为多发")
            
            # 基础设施问题表明维护不当
            if "infrastructure_issues" in problem_types:
                root_causes.append("交通基础设施维护保养不及时")
            
            # 环境问题表明应对措施不足
            if "environmental_risk" in problem_types:
                root_causes.append("恶劣环境条件应对措施不完善")
            
            # 安全风险表明系统性问题
            if "safety_risk" in problem_types:
                root_causes.append("交通安全管理体系存在薄弱环节")
            
            return root_causes
            
        except Exception as e:
            self.logger.error(f"❌ 根本原因识别失败: {e}")
            return ["需要进一步分析确定根本原因"]
    
    def _identify_affected_areas(self, 
                               scene_analysis: Dict[str, Any],
                               identified_problems: List[Dict]) -> List[str]:
        """
        识别受影响的区域和范围
        
        Args:
            scene_analysis: 场景分析数据
            identified_problems: 识别出的问题列表
            
        Returns:
            List[str]: 受影响区域列表
        """
        affected_areas = []
        
        try:
            # 基于道路环境识别
            road_env = scene_analysis.get("road_environment", {})
            road_type = road_env.get("road_type", "道路")
            affected_areas.append(f"当前{road_type}路段")
            
            # 基于问题类型识别
            problem_types = set(problem["type"] for problem in identified_problems)
            
            if "lane_violation" in problem_types:
                affected_areas.append("机动车道和非机动车道交界区域")
            
            if "pedestrian_violation" in problem_types:
                affected_areas.append("人行横道和行人活动区域")
            
            if "signal_violation" in problem_types:
                affected_areas.append("信号控制路口区域")
            
            # 去重
            affected_areas = list(set(affected_areas))
            
            return affected_areas
            
        except Exception as e:
            self.logger.error(f"❌ 受影响区域识别失败: {e}")
            return ["当前路段"]
    
    async def _generate_categorized_suggestions(self, 
                                             problem_analysis: Dict[str, Any],
                                             generation_options: Optional[Dict] = None) -> Dict[SuggestionCategory, List[ImprovementSuggestion]]:
        """
        按类别生成改进建议
        
        基于问题分析结果，查找对应的解决方案模板，
        生成分类的改进建议。
        
        Args:
            problem_analysis: 问题分析结果
            generation_options: 生成选项
            
        Returns:
            Dict[SuggestionCategory, List[ImprovementSuggestion]]: 分类的建议列表
        """
        try:
            categorized_suggestions = {
                SuggestionCategory.INFRASTRUCTURE: [],
                SuggestionCategory.MANAGEMENT: [],
                SuggestionCategory.TECHNOLOGY: [],
                SuggestionCategory.EDUCATION: [],
                SuggestionCategory.EMERGENCY: []
            }
            
            identified_problems = problem_analysis.get("identified_problems", [])
            
            # 为每个问题生成对应的建议
            for problem in identified_problems:
                problem_type = problem["type"]
                problem_severity = problem["severity"]
                
                # 查找对应的解决方案
                if problem_type in self.problem_solution_mapping:
                    solution_keys = self.problem_solution_mapping[problem_type]
                    
                    for solution_key in solution_keys:
                        # 在各个类别模板中查找解决方案
                        suggestion = self._create_suggestion_from_template(
                            solution_key, problem, problem_analysis
                        )
                        
                        if suggestion:
                            # 根据建议类型添加到对应类别
                            category = self._determine_suggestion_category(solution_key)
                            if category in categorized_suggestions:
                                categorized_suggestions[category].append(suggestion)
            
            # 为每个类别限制建议数量
            for category in categorized_suggestions:
                suggestions = categorized_suggestions[category]
                # 按可行性分数排序，取前N个
                suggestions.sort(key=lambda x: x.feasibility_score, reverse=True)
                categorized_suggestions[category] = suggestions[:self.max_suggestions_per_category]
            
            return categorized_suggestions
            
        except Exception as e:
            self.logger.error(f"❌ 分类建议生成失败: {e}")
            return {
                SuggestionCategory.INFRASTRUCTURE: [],
                SuggestionCategory.MANAGEMENT: [],
                SuggestionCategory.TECHNOLOGY: [],
                SuggestionCategory.EDUCATION: [],
                SuggestionCategory.EMERGENCY: []
            }
    
    def _create_suggestion_from_template(self, 
                                       solution_key: str,
                                       problem: Dict[str, Any],
                                       problem_analysis: Dict[str, Any]) -> Optional[ImprovementSuggestion]:
        """
        基于模板创建改进建议
        
        Args:
            solution_key: 解决方案键值
            problem: 具体问题信息
            problem_analysis: 问题分析结果
            
        Returns:
            Optional[ImprovementSuggestion]: 创建的建议对象
        """
        try:
            # 在所有类别模板中查找解决方案
            template = None
            template_category = None
            
            for category, templates in self.suggestion_templates.items():
                if solution_key in templates:
                    template = templates[solution_key]
                    template_category = category
                    break
            
            if not template:
                return None
            
            # 创建建议对象
            suggestion = ImprovementSuggestion(
                suggestion_id=f"{solution_key}_{len(problem_analysis.get('identified_problems', []))}",
                title=template["title"],
                description=template["description"],
                category=self._determine_suggestion_category(solution_key),
                priority=self._determine_suggestion_priority(problem, template),
                target_problems=[problem["name"]],
                implementation_steps=template["steps"],
                estimated_cost=template["cost"],
                implementation_time=template["time"],
                expected_effect=template["effect"],
                success_indicators=self._generate_success_indicators(template),
                responsible_party=self._determine_responsible_party(template_category),
                feasibility_score=self._calculate_initial_feasibility(template, problem)
            )
            
            return suggestion
            
        except Exception as e:
            self.logger.error(f"❌ 从模板创建建议失败: {e}")
            return None
    
    def _determine_suggestion_category(self, solution_key: str) -> SuggestionCategory:
        """
        确定建议的类别
        
        Args:
            solution_key: 解决方案键值
            
        Returns:
            SuggestionCategory: 建议类别
        """
        category_mapping = {
            "lane_separation": SuggestionCategory.INFRASTRUCTURE,
            "signal_upgrade": SuggestionCategory.INFRASTRUCTURE,
            "road_marking": SuggestionCategory.INFRASTRUCTURE,
            "enforcement_increase": SuggestionCategory.MANAGEMENT,
            "traffic_organization": SuggestionCategory.MANAGEMENT,
            "timing_optimization": SuggestionCategory.MANAGEMENT,
            "safety_campaign": SuggestionCategory.EDUCATION,
            "warning_signs": SuggestionCategory.EDUCATION,
            "intelligent_monitoring": SuggestionCategory.TECHNOLOGY,
            "adaptive_signals": SuggestionCategory.TECHNOLOGY,
            "emergency_response": SuggestionCategory.EMERGENCY
        }
        
        return category_mapping.get(solution_key, SuggestionCategory.MANAGEMENT)
    
    def _determine_suggestion_priority(self, 
                                     problem: Dict[str, Any],
                                     template: Dict[str, Any]) -> SuggestionPriority:
        """
        确定建议的优先级
        
        Args:
            problem: 问题信息
            template: 建议模板
            
        Returns:
            SuggestionPriority: 建议优先级
        """
        problem_severity = problem.get("severity", "medium")
        
        # 基于问题严重程度确定优先级
        if problem_severity == "critical":
            return SuggestionPriority.URGENT
        elif problem_severity == "high":
            return SuggestionPriority.HIGH
        elif problem_severity == "medium":
            return SuggestionPriority.MEDIUM
        else:
            return SuggestionPriority.LOW
    
    def _generate_success_indicators(self, template: Dict[str, Any]) -> List[str]:
        """
        生成成功指标
        
        Args:
            template: 建议模板
            
        Returns:
            List[str]: 成功指标列表
        """
        # 基于模板效果描述生成成功指标
        effect = template.get("effect", "")
        
        indicators = []
        
        # 从效果描述中提取量化指标
        if "%" in effect:
            indicators.append("达到预期的量化改进目标")
        
        if "减少" in effect:
            indicators.append("相关问题发生率显著下降")
        
        if "提高" in effect:
            indicators.append("相关指标明显提升")
        
        # 添加通用指标
        indicators.extend([
            "实施方案按时完成",
            "相关方反馈积极",
            "运行状态稳定"
        ])
        
        return indicators[:3]  # 限制指标数量
    
    def _determine_responsible_party(self, template_category: str) -> str:
        """
        确定责任主体
        
        Args:
            template_category: 模板类别
            
        Returns:
            str: 责任主体
        """
        responsibility_mapping = {
            "infrastructure": "道路管理部门",
            "management": "交通管理部门",
            "technology": "科技信息部门",
            "education": "交通宣传部门",
            "emergency": "应急管理部门"
        }
        
        return responsibility_mapping.get(template_category, "相关管理部门")
    
    def _calculate_initial_feasibility(self, 
                                     template: Dict[str, Any],
                                     problem: Dict[str, Any]) -> float:
        """
        计算初始可行性分数
        
        Args:
            template: 建议模板
            problem: 问题信息
            
        Returns:
            float: 可行性分数 (0-1)
        """
        try:
            base_score = 0.7  # 基础可行性分数
            
            # 基于成本调整
            cost = safe_str_lower(template.get("cost", ""))
            if "很低" in cost or "较低" in cost:
                base_score += 0.15
            elif "较高" in cost or "高" in cost:
                base_score -= 0.1
            
            # 基于实施时间调整
            time = safe_str_lower(template.get("time", ""))
            if "立即" in time or "1周" in time:
                base_score += 0.1
            elif "月" in time:
                base_score -= 0.05
            
            # 基于问题严重程度调整
            severity = problem.get("severity", "medium")
            if severity in ["critical", "high"]:
                base_score += 0.1  # 严重问题提高可行性权重
            
            # 确保分数在合理范围内
            feasibility_score = max(0.0, min(1.0, base_score))
            
            return round(feasibility_score, 2)
            
        except Exception as e:
            self.logger.error(f"❌ 可行性分数计算失败: {e}")
            return 0.6
    
    async def _evaluate_suggestions_feasibility(self, 
                                              categorized_suggestions: Dict[SuggestionCategory, List[ImprovementSuggestion]],
                                              scene_analysis: Dict[str, Any]) -> Dict[SuggestionCategory, List[ImprovementSuggestion]]:
        """
        评估建议的可行性
        
        基于场景条件和实施环境，进一步评估建议的可行性。
        
        Args:
            categorized_suggestions: 分类的建议列表
            scene_analysis: 场景分析数据
            
        Returns:
            Dict[SuggestionCategory, List[ImprovementSuggestion]]: 可行性评估后的建议
        """
        try:
            feasible_suggestions = {}
            
            for category, suggestions in categorized_suggestions.items():
                feasible_list = []
                
                for suggestion in suggestions:
                    # 进一步评估可行性
                    adjusted_score = self._adjust_feasibility_score(
                        suggestion, scene_analysis
                    )
                    
                    # 更新可行性分数
                    suggestion.feasibility_score = adjusted_score
                    
                    # 只保留可行性达到阈值的建议
                    if adjusted_score >= self.min_feasibility_score:
                        feasible_list.append(suggestion)
                
                feasible_suggestions[category] = feasible_list
            
            return feasible_suggestions
            
        except Exception as e:
            self.logger.error(f"❌ 可行性评估失败: {e}")
            return categorized_suggestions
    
    def _adjust_feasibility_score(self, 
                                suggestion: ImprovementSuggestion,
                                scene_analysis: Dict[str, Any]) -> float:
        """
        基于场景条件调整可行性分数
        
        Args:
            suggestion: 改进建议
            scene_analysis: 场景分析数据
            
        Returns:
            float: 调整后的可行性分数
        """
        try:
            current_score = suggestion.feasibility_score
            
            # 基于道路类型调整
            road_env = scene_analysis.get("road_environment", {})
            road_type = safe_str_lower(road_env.get("road_type", ""))
            
            if suggestion.category == SuggestionCategory.INFRASTRUCTURE:
                if "高速公路" in road_type:
                    current_score -= 0.1  # 高速公路基础设施改造较困难
                elif "城市道路" in road_type:
                    current_score += 0.05  # 城市道路改造相对容易
            
            # 基于交通设施现状调整
            facilities = scene_analysis.get("traffic_facilities", {})
            
            if suggestion.category == SuggestionCategory.TECHNOLOGY:
                if "正常" in str(facilities):
                    current_score += 0.1  # 现有设施良好，技术升级更容易
                elif "故障" in str(facilities):
                    current_score += 0.15  # 设施故障，技术改进更迫切和可行
            
            # 基于环境条件调整
            environment = scene_analysis.get("environment", {})
            weather = safe_str_lower(environment.get("weather", ""))
            
            if "雨" in weather or "雪" in weather:
                if suggestion.category == SuggestionCategory.INFRASTRUCTURE:
                    current_score -= 0.05  # 恶劣天气影响施工
            
            # 确保分数在合理范围内
            adjusted_score = max(0.0, min(1.0, current_score))
            
            return round(adjusted_score, 2)
            
        except Exception as e:
            self.logger.error(f"❌ 可行性分数调整失败: {e}")
            return suggestion.feasibility_score
    
    async def _prioritize_suggestions(self, 
                                    feasible_suggestions: Dict[SuggestionCategory, List[ImprovementSuggestion]],
                                    problem_analysis: Dict[str, Any]) -> Dict[SuggestionCategory, List[ImprovementSuggestion]]:
        """
        对建议进行优先级排序
        
        Args:
            feasible_suggestions: 可行的建议列表
            problem_analysis: 问题分析结果
            
        Returns:
            Dict[SuggestionCategory, List[ImprovementSuggestion]]: 排序后的建议列表
        """
        try:
            prioritized_suggestions = {}
            
            for category, suggestions in feasible_suggestions.items():
                # 计算每个建议的综合优先级分数
                for suggestion in suggestions:
                    priority_score = self._calculate_priority_score(
                        suggestion, problem_analysis
                    )
                    # 临时存储优先级分数用于排序
                    suggestion._priority_score = priority_score
                
                # 按优先级分数排序
                suggestions.sort(key=lambda x: x._priority_score, reverse=True)
                
                # 清理临时分数
                for suggestion in suggestions:
                    delattr(suggestion, '_priority_score')
                
                prioritized_suggestions[category] = suggestions
            
            return prioritized_suggestions
            
        except Exception as e:
            self.logger.error(f"❌ 建议优先级排序失败: {e}")
            return feasible_suggestions
    
    def _calculate_priority_score(self, 
                                suggestion: ImprovementSuggestion,
                                problem_analysis: Dict[str, Any]) -> float:
        """
        计算建议的综合优先级分数
        
        Args:
            suggestion: 改进建议
            problem_analysis: 问题分析结果
            
        Returns:
            float: 优先级分数
        """
        try:
            # 基础优先级分数
            priority_mapping = {
                SuggestionPriority.URGENT: 1.0,
                SuggestionPriority.HIGH: 0.8,
                SuggestionPriority.MEDIUM: 0.6,
                SuggestionPriority.LOW: 0.4
            }
            
            base_score = priority_mapping.get(suggestion.priority, 0.6)
            
            # 安全影响权重
            safety_impact = base_score * self.priority_weights["safety_impact"]
            
            # 实施难易度权重（基于可行性分数）
            implementation_ease = suggestion.feasibility_score * self.priority_weights["implementation_ease"]
            
            # 成本效益权重
            cost_effectiveness = self._calculate_cost_effectiveness(suggestion) * self.priority_weights["cost_effectiveness"]
            
            # 紧急程度权重
            urgency = self._calculate_urgency_factor(suggestion, problem_analysis) * self.priority_weights["urgency"]
            
            # 综合优先级分数
            total_score = safety_impact + implementation_ease + cost_effectiveness + urgency
            
            return round(total_score, 3)
            
        except Exception as e:
            self.logger.error(f"❌ 优先级分数计算失败: {e}")
            return 0.5
    
    def _calculate_cost_effectiveness(self, suggestion: ImprovementSuggestion) -> float:
        """
        计算成本效益分数
        
        Args:
            suggestion: 改进建议
            
        Returns:
            float: 成本效益分数 (0-1)
        """
        try:
            cost = safe_str_lower(suggestion.estimated_cost)
            
            # 成本效益评估（成本越低，效益分数越高）
            if "很低" in cost:
                return 1.0
            elif "较低" in cost:
                return 0.8
            elif "中等" in cost:
                return 0.6
            elif "较高" in cost:
                return 0.4
            elif "高" in cost:
                return 0.2
            else:
                return 0.5  # 默认中等
                
        except Exception as e:
            self.logger.error(f"❌ 成本效益计算失败: {e}")
            return 0.5
    
    def _calculate_urgency_factor(self, 
                                suggestion: ImprovementSuggestion,
                                problem_analysis: Dict[str, Any]) -> float:
        """
        计算紧急程度因子
        
        Args:
            suggestion: 改进建议
            problem_analysis: 问题分析结果
            
        Returns:
            float: 紧急程度分数 (0-1)
        """
        try:
            urgency_factors = problem_analysis.get("urgency_factors", [])
            
            # 如果存在紧急因素，提高紧急程度
            if urgency_factors:
                return 0.9
            
            # 基于建议针对的问题类型评估紧急程度
            target_problems = suggestion.target_problems
            
            for problem in target_problems:
                problem_lower = safe_str_lower(problem)
                if any(urgent_keyword in problem_lower for urgent_keyword in 
                       ["故障", "危险", "严重", "urgent", "critical"]):
                    return 0.8
            
            return 0.5  # 默认中等紧急程度
            
        except Exception as e:
            self.logger.error(f"❌ 紧急程度计算失败: {e}")
            return 0.5
    
    def _generate_implementation_timeline(self, 
                                        prioritized_suggestions: Dict[SuggestionCategory, List[ImprovementSuggestion]]) -> Dict[str, List[str]]:
        """
        生成实施时间安排
        
        Args:
            prioritized_suggestions: 排序后的建议列表
            
        Returns:
            Dict[str, List[str]]: 时间安排
        """
        timeline = {
            "立即实施": [],
            "1周内": [],
            "1月内": [],
            "3月内": []
        }
        
        try:
            for category, suggestions in prioritized_suggestions.items():
                for suggestion in suggestions:
                    time_str = suggestion.implementation_time.lower()
                    
                    if "立即" in time_str:
                        timeline["立即实施"].append(suggestion.title)
                    elif "周" in time_str:
                        timeline["1周内"].append(suggestion.title)
                    elif "月" in time_str and "3" not in time_str:
                        timeline["1月内"].append(suggestion.title)
                    else:
                        timeline["3月内"].append(suggestion.title)
            
            return timeline
            
        except Exception as e:
            self.logger.error(f"❌ 实施时间安排生成失败: {e}")
            return timeline
    
    def _calculate_cost_estimation(self, 
                                 prioritized_suggestions: Dict[SuggestionCategory, List[ImprovementSuggestion]]) -> Dict[str, Any]:
        """
        计算成本估算汇总
        
        Args:
            prioritized_suggestions: 排序后的建议列表
            
        Returns:
            Dict[str, Any]: 成本估算汇总
        """
        cost_summary = {
            "total_categories": 0,
            "total_suggestions": 0,
            "cost_distribution": {},
            "budget_recommendation": ""
        }
        
        try:
            total_suggestions = 0
            cost_levels = {"很低": 0, "较低": 0, "中等": 0, "较高": 0, "高": 0}
            
            for category, suggestions in prioritized_suggestions.items():
                if suggestions:
                    cost_summary["total_categories"] += 1
                    total_suggestions += len(suggestions)
                    
                    # 统计各类成本级别
                    for suggestion in suggestions:
                        cost = safe_str_lower(suggestion.estimated_cost)
                        for level in cost_levels:
                            if level in cost:
                                cost_levels[level] += 1
                                break
            
            cost_summary["total_suggestions"] = total_suggestions
            cost_summary["cost_distribution"] = cost_levels
            
            # 生成预算建议
            if cost_levels["高"] > 0 or cost_levels["较高"] > 2:
                cost_summary["budget_recommendation"] = "建议分阶段实施，优先低成本高效益措施"
            elif cost_levels["很低"] + cost_levels["较低"] > total_suggestions * 0.7:
                cost_summary["budget_recommendation"] = "大部分措施成本较低，建议优先全面实施"
            else:
                cost_summary["budget_recommendation"] = "成本适中，建议按优先级有序实施"
            
            return cost_summary
            
        except Exception as e:
            self.logger.error(f"❌ 成本估算计算失败: {e}")
            return cost_summary
    
    def _format_suggestions_output(self, 
                                 prioritized_suggestions: Dict[SuggestionCategory, List[ImprovementSuggestion]]) -> Dict[str, Any]:
        """
        格式化建议输出
        
        Args:
            prioritized_suggestions: 排序后的建议列表
            
        Returns:
            Dict[str, Any]: 格式化的建议输出
        """
        formatted_output = {}
        
        try:
            for category, suggestions in prioritized_suggestions.items():
                category_name = {
                    SuggestionCategory.INFRASTRUCTURE: "基础设施改进",
                    SuggestionCategory.MANAGEMENT: "管理措施改进", 
                    SuggestionCategory.TECHNOLOGY: "技术手段改进",
                    SuggestionCategory.EDUCATION: "宣传教育改进",
                    SuggestionCategory.EMERGENCY: "应急预案改进"
                }.get(category, category.value)
                
                formatted_suggestions = []
                for suggestion in suggestions:
                    formatted_suggestion = {
                        "id": suggestion.suggestion_id,
                        "title": suggestion.title,
                        "description": suggestion.description,
                        "priority": suggestion.priority.value,
                        "target_problems": suggestion.target_problems,
                        "implementation_steps": suggestion.implementation_steps,
                        "estimated_cost": suggestion.estimated_cost,
                        "implementation_time": suggestion.implementation_time,
                        "expected_effect": suggestion.expected_effect,
                        "success_indicators": suggestion.success_indicators,
                        "responsible_party": suggestion.responsible_party,
                        "feasibility_score": suggestion.feasibility_score
                    }
                    formatted_suggestions.append(formatted_suggestion)
                
                formatted_output[category_name] = formatted_suggestions
            
            return formatted_output
            
        except Exception as e:
            self.logger.error(f"❌ 建议输出格式化失败: {e}")
            return {}
    
    def _generate_priority_summary(self, 
                                 prioritized_suggestions: Dict[SuggestionCategory, List[ImprovementSuggestion]]) -> Dict[str, Any]:
        """
        生成优先级汇总
        
        Args:
            prioritized_suggestions: 排序后的建议列表
            
        Returns:
            Dict[str, Any]: 优先级汇总
        """
        priority_summary = {
            "urgent": 0,
            "high": 0,
            "medium": 0,
            "low": 0,
            "top_priorities": []
        }
        
        try:
            all_suggestions = []
            
            # 收集所有建议并统计优先级
            for category, suggestions in prioritized_suggestions.items():
                for suggestion in suggestions:
                    all_suggestions.append(suggestion)
                    
                    priority = suggestion.priority.value
                    if priority in priority_summary:
                        priority_summary[priority] += 1
            
            # 选出最高优先级的建议
            all_suggestions.sort(key=lambda x: (
                {"urgent": 4, "high": 3, "medium": 2, "low": 1}.get(x.priority.value, 1),
                x.feasibility_score
            ), reverse=True)
            
            priority_summary["top_priorities"] = [
                {
                    "title": suggestion.title,
                    "priority": suggestion.priority.value,
                    "category": suggestion.category.value,
                    "feasibility": suggestion.feasibility_score
                }
                for suggestion in all_suggestions[:5]  # 前5个最高优先级
            ]
            
            return priority_summary
            
        except Exception as e:
            self.logger.error(f"❌ 优先级汇总生成失败: {e}")
            return priority_summary
    
    def get_generation_statistics(self) -> Dict[str, Any]:
        """
        获取生成统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "total_generations": self.generation_count,
            "total_suggestions_generated": self.total_suggestions_generated,
            "avg_suggestions_per_generation": round(
                self.total_suggestions_generated / max(self.generation_count, 1), 1
            ),
            "template_categories": len(self.suggestion_templates),
            "problem_solution_mappings": len(self.problem_solution_mapping),
            "min_feasibility_threshold": self.min_feasibility_score
        }