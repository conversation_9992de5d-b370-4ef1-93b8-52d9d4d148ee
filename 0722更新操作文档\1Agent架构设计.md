# Agent架构设计 - 基于LangChain的智能交通助手

## 🏗️ 整体架构概览

本项目采用**基于LangChain的多模态Agent架构**，实现从感知、理解、推理到行动的完整智能循环。

```
┌─────────────────────────────────────────────────────────────────┐
│                      用户交互层                                    │
├─────────────────────────────────────────────────────────────────┤
│  Streamlit Web界面  │  FastAPI接口  │  命令行界面             │
└─────────────────┬───┴─────────────┬─┴─────────────────────────┘
                  │                 │
┌─────────────────┴─────────────────┴─────────────────────────────┐
│                    LangChain Agent 核心                        │
├─────────────────────────────────────────────────────────────────┤
│  Agent执行器  │  工具管理器  │  记忆管理  │  推理链控制      │
└─────────────────┬───────────────┬─────────┬─────────────────┘
                  │               │         │
┌─────────────────┴───┐ ┌─────────┴──────┐ │ ┌─────────────────┐
│   多模态模型层      │ │    工具层      │ │ │   知识存储层    │
├─────────────────────┤ ├────────────────┤ │ ├─────────────────┤
│  Qwen2.5-VL-7B     │ │ 图像分析工具   │ │ │  ChromaDB       │
│  (Ollama本地部署)  │ │ 法规检索工具   │ │ │  向量知识库     │
│                     │ │ 安全评估工具   │ │ │  关系数据库     │
└─────────────────────┘ └────────────────┘ │ └─────────────────┘
                                           │
                        ┌─────────────────┴─────────────────┐
                        │            RAG系统                │
                        ├───────────────────────────────────┤
                        │ 检索器 │ 嵌入模型 │ 重排序器     │
                        └───────────────────────────────────┘
```

## 🤖 LangChain Agent核心设计

### 1. Agent执行器架构

```python
from langchain.agents import AgentExecutor, create_react_agent
from langchain.tools import Tool
from langchain.memory import ConversationBufferWindowMemory
from langchain.prompts import PromptTemplate

class TrafficMultimodalAgent:
    """智能交通多模态Agent"""
    
    def __init__(self):
        # 1. 初始化核心组件
        self.llm = self._init_multimodal_llm()
        self.tools = self._init_tools()
        self.memory = ConversationBufferWindowMemory(
            k=10, 
            memory_key="chat_history",
            return_messages=True
        )
        
        # 2. 创建Agent
        self.agent = create_react_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=self._create_agent_prompt()
        )
        
        # 3. 创建执行器
        self.agent_executor = AgentExecutor(
            agent=self.agent,
            tools=self.tools,
            memory=self.memory,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=5
        )
    
    def _init_multimodal_llm(self):
        """初始化多模态语言模型"""
        return OllamaMultimodalLLM(
            model="qwen2.5vl:7b",
            base_url="http://localhost:11434",
            temperature=0.1
        )
    
    def _init_tools(self):
        """初始化工具集"""
        return [
            Tool(
                name="analyze_traffic_image",
                func=self.traffic_image_analyzer.analyze,
                description="""
                分析交通场景图像，识别道路、车辆、行人、交通设施等元素。
                输入: 图像路径或base64编码
                输出: 详细的场景描述和元素识别结果
                使用场景: 当用户上传交通图像需要分析时使用
                """
            ),
            Tool(
                name="detect_traffic_violations",
                func=self.violation_detector.detect,
                description="""
                基于图像分析结果检测交通违规行为。
                输入: 图像分析结果
                输出: 违规行为列表，包含违规类型、法规引用、风险等级
                使用场景: 需要进行违规检测和安全评估时使用
                """
            ),
            Tool(
                name="search_traffic_regulations",
                func=self.regulation_searcher.search,
                description="""
                检索相关交通法规和案例。
                输入: 查询关键词或违规类型
                输出: 相关法规条文、执法案例、处罚标准
                使用场景: 需要法规解释或违规认定依据时使用
                """
            ),
            Tool(
                name="assess_safety_risk",
                func=self.safety_assessor.assess,
                description="""
                评估交通场景的安全风险等级。
                输入: 场景描述和违规检测结果
                输出: 安全风险等级(A-E)、风险因素分析、改进建议
                使用场景: 需要进行安全评估和风险预警时使用
                """
            ),
            Tool(
                name="generate_improvement_suggestions",
                func=self.suggestion_generator.generate,
                description="""
                基于分析结果生成改进建议。
                输入: 违规检测结果和安全评估结果
                输出: 具体的改进措施、实施建议、预期效果
                使用场景: 需要提供解决方案和改进建议时使用
                """
            )
        ]
    
    def _create_agent_prompt(self):
        """创建Agent提示词模板"""
        template = """你是一个专业的智能交通分析助手，能够理解文本和图像，并使用各种工具进行交通安全分析。

你有以下能力:
1. 分析交通场景图像，识别各种交通元素
2. 检测交通违规行为，引用相关法规
3. 评估安全风险，提供专业建议
4. 检索交通法规和相关案例

工作流程:
1. 理解用户的问题和需求
2. 如果有图像，先使用analyze_traffic_image工具分析
3. 根据分析结果，使用相应工具进行深入分析
4. 整合所有信息，提供专业、准确的回答

注意事项:
- 始终基于事实和法规进行分析
- 提供具体的法规条文引用
- 给出可操作的改进建议
- 保持专业和客观的语调

可用工具: {tools}
工具名称: {tool_names}

使用格式:
Thought: 我需要思考如何解决这个问题
Action: [工具名称]
Action Input: [工具输入]
Observation: [工具输出]
... (可能重复多次)
Thought: 我现在知道最终答案了
Final Answer: [最终回答]

对话历史: {chat_history}

用户问题: {input}
{agent_scratchpad}"""
        
        return PromptTemplate.from_template(template)
    
    async def process_query(self, query: str, image_path: str = None):
        """处理用户查询"""
        try:
            # 构建输入
            input_data = {"input": query}
            if image_path:
                input_data["image_path"] = image_path
            
            # 执行Agent
            result = await self.agent_executor.ainvoke(input_data)
            
            return {
                "answer": result["output"],
                "reasoning_steps": self._extract_reasoning_steps(result),
                "tools_used": self._extract_tools_used(result),
                "success": True
            }
            
        except Exception as e:
            return {
                "answer": f"处理过程中出现错误: {str(e)}",
                "success": False,
                "error": str(e)
            }
```

### 2. 工具系统详细设计

#### 2.1 图像分析工具
```python
class TrafficImageAnalyzer:
    """交通图像分析工具"""
    
    def __init__(self, ollama_client):
        self.ollama_client = ollama_client
        
    async def analyze(self, image_input):
        """分析交通图像"""
        prompt = """请仔细观察这张交通场景图片，进行客观详细的描述:

## 道路环境分析
- 道路类型: [城市道路/高速公路/乡村道路/停车场等]
- 车道配置: [车道数量、车道宽度、分隔线类型]
- 路面状况: [干燥/湿滑/有积水/路面损坏等]
- 环境条件: [白天/夜晚/晴天/雨天/雾天等]

## 交通参与者识别
- 机动车: [数量、类型(轿车/货车/公交等)、颜色、具体位置]
- 非机动车: [电动车/自行车/摩托车的数量和位置]
- 行人: [数量、位置、行为状态(行走/等待/横穿等)]

## 交通设施状态
- 交通信号灯: [红绿灯状态，如可见]
- 交通标志: [限速/禁行/导向等标志内容和位置]
- 道路标线: [实线/虚线/人行横道/停止线等]
- 其他设施: [护栏/隔离带/监控设备等]

## 关键空间关系
- 各车辆相对于车道标线的精确位置
- 行人相对于人行道、人行横道的位置  
- 交通参与者之间的距离和相对位置

请只描述客观可见的事实，不做违规判断。"""

        result = await self.ollama_client.generate(
            prompt=prompt,
            images=[image_input],
            model="qwen2.5vl:7b"
        )
        
        return self._parse_analysis_result(result)
```

#### 2.2 违规检测工具
```python
class TrafficViolationDetector:
    """交通违规检测工具"""
    
    def __init__(self, regulation_db):
        self.regulation_db = regulation_db
        self.violation_patterns = self._load_violation_patterns()
    
    async def detect(self, image_analysis_result):
        """检测交通违规行为"""
        violations = []
        
        # 检查常见违规模式
        for pattern_name, pattern_config in self.violation_patterns.items():
            if self._match_pattern(image_analysis_result, pattern_config):
                violation = await self._create_violation_record(
                    pattern_name, 
                    pattern_config, 
                    image_analysis_result
                )
                violations.append(violation)
        
        return violations
    
    def _load_violation_patterns(self):
        """加载违规检测模式"""
        return {
            "lane_violation": {
                "description": "非机动车占用机动车道",
                "indicators": ["电动车", "自行车", "机动车道", "占用"],
                "legal_reference": "《道路交通安全法》第57条",
                "severity": "medium",
                "detection_method": "spatial_analysis"
            },
            "pedestrian_violation": {
                "description": "机动车未礼让行人",
                "indicators": ["人行横道", "行人", "机动车", "未让行"],
                "legal_reference": "《道路交通安全法》第47条", 
                "severity": "high",
                "detection_method": "interaction_analysis"
            },
            "signal_violation": {
                "description": "违反交通信号",
                "indicators": ["红灯", "闯红灯", "信号灯"],
                "legal_reference": "《道路交通安全法》第38条",
                "severity": "high", 
                "detection_method": "signal_analysis"
            },
            "parking_violation": {
                "description": "违法停车",
                "indicators": ["停车", "禁停", "人行道", "黄线"],
                "legal_reference": "《道路交通安全法》第56条",
                "severity": "medium",
                "detection_method": "position_analysis"
            }
        }
```

#### 2.3 法规检索工具
```python
class TrafficRegulationSearcher:
    """交通法规检索工具"""
    
    def __init__(self, vector_store, embeddings):
        self.vector_store = vector_store
        self.embeddings = embeddings
        
    async def search(self, query, violation_type=None, top_k=5):
        """搜索相关法规"""
        # 构建检索查询
        search_queries = [query]
        if violation_type:
            search_queries.append(f"{violation_type} 相关法规")
        
        results = []
        for search_query in search_queries:
            # 向量检索
            vector_results = await self.vector_store.similarity_search(
                query=search_query,
                k=top_k
            )
            results.extend(vector_results)
        
        # 去重和重排序
        unique_results = self._deduplicate_results(results)
        ranked_results = await self._rerank_results(query, unique_results)
        
        return self._format_regulation_results(ranked_results)
```

#### 2.4 安全评估工具
```python
class TrafficSafetyAssessor:
    """交通安全评估工具"""
    
    def __init__(self):
        self.risk_factors = self._init_risk_factors()
        
    async def assess(self, scene_analysis, violations):
        """评估安全风险"""
        risk_score = 0
        risk_factors = []
        
        # 基于违规行为计算风险
        for violation in violations:
            severity_weight = {
                "low": 1, "medium": 2, "high": 3
            }
            risk_score += severity_weight.get(violation.get("severity", "medium"), 2)
            risk_factors.append(f"违规行为: {violation['description']}")
        
        # 基于环境因素调整风险
        environmental_risks = self._assess_environmental_risks(scene_analysis)
        risk_score += environmental_risks["score"]
        risk_factors.extend(environmental_risks["factors"])
        
        # 计算最终风险等级
        risk_grade = self._calculate_risk_grade(risk_score)
        
        return {
            "risk_grade": risk_grade,
            "risk_score": risk_score,
            "risk_factors": risk_factors,
            "assessment_details": self._generate_assessment_details(
                risk_grade, risk_factors
            )
        }
```

## 🔄 Agent工作流程

### 标准工作流程
```
1. 用户输入 (文本 + 可选图像)
     ↓
2. Agent理解需求 (ReAct推理开始)
     ↓
3. 工具选择与执行
   3.1 如有图像 → 调用图像分析工具
   3.2 基于分析结果 → 调用违规检测工具  
   3.3 检索相关法规 → 调用法规检索工具
   3.4 评估安全风险 → 调用安全评估工具
   3.5 生成改进建议 → 调用建议生成工具
     ↓
4. 结果整合与生成
     ↓
5. 返回最终答案
```

### ReAct推理示例
```
用户: "请分析这个路口的交通安全问题" [上传图像]

Thought: 用户上传了一张路口图像，需要分析交通安全问题。我应该先分析图像内容。

Action: analyze_traffic_image
Action Input: [图像数据]

Observation: 分析结果显示这是一个城市十字路口，有电动车在机动车道行驶，行人在人行横道通过...

Thought: 图像分析完成，发现了一些可能的违规行为。现在需要进行违规检测。

Action: detect_traffic_violations  
Action Input: [图像分析结果]

Observation: 检测到违规: 1. 非机动车占用机动车道(违反第57条) 2. 部分机动车未完全礼让行人...

Thought: 检测到了违规行为，需要检索相关法规条文获取详细信息。

Action: search_traffic_regulations
Action Input: "非机动车道使用 机动车礼让行人"

Observation: 找到相关法规: 《道路交通安全法》第57条规定非机动车应在非机动车道内行驶...

Thought: 有了违规信息和法规依据，现在评估整体安全风险。

Action: assess_safety_risk
Action Input: [场景描述+违规检测结果]

Observation: 安全风险等级: C级(一般)，主要风险因素: 车道使用不当、人车冲突潜在风险...

Thought: 现在我有了完整的分析结果，可以生成改进建议并给出最终答案。

Final Answer: 基于分析，这个路口存在以下安全问题...[详细回答]
```

## 🧠 多模态集成方案

### Qwen2.5-VL-7B集成
```python
class OllamaMultimodalLLM(BaseLLM):
    """Ollama多模态语言模型封装"""
    
    def __init__(self, model="qwen2.5vl:7b", **kwargs):
        self.model = model
        self.base_url = kwargs.get("base_url", "http://localhost:11434")
        self.client = OllamaClient(base_url=self.base_url)
        
    async def _agenerate(self, messages, images=None, **kwargs):
        """异步生成响应"""
        try:
            response = await self.client.chat(
                model=self.model,
                messages=self._format_messages(messages, images),
                stream=False,
                **kwargs
            )
            return LLMResult(
                generations=[[Generation(text=response["message"]["content"])]],
                llm_output={"model": self.model}
            )
        except Exception as e:
            raise LLMException(f"Ollama调用失败: {str(e)}")
    
    def _format_messages(self, messages, images=None):
        """格式化消息"""
        formatted_messages = []
        for message in messages:
            formatted_message = {
                "role": "user" if isinstance(message, HumanMessage) else "assistant",
                "content": message.content
            }
            if images and isinstance(message, HumanMessage):
                formatted_message["images"] = images
            formatted_messages.append(formatted_message)
        return formatted_messages
```

## 📊 性能优化策略

### 1. 缓存机制
```python
class AgentCacheManager:
    """Agent缓存管理"""
    
    def __init__(self):
        self.image_cache = LRUCache(maxsize=100)
        self.regulation_cache = LRUCache(maxsize=500)
        
    def cache_image_analysis(self, image_hash, result):
        """缓存图像分析结果"""
        self.image_cache[image_hash] = result
        
    def get_cached_image_analysis(self, image_hash):
        """获取缓存的图像分析"""
        return self.image_cache.get(image_hash)
```

### 2. 异步并发
```python
class AsyncAgentExecutor:
    """异步Agent执行器"""
    
    async def batch_process(self, queries):
        """批量处理查询"""
        tasks = [self.process_single_query(query) for query in queries]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return results
```

## 📈 监控与日志

### Agent执行监控
```python
class AgentMonitor:
    """Agent执行监控"""
    
    def __init__(self):
        self.metrics = {
            "total_queries": 0,
            "successful_queries": 0, 
            "tool_usage_stats": defaultdict(int),
            "average_response_time": 0
        }
    
    def log_execution(self, query, result, execution_time, tools_used):
        """记录执行情况"""
        self.metrics["total_queries"] += 1
        if result.get("success"):
            self.metrics["successful_queries"] += 1
        
        for tool in tools_used:
            self.metrics["tool_usage_stats"][tool] += 1
            
        # 更新平均响应时间
        self.metrics["average_response_time"] = (
            (self.metrics["average_response_time"] * (self.metrics["total_queries"] - 1) + execution_time) 
            / self.metrics["total_queries"]
        )
```

---

**下一步**: 查看完整实操指南，了解如何一步步实现这个Agent系统。