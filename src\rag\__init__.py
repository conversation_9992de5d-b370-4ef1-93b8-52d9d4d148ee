# -*- coding: utf-8 -*-
"""
RAG模块 - Retrieval-Augmented Generation Module

这个模块实现了智能交通领域的检索增强生成（RAG）系统。
主要功能包括：

1. 交通知识库管理 (TrafficKnowledgeBase)
   - 基于ChromaDB构建的向量知识库
   - 存储交通法规、技术标准、案例数据等专业知识
   - 支持多集合分类存储和管理

2. 向量嵌入服务 (EmbeddingService)
   - 使用BGE-large-zh-v1.5中文嵌入模型
   - 将文本转换为高质量的向量表示
   - 优化检索相关性和语义理解

3. 高级检索器 (AdvancedRetriever)
   - 实现多阶段检索策略（初检索->重排序->后处理）
   - 支持查询扩展、意图识别、分类过滤
   - 融合向量检索和关键词检索的混合策略

4. 查询处理器 (QueryProcessor)
   - 智能理解和预处理用户查询
   - 检测查询类型和相关违规类别
   - 生成扩展查询以提高检索覆盖率

5. 上下文构建器 (ContextBuilder)
   - 将检索结果整合为结构化上下文
   - 根据查询类型选择合适的模板
   - 优化上下文质量以提升生成效果

6. 知识更新服务 (KnowledgeUpdateService)
   - 支持实时知识库更新和扩展
   - 监控法规变化和政策更新
   - 保持知识库的时效性和准确性

RAG系统为LangChain Agent提供了强大的领域知识支撑，确保生成内容的专业性和准确性。
"""