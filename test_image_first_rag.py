#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图像先行RAG架构
演示优化后的流程：图像预分析 → 精准RAG检索 → Agent推理
"""

import asyncio
import logging
from pathlib import Path
import sys

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from src.agent.traffic_rag_agent import create_traffic_rag_agent
from src.utils.logger import get_logger

logger = get_logger(__name__)

async def test_image_first_rag():
    """测试图像先行RAG架构的完整流程"""
    print("🚀 测试图像先行RAG架构优化")
    print("=" * 80)
    
    try:
        # 创建RAG增强Agent
        agent = create_traffic_rag_agent(enable_rag=True)
        
        print("\n📊 1. 检查RAG系统状态")
        print("-" * 50)
        rag_status = agent.get_rag_status()
        print(f"RAG状态: {rag_status}")
        
        # 测试用例：演示用户提供的场景
        test_cases = [
            {
                "name": "图像先行RAG测试 - 人行横道违规",
                "user_input": "分析这个路口的交通违规行为，给出法规依据和改进建议",
                # 这里是修改后的部分
                "image_path": "/home/<USER>/lhp/projects/0714agent/my-agent1/data/test_images/截图 2025-07-25 21-28-40.png",  # 模拟场景
                "expected_scene": "人行横道 + 机动车 + 行人 + 未礼让",
                "expected_rag": "机动车未礼让行人相关法规"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🧪 {i}. {test_case['name']}")
            print("-" * 70)
            print(f"用户输入: {test_case['user_input']}")
            print(f"期望场景识别: {test_case['expected_scene']}")
            print(f"期望RAG检索: {test_case['expected_rag']}")
            print()
            
            # 如果没有真实图像，我们可以测试纯文本的RAG检索优化
            try:
                # 测试RAG知识检索（基于用户输入）
                print("🔍 测试RAG知识检索能力:")
                rag_test_result = await agent.test_rag_knowledge(test_case['user_input'])
                print(f"   检索到 {rag_test_result['knowledge_count']} 条相关知识")
                
                if rag_test_result['knowledge_results']:
                    for j, knowledge in enumerate(rag_test_result['knowledge_results'][:2], 1):
                        content = knowledge.get('content', '')[:100]
                        print(f"   {j}. {content}...")
                
                print()
                
                # 测试完整的process_query流程
                print("🎯 测试完整Agent处理流程:")
                response = await agent.process_query(
                    user_input=test_case['user_input'],
                    image_path=test_case['image_path']  # None表示没有图像
                )
                
                print("📋 Agent回复:")
                print(response[:500] + "..." if len(response) > 500 else response)
                print()
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                import traceback
                traceback.print_exc()
                
        # 额外测试：验证图像预分析功能
        print("\n🖼️ 3. 测试图像预分析功能架构")
        print("-" * 50)
        print("注意: 由于没有真实图像文件，以下展示架构设计逻辑")
        
        # 模拟展示图像预分析的设计思路
        mock_scene_analysis = {
            "description": "城市道路人行横道场景，机动车接近行人正在过街，车辆未停车让行",
            "traffic_elements": {
                "vehicles": ["白色小汽车"],
                "pedestrians": ["行人1", "行人2"]
            }
        }
        
        print("模拟图像分析结果:")
        print(f"  - 场景描述: {mock_scene_analysis['description']}")
        print(f"  - 交通参与者: {mock_scene_analysis['traffic_elements']}")
        
        # 演示场景关键词提取
        mock_keywords = agent._extract_scene_keywords(mock_scene_analysis)
        print(f"  - 提取的关键词: {mock_keywords}")
        
        # 演示增强查询构建
        enhanced_query = agent._build_enhanced_query("分析违规行为", mock_keywords)
        print(f"  - 增强查询: {enhanced_query}")
        
        print("\n✅ 图像先行RAG架构测试完成!")
        print("\n📈 优化效果预期:")
        print("  1. 图像预分析提供精确的场景上下文")
        print("  2. 增强查询提高RAG检索的精准度")
        print("  3. 结合场景和知识的综合分析更专业")
        print("  4. 法规引用更准确，建议更有针对性")
        
    except Exception as e:
        logger.error(f"测试过程出现错误: {e}")
        import traceback
        traceback.print_exc()

async def test_rag_retrieval_comparison():
    """对比测试：优化前后的RAG检索效果"""
    print("\n🔬 对比测试：图像先行 vs 传统RAG检索")
    print("=" * 80)
    
    agent = create_traffic_rag_agent(enable_rag=True)
    
    # 测试查询
    base_query = "分析交通违规行为"
    enhanced_query = "分析交通违规行为 场景识别: 人行横道, 机动车, 行人, 未礼让行人"
    
    print(f"传统查询: {base_query}")
    result1 = await agent.test_rag_knowledge(base_query)
    print(f"检索结果数: {result1['knowledge_count']}")
    if result1['knowledge_results']:
        for i, r in enumerate(result1['knowledge_results'][:2], 1):
            print(f"  {i}. {r.get('content', '')[:80]}...")
    
    print(f"\n优化查询: {enhanced_query}")
    result2 = await agent.test_rag_knowledge(enhanced_query)
    print(f"检索结果数: {result2['knowledge_count']}")
    if result2['knowledge_results']:
        for i, r in enumerate(result2['knowledge_results'][:2], 1):
            print(f"  {i}. {r.get('content', '')[:80]}...")
    
    print("\n💡 对比分析:")
    print("  - 传统方式: 基于泛化查询，可能检索到不相关内容")
    print("  - 优化方式: 基于具体场景，检索结果更精准匹配")

if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_image_first_rag())
    asyncio.run(test_rag_retrieval_comparison())