# -*- coding: utf-8 -*-
"""
日志工具模块
"""
import logging
import logging.config
from pathlib import Path
from config.settings import LOGGING_CONFIG, PROJECT_ROOT

def setup_logger(name: str = None) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        配置好的日志记录器
    """
    # 确保日志目录存在
    log_dir = PROJECT_ROOT / "logs"
    log_dir.mkdir(exist_ok=True)
    
    # 配置日志
    logging.config.dictConfig(LOGGING_CONFIG)
    
    # 返回日志记录器
    return logging.getLogger(name)

def get_logger(name: str) -> logging.Logger:
    """
    获取日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        日志记录器
    """
    return logging.getLogger(name)