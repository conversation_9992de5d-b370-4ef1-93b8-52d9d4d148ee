# Docker忽略文件
# 用于指定在构建Docker镜像时需要忽略的文件和目录

# Python编译文件和缓存
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 版本控制
.git/
.gitignore
.gitattributes

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
tmp/
temp/

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 项目特定忽略
data/knowledge_base/
data/test_images/
data/models/
*.ipynb_checkpoints/

# 文档和说明
README.md
docs/
0722更新操作文档/
操作文档/

# 测试相关
.pytest_cache/
.coverage
htmlcov/
.tox/

# 环境配置
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Node.js (如果有前端构建)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*