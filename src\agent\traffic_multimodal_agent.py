# -*- coding: utf-8 -*-
"""
交通多模态Agent - Traffic Multimodal Agent

这是系统的核心Agent类，负责协调多个专业工具来分析交通场景。
主要功能：

1. 多模态输入处理
   - 同时处理文本查询和交通图像
   - 智能理解用户意图和分析需求

2. 工具协调管理
   - 图像分析工具：解析交通场景元素
   - 违规检测工具：识别潜在违规行为
   - 法规检索工具：查找相关交通法规
   - 安全评估工具：评估场景安全等级
   - 建议生成工具：提供改进建议

3. 推理链管理
   - 基于LangChain的智能推理流程
   - 动态选择和组合工具调用
   - 对话历史记忆管理

4. 结果整合输出
   - 综合多工具分析结果
   - 生成结构化的专业报告
   - 提供法规引用和改进建议

使用示例：
    agent = TrafficMultimodalAgent()
    result = await agent.process_query(
        query="这个路口是否存在违规行为？",
        image_path="traffic_scene.jpg"
    )
"""

import asyncio
import io
from typing import Dict, List, Optional, Any, Tuple

from langchain.agents import AgentExecutor, create_react_agent
from langchain.memory import ConversationBufferWindowMemory
from langchain.tools import Tool
from langchain_core.prompts import PromptTemplate

from config.settings import AGENT_CONFIG, OLLAMA_CONFIG
from src.utils.logger import get_logger
from src.utils.ollama_client import OllamaMultimodalLLM
from src.utils.image_processor import ImageProcessor
from src.tools.traffic_image_analyzer import TrafficImageAnalyzer
from src.tools.traffic_violation_detector import TrafficViolationDetector
# 移除冗余的RAG工具导入 - RAG功能已由TrafficRAGAgent在上层处理
from src.tools.traffic_safety_assessor import TrafficSafetyAssessor
from src.tools.traffic_suggestion_generator import TrafficSuggestionGenerator

logger = get_logger(__name__)

class TrafficMultimodalAgent:
    """
    交通多模态智能Agent
    
    基于LangChain框架构建的专业交通分析Agent，能够：
    - 处理多模态输入（文本+图像）
    - 协调多个专业分析工具
    - 生成结构化的分析报告
    - 提供法规依据和改进建议
    """
    
    def __init__(self):
        """
        初始化交通多模态Agent
        
        包括：
        - LLM模型初始化
        - 专业工具加载
        - 对话内存配置
        - Agent执行器创建
        """
        self.logger = get_logger(self.__class__.__name__)
        self.image_processor = ImageProcessor()
        
        # 初始化标志
        self._initialized = False
        
        # 初始化组件
        self.llm = None
        self.tools = []
        self.memory = None
        self.agent_executor = None
        
        # 初始化专业分析工具实例
        self.image_analyzer = None
        self.violation_detector = None
        # regulation_searcher已移除 - RAG功能由上层TrafficRAGAgent处理
        self.safety_assessor = None
        self.suggestion_generator = None
    
    async def initialize(self):
        """异步初始化Agent组件"""
        if self._initialized:
            return
            
        try:
            self.logger.info("🤖 开始初始化交通多模态Agent...")
            
            # 1. 初始化LLM
            await self._initialize_llm()
            
            # 2. 初始化专业分析工具实例
            self._initialize_analysis_tools()
            
            # 3. 加载专业工具
            self._load_tools()
            
            # 4. 配置内存管理
            self._setup_memory()
            
            # 5. 创建Agent执行器
            self._create_agent_executor()
            
            self._initialized = True
            self.logger.info("✅ 交通多模态Agent初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ Agent初始化失败: {str(e)}")
            raise e
    
    async def _initialize_llm(self):
        """初始化多模态LLM"""
        self.logger.info("🧠 初始化Qwen2.5-VL多模态模型...")
        
        self.llm = OllamaMultimodalLLM(
            base_url=OLLAMA_CONFIG["base_url"],
            model_name=OLLAMA_CONFIG["model_name"],
            temperature=OLLAMA_CONFIG["temperature"],
            max_tokens=OLLAMA_CONFIG["max_tokens"]
        )
        
        # 验证模型可用性
        model_available = await self.llm.check_model_availability()
        if not model_available:
            raise Exception(f"模型 {OLLAMA_CONFIG['model_name']} 不可用")
        
        self.logger.info(f"✅ 模型 {OLLAMA_CONFIG['model_name']} 加载成功")
    
    def _initialize_analysis_tools(self):
        """初始化专业分析工具实例"""
        self.logger.info("🔧 初始化专业分析工具实例...")
        
        try:
            # 初始化图像分析工具
            self.image_analyzer = TrafficImageAnalyzer()
            self.logger.debug("✅ TrafficImageAnalyzer 初始化完成")
            
            # 初始化违规检测工具
            self.violation_detector = TrafficViolationDetector()
            self.logger.debug("✅ TrafficViolationDetector 初始化完成")
            
            # 法规检索工具已移除 - RAG功能由上层TrafficRAGAgent统一处理
            
            # 初始化安全评估工具
            self.safety_assessor = TrafficSafetyAssessor()
            self.logger.debug("✅ TrafficSafetyAssessor 初始化完成")
            
            # 初始化建议生成工具
            self.suggestion_generator = TrafficSuggestionGenerator()
            self.logger.debug("✅ TrafficSuggestionGenerator 初始化完成")
            
            self.logger.info("✅ 所有专业分析工具实例初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 分析工具初始化失败: {e}")
            raise e
    
    def _load_tools(self):
        """加载专业分析工具"""
        self.logger.info("🔧 加载交通分析专业工具...")
        
        # 创建5个核心工具
        self.tools = [
            self._create_image_analysis_tool(),
            self._create_violation_detection_tool(),
            # 法规检索工具已移除 - RAG功能由上层TrafficRAGAgent统一处理
            self._create_safety_assessment_tool(),
            self._create_suggestion_generation_tool()
        ]
        
        self.logger.info(f"✅ 已加载 {len(self.tools)} 个专业工具")
    
    def _create_image_analysis_tool(self) -> Tool:
        """创建图像分析工具"""
        return Tool(
            name="analyze_traffic_image",
            description="""
            分析交通场景图像的专业工具。
            
            功能：识别道路类型、车道配置、交通标志标线、交通参与者、环境条件等。
            输入：图像描述或"请分析当前图像"
            输出：结构化的场景分析结果
            
            使用场景：当需要理解交通图像内容时调用此工具
            """,
            func=lambda x: asyncio.run(self._analyze_traffic_image(x))
        )
    
    def _create_violation_detection_tool(self) -> Tool:
        """创建违规检测工具"""
        return Tool(
            name="detect_traffic_violations",
            description="""
            检测交通违规行为的专业工具。
            
            功能：基于场景分析识别车道违规、信号违规、停车违规等行为。
            输入：场景描述或分析结果
            输出：检测到的违规行为列表，包含法规依据
            
            使用场景：需要识别交通违规行为时调用此工具
            """,
            func=lambda x: asyncio.run(self._detect_violations(x))
        )
    
    # _create_regulation_search_tool 方法已移除
    # RAG功能现在由上层TrafficRAGAgent统一处理，架构更加清晰
    
    def _create_safety_assessment_tool(self) -> Tool:
        """创建安全评估工具"""
        return Tool(
            name="assess_safety_risk",
            description="""
            评估交通场景安全风险的专业工具。
            
            功能：综合分析安全状况，计算风险等级（A-E级），识别风险因素。
            输入：场景分析结果和违规检测结果
            输出：安全等级评估、风险因素分析、预警建议
            
            使用场景：需要评估交通安全状况时调用此工具
            """,
            func=lambda x: asyncio.run(self._assess_safety(x))
        )
    
    def _create_suggestion_generation_tool(self) -> Tool:
        """创建建议生成工具"""
        return Tool(
            name="generate_improvement_suggestions",
            description="""
            生成交通改进建议的专业工具。
            
            功能：基于分析结果生成具体改进措施、管理建议、宣传教育建议。
            输入：综合分析结果和安全评估
            输出：分类的改进建议列表，包含实施指导
            
            使用场景：需要提供改进建议时调用此工具
            """,
            func=lambda x: asyncio.run(self._generate_suggestions(x))
        )
    
    def _setup_memory(self):
        """配置内存管理"""
        self.logger.info("🧠 配置Agent对话内存...")
        
        # 使用简单的对话窗口内存（RAG的向量内存在Phase 3实现）
        self.memory = ConversationBufferWindowMemory(
            k=AGENT_CONFIG["memory_window"],
            memory_key=AGENT_CONFIG["memory_key"],
            return_messages=True,
            output_key="output"
        )
        
        self.logger.info(f"✅ 对话内存窗口大小: {AGENT_CONFIG['memory_window']}")
    
    def _create_agent_executor(self):
        """创建Agent执行器"""
        self.logger.info("⚙️ 创建Agent执行器...")
        
        # 定义ReAct提示模板
        prompt_template = self._get_react_prompt_template()
        
        # 创建ReAct Agent
        agent = create_react_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=prompt_template
        )
        
        # 创建执行器
        self.agent_executor = AgentExecutor(
            agent=agent,
            tools=self.tools,
            memory=self.memory,
            max_iterations=AGENT_CONFIG["max_iterations"],
            early_stopping_method=AGENT_CONFIG["early_stopping_method"],
            verbose=AGENT_CONFIG["verbose"],
            handle_parsing_errors=AGENT_CONFIG["handle_parsing_errors"]
        )
        
        self.logger.info("✅ Agent执行器创建完成")
    
    def _get_react_prompt_template(self) -> PromptTemplate:
        """获取ReAct推理提示模板"""
        template = """
你是一个专业的交通安全分析专家，具备以下能力：

## 专业背景
- 精通中国道路交通安全法律法规
- 熟悉交通工程和安全管理
- 具备多模态交通场景分析能力
- 能够提供专业的改进建议

## 分析原则
1. 客观准确：基于事实进行分析，不主观臆断
2. 专业严谨：引用准确的法规条文和技术标准
3. 实用性强：提供可操作的改进建议
4. 安全优先：始终以交通安全为首要考虑

## 工作流程
你已经获得了完整的交通场景数据，请按以下步骤进行专业分析：

**重要**：场景数据已经预先提供，无需使用图像分析工具。请直接基于场景描述进行分析。

1. 违规识别：使用 detect_traffic_violations 工具识别交通违规行为
2. 安全评估：使用 assess_safety_risk 工具评估风险等级  
3. 建议生成：使用 generate_improvement_suggestions 工具提供改进措施

注意：法规知识由RAG系统自动提供，无需手动查询工具

**注意**：请避免调用 analyze_traffic_image 工具，因为场景信息已经结构化提供。

## 可用工具
{tools}

## 工具使用格式
使用以下格式进行推理：

Thought: 我需要分析这个交通问题，首先确定需要使用哪些工具
Action: [工具名称]
Action Input: [工具输入]
Observation: [工具输出结果]
... (重复思考-行动-观察过程)
Thought: 我现在了解了情况，可以给出最终答案
Final Answer: [综合分析结果]

## 输出要求
最终答案应包含：
- 场景描述
- 发现的问题（如有）
- 相关法规依据
- 安全等级评估  
- 具体改进建议

## 当前对话历史
{chat_history}

## 用户问题
{input}

## 推理过程
{agent_scratchpad}
"""
        
        return PromptTemplate(
            template=template,
            input_variables=["input", "chat_history", "agent_scratchpad"],
            partial_variables={
                "tools": self._format_tools(),
                "tool_names": self._get_tool_names()
            }
        )
    
    def _format_tools(self) -> str:
        """格式化工具描述"""
        tool_descriptions = []
        for tool in self.tools:
            tool_descriptions.append(f"- {tool.name}: {tool.description.strip()}")
        return "\n".join(tool_descriptions)
    
    def _get_tool_names(self) -> str:
        """获取工具名称列表"""
        return ", ".join([tool.name for tool in self.tools])
    
    async def process_query(
        self,
        query: str,
        image_path: Optional[str] = None,
        image_base64: Optional[str] = None,
        analysis_options: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        处理用户查询的主要接口
        
        Args:
            query: 用户的文本查询
            image_path: 交通图像文件路径
            image_base64: 图像的base64编码
            analysis_options: 分析选项配置
            
        Returns:
            包含分析结果的字典
        """
        try:
            # 确保Agent已初始化
            if not self._initialized:
                await self.initialize()
            
            self.logger.info(f"🔍 开始处理查询: {query[:50]}...")
            
            # 构建输入
            input_data = await self._prepare_input(
                query, image_path, image_base64, analysis_options
            )
            
            # 执行分析
            result = await self.agent_executor.ainvoke(input_data)
            
            # 处理结果
            processed_result = self._process_result(result)
            
            self.logger.info("✅ 查询处理完成")
            return processed_result
            
        except Exception as e:
            self.logger.error(f"❌ 查询处理失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "查询处理过程中发生错误，请检查输入或重试"
            }
    
    async def _prepare_input(
        self,
        query: str,
        image_path: Optional[str] = None,
        image_base64: Optional[str] = None,
        analysis_options: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """准备Agent输入"""
        input_data = {"input": query}
        
        # 处理图像输入
        if image_path or image_base64:
            image_info = await self._process_image_input(image_path, image_base64)
            if image_info["success"]:
                # 将图像信息添加到查询中
                input_data["input"] += f"\n\n[系统提示: 用户上传了一张图像，尺寸: {image_info.get('width', 0)}x{image_info.get('height', 0)}, 质量: {image_info.get('quality', '未知')}。请使用图像分析工具分析这张交通场景图像。]"
                # 存储图像数据供工具使用 - 设置为实例属性
                self._current_image_data = image_info
            else:
                # 图像处理失败时的处理
                input_data["input"] += f"\n\n[系统提示: 图像处理失败 - {image_info.get('error', '未知错误')}]"
                self._current_image_data = None
        else:
            self._current_image_data = None
        
        # 添加分析选项
        if analysis_options:
            input_data["_analysis_options"] = analysis_options
            
        return input_data
    
    async def _process_image_input(
        self,
        image_path: Optional[str] = None,
        image_base64: Optional[str] = None
    ) -> Dict[str, Any]:
        """处理图像输入"""
        try:
            if image_path:
                # 处理图像文件 - 修复中文文件名编码问题
                with open(image_path, 'rb') as f:
                    # 读取文件内容到BytesIO对象，避免文件名编码问题
                    image_data = f.read()
                    image_file = io.BytesIO(image_data)
                    result = self.image_processor.process_uploaded_image(image_file)
            elif image_base64:
                # 处理base64图像
                import base64
                image_data = base64.b64decode(image_base64)
                image_file = io.BytesIO(image_data)
                result = self.image_processor.process_uploaded_image(image_file)
            else:
                return {"success": False, "error": "没有提供图像数据"}
            
            if result["success"]:
                return {
                    "success": True,
                    "width": result['image_info']['width'],
                    "height": result['image_info']['height'],
                    "quality": result['quality_assessment']['level'],
                    "base64_image": result.get('base64_image', ''),
                    "processed_image": result.get('processed_image')
                }
            else:
                return result
                
        except Exception as e:
            return {"success": False, "error": f"图像处理失败: {str(e)}"}
    
    def _process_result(self, raw_result: Dict) -> Dict[str, Any]:
        """处理Agent执行结果"""
        return {
            "success": True,
            "answer": raw_result.get("output", ""),
            "reasoning_steps": self._extract_reasoning_steps(raw_result),
            "tools_used": self._extract_tools_used(raw_result),
            "confidence": self._calculate_confidence(raw_result),
            "metadata": {
                "agent_type": "TrafficMultimodalAgent",
                "model": OLLAMA_CONFIG["model_name"],
                "timestamp": self._get_timestamp()
            }
        }
    
    def _extract_reasoning_steps(self, result: Dict) -> List[Dict]:
        """提取推理步骤"""
        steps = []
        if "intermediate_steps" in result:
            for step in result["intermediate_steps"]:
                if hasattr(step, '__len__') and len(step) >= 2:
                    action = step[0]
                    observation = step[1]
                    steps.append({
                        "tool": getattr(action, 'tool', 'unknown'),
                        "input": getattr(action, 'tool_input', str(action)),
                        "output": str(observation)
                    })
        return steps
    
    def _extract_tools_used(self, result: Dict) -> List[str]:
        """提取使用的工具列表"""
        tools_used = []
        if "intermediate_steps" in result:
            for step in result["intermediate_steps"]:
                if hasattr(step, '__len__') and len(step) >= 1:
                    action = step[0]
                    tool_name = getattr(action, 'tool', 'unknown')
                    if tool_name not in tools_used:
                        tools_used.append(tool_name)
        return tools_used
    
    def _calculate_confidence(self, result: Dict) -> float:
        """计算结果置信度"""
        if "error" in result:
            return 0.0
        
        tools_used = len(self._extract_tools_used(result))
        reasoning_steps = len(self._extract_reasoning_steps(result))
        
        # 基于使用的工具数量和推理步骤计算置信度
        base_confidence = 0.6
        tool_bonus = min(tools_used * 0.08, 0.3)
        step_bonus = min(reasoning_steps * 0.02, 0.1)
        
        confidence = base_confidence + tool_bonus + step_bonus
        return round(min(confidence, 1.0), 2)
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    # ========== 工具实现方法（占位符） ==========
    
    async def _analyze_traffic_image(self, input_query: str) -> str:
        """
        图像分析工具实现
        
        调用TrafficImageAnalyzer对交通场景图像进行专业分析。
        这个方法作为LangChain工具的包装器，处理输入输出格式转换。
        
        Args:
            input_query: 工具输入查询，可能包含分析要求或"请分析当前图像"
            
        Returns:
            str: 格式化的图像分析结果文本
        """
        try:
            self.logger.info("🖼️ 调用专业图像分析工具...")
            
            # 检查是否存在图像数据（从Agent的输入数据中获取）
            # 这个数据在process_query中通过_image_data传递
            if hasattr(self, '_current_image_data') and self._current_image_data:
                image_data = self._current_image_data
                
                # 调用专业图像分析工具
                analysis_result = await self.image_analyzer.analyze(
                    image_input=image_data.get('base64_image', ''),
                    analysis_focus=self._extract_analysis_focus(input_query),
                    output_format="structured"
                )
                
                if analysis_result["success"]:
                    # 格式化分析结果为易读文本
                    return self._format_image_analysis_result(analysis_result["analysis_result"])
                else:
                    return f"图像分析失败: {analysis_result.get('error', '未知错误')}"
            else:
                return "错误：没有检测到图像数据，请确保上传了交通场景图像。"
                
        except Exception as e:
            self.logger.error(f"❌ 图像分析工具调用失败: {e}")
            return f"图像分析过程中发生错误: {str(e)}"
    
    async def _detect_violations(self, scene_analysis: str) -> str:
        """
        违规检测工具实现
        
        基于场景分析结果检测潜在的交通违规行为。
        调用TrafficViolationDetector进行专业的违规模式匹配。
        
        Args:
            scene_analysis: 场景分析结果文本或前一步工具的输出
            
        Returns:
            str: 格式化的违规检测结果文本
        """
        try:
            self.logger.info("⚠️ 调用专业违规检测工具...")
            
            # 解析场景分析结果
            parsed_scene = self._parse_scene_analysis(scene_analysis)
            
            # 调用专业违规检测工具
            violation_result = await self.violation_detector.detect(
                scene_analysis=parsed_scene,
                detection_focus=None,  # 检测所有类型的违规
                confidence_threshold=0.6
            )
            
            if violation_result["success"]:
                violations = violation_result["violations_detected"]
                
                if violations:
                    # 格式化违规检测结果
                    result_text = "🚨 违规检测结果：\n"
                    result_text += f"检测到 {len(violations)} 个违规行为：\n\n"
                    
                    for i, violation in enumerate(violations, 1):
                        result_text += f"{i}. 违规行为：{violation['name']}\n"
                        result_text += f"   - 严重程度：{violation['severity']}\n"
                        result_text += f"   - 置信度：{violation['confidence']}\n"
                        result_text += f"   - 法规依据：{violation['legal_reference']}\n"
                        result_text += f"   - 处罚信息：{violation['penalty_info']}\n"
                        result_text += f"   - 详细描述：{violation['description']}\n\n"
                    
                    result_text += f"整体风险等级：{violation_result['risk_level']}\n"
                    
                    # 添加建议
                    if violation_result.get('recommendations'):
                        result_text += "\n预防建议：\n"
                        for rec in violation_result['recommendations']:
                            result_text += f"- {rec}\n"
                    
                    return result_text
                else:
                    return "✅ 违规检测结果：未检测到明显的交通违规行为。"
            else:
                return f"违规检测失败: {violation_result.get('error', '未知错误')}"
                
        except Exception as e:
            self.logger.error(f"❌ 违规检测工具调用失败: {e}")
            return f"违规检测过程中发生错误: {str(e)}"
    
    # _search_regulations 方法已移除
    # RAG功能现在由上层TrafficRAGAgent统一处理，避免架构重复
    
    async def _assess_safety(self, analysis_data: str) -> str:
        """
        安全评估工具实现
        
        综合分析交通场景的安全风险，计算安全等级。
        调用TrafficSafetyAssessor进行专业的安全风险评估。
        
        Args:
            analysis_data: 前序分析数据，包含场景分析和违规检测结果
            
        Returns:
            str: 格式化的安全评估结果文本
        """
        try:
            self.logger.info("🛡️ 调用专业安全评估工具...")
            
            # 解析前序分析数据
            scene_analysis, violation_results = self._parse_analysis_data(analysis_data)
            
            # 调用专业安全评估工具
            safety_result = await self.safety_assessor.assess(
                scene_analysis=scene_analysis,
                violation_results=violation_results,
                assessment_options=None
            )
            
            if safety_result["success"]:
                assessment = safety_result["safety_assessment"]
                
                # 格式化安全评估结果
                result_text = "🛡️ 安全评估结果：\n\n"
                result_text += f"📊 总体安全分数：{assessment['overall_score']:.1f}/100\n"
                result_text += f"🏆 安全等级：{assessment['safety_level']}级\n"
                result_text += f"📝 等级描述：{assessment['level_description']}\n\n"
                
                # 各维度评分
                dimensional_scores = assessment.get('dimensional_scores', {})
                if dimensional_scores:
                    result_text += "📈 各维度安全评分：\n"
                    dimension_names = {
                        'environmental': '环境安全',
                        'infrastructure': '基础设施安全',
                        'behavioral': '行为安全',
                        'traffic_flow': '交通流安全',
                        'equipment': '设备安全'
                    }
                    
                    for dimension, score in dimensional_scores.items():
                        name = dimension_names.get(dimension, dimension)
                        result_text += f"   - {name}：{score:.1f}/100\n"
                    result_text += "\n"
                
                # 风险因素分析
                risk_factors = assessment.get('risk_factors', {})
                if risk_factors:
                    result_text += "⚠️ 主要风险因素：\n"
                    for risk_type, risk_info in risk_factors.items():
                        result_text += f"   - {risk_type}：{risk_info['risk_level']}风险（{risk_info['score']:.1f}分）\n"
                        result_text += f"     问题：{', '.join(risk_info['main_issues'])}\n"
                    result_text += "\n"
                
                # 主要风险点
                major_risks = safety_result.get('major_risks', [])
                if major_risks:
                    result_text += "🚨 主要风险点：\n"
                    for risk in major_risks:
                        result_text += f"   - {risk}\n"
                    result_text += "\n"
                
                # 改进紧急程度和趋势
                result_text += f"⏰ 改进紧急程度：{assessment.get('improvement_urgency', '需要评估')}\n"
                result_text += f"📈 安全趋势预测：{assessment.get('prediction_trend', '趋势平稳')}\n"
                result_text += f"🎯 评估置信度：{assessment.get('confidence_level', 0.7):.2f}\n\n"
                
                # 安全建议
                recommendations = safety_result.get('recommendations', [])
                if recommendations:
                    result_text += "💡 安全改进建议：\n"
                    for rec in recommendations:
                        result_text += f"   - {rec}\n"
                
                return result_text
            else:
                return f"安全评估失败: {safety_result.get('error', '未知错误')}"
                
        except Exception as e:
            self.logger.error(f"❌ 安全评估工具调用失败: {e}")
            return f"安全评估过程中发生错误: {str(e)}"
    
    async def _generate_suggestions(self, assessment_data: str) -> str:
        """
        建议生成工具实现
        
        基于前序分析结果生成具体的交通改进建议。
        调用TrafficSuggestionGenerator进行专业的建议生成。
        
        Args:
            assessment_data: 前序分析数据，包含场景分析、违规检测、安全评估结果
            
        Returns:
            str: 格式化的改进建议结果文本
        """
        try:
            self.logger.info("💡 调用专业建议生成工具...")
            
            # 解析前序分析数据
            scene_analysis, violation_results, safety_assessment = self._parse_comprehensive_data(assessment_data)
            
            # 调用专业建议生成工具
            suggestion_result = await self.suggestion_generator.generate(
                scene_analysis=scene_analysis,
                violation_results=violation_results,
                safety_assessment=safety_assessment,
                generation_options=None
            )
            
            if suggestion_result["success"]:
                suggestions = suggestion_result["suggestions"]
                
                # 格式化建议生成结果
                result_text = "💡 交通改进建议：\n\n"
                result_text += f"📊 总计生成 {suggestion_result['total_suggestions']} 条建议\n\n"
                
                # 按类别展示建议
                category_names = {
                    "基础设施改进": "🏗️",
                    "管理措施改进": "👮",
                    "技术手段改进": "💻",
                    "宣传教育改进": "📢",
                    "应急预案改进": "🚨"
                }
                
                for category, category_suggestions in suggestions.items():
                    if category_suggestions:
                        icon = category_names.get(category, "📋")
                        result_text += f"{icon} {category}：\n"
                        
                        for i, suggestion in enumerate(category_suggestions, 1):
                            result_text += f"   {i}. {suggestion['title']}\n"
                            result_text += f"      描述：{suggestion['description']}\n"
                            result_text += f"      优先级：{suggestion['priority']}\n"
                            result_text += f"      预估成本：{suggestion['estimated_cost']}\n"
                            result_text += f"      实施周期：{suggestion['implementation_time']}\n"
                            result_text += f"      预期效果：{suggestion['expected_effect']}\n"
                            result_text += f"      可行性：{suggestion['feasibility_score']:.2f}\n\n"
                
                # 优先级汇总
                priority_summary = suggestion_result.get('priority_summary', {})
                if priority_summary:
                    result_text += "🎯 优先级汇总：\n"
                    result_text += f"   - 紧急：{priority_summary.get('urgent', 0)} 条\n"
                    result_text += f"   - 高：{priority_summary.get('high', 0)} 条\n"
                    result_text += f"   - 中：{priority_summary.get('medium', 0)} 条\n"
                    result_text += f"   - 低：{priority_summary.get('low', 0)} 条\n\n"
                
                # 实施时间安排
                timeline = suggestion_result.get('implementation_timeline', {})
                if timeline:
                    result_text += "⏰ 实施时间安排：\n"
                    for timeframe, items in timeline.items():
                        if items:
                            result_text += f"   {timeframe}：\n"
                            for item in items:
                                result_text += f"     - {item}\n"
                    result_text += "\n"
                
                # 成本预算建议
                cost_estimation = suggestion_result.get('cost_estimation', {})
                if cost_estimation.get('budget_recommendation'):
                    result_text += f"💰 预算建议：{cost_estimation['budget_recommendation']}\n"
                
                return result_text
            else:
                return f"建议生成失败: {suggestion_result.get('error', '未知错误')}"
                
        except Exception as e:
            self.logger.error(f"❌ 建议生成工具调用失败: {e}")
            return f"建议生成过程中发生错误: {str(e)}"
    
    # ========== 辅助方法 ==========
    
    def _extract_analysis_focus(self, input_query: str) -> List[str]:
        """
        从输入查询中提取分析重点
        
        Args:
            input_query: 用户输入查询
            
        Returns:
            List[str]: 分析重点列表
        """
        focus_keywords = {
            "violations": ["违规", "违法", "不规范"],
            "safety": ["安全", "风险", "危险"],
            "infrastructure": ["设施", "基础", "道路"],
            "participants": ["车辆", "行人", "参与者"],
            "environment": ["环境", "天气", "光照"]
        }
        
        analysis_focus = []
        query_lower = input_query.lower()
        
        for focus, keywords in focus_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                analysis_focus.append(focus)
        
        return analysis_focus if analysis_focus else None
    
    def _format_image_analysis_result(self, analysis_result: Dict[str, Any]) -> str:
        """
        格式化图像分析结果为易读文本
        
        Args:
            analysis_result: 结构化的分析结果
            
        Returns:
            str: 格式化的文本结果
        """
        try:
            result_text = "🖼️ 图像分析结果：\n\n"
            
            # 道路环境
            road_env = analysis_result.get("road_environment", {})
            if road_env:
                result_text += "🛣️ 道路环境：\n"
                result_text += f"   - 道路类型：{road_env.get('road_type', '未知')}\n"
                result_text += f"   - 车道配置：{road_env.get('lane_config', '未知')}\n"
                result_text += f"   - 路面状况：{road_env.get('surface_condition', '未知')}\n"
                result_text += f"   - 标识状况：{road_env.get('markings_condition', '未知')}\n\n"
            
            # 交通参与者
            participants = analysis_result.get("traffic_participants", {})
            if participants:
                result_text += "🚗 交通参与者：\n"
                
                motor = participants.get("motor_vehicles", {})
                if motor:
                    result_text += f"   - 机动车：{motor.get('count', 0)}辆 ({', '.join(motor.get('types', []))})\n"
                
                non_motor = participants.get("non_motor_vehicles", {})
                if non_motor:
                    result_text += f"   - 非机动车：{non_motor.get('count', 0)}辆 ({', '.join(non_motor.get('types', []))})\n"
                
                pedestrians = participants.get("pedestrians", {})
                if pedestrians:
                    result_text += f"   - 行人：{pedestrians.get('count', 0)}名\n"
                
                result_text += "\n"
            
            # 交通设施
            facilities = analysis_result.get("traffic_facilities", {})
            if facilities:
                result_text += "🚦 交通设施：\n"
                for facility, status in facilities.items():
                    result_text += f"   - {facility}：{status}\n"
                result_text += "\n"
            
            # 环境条件
            environment = analysis_result.get("environment", {})
            if environment:
                result_text += "🌤️ 环境条件：\n"
                for env_factor, condition in environment.items():
                    result_text += f"   - {env_factor}：{condition}\n"
                result_text += "\n"
            
            # 关键观察
            key_observations = analysis_result.get("key_observations", [])
            if key_observations:
                result_text += "👁️ 关键观察：\n"
                for observation in key_observations:
                    result_text += f"   - {observation}\n"
            
            return result_text
            
        except Exception as e:
            self.logger.error(f"❌ 格式化图像分析结果失败: {e}")
            return f"图像分析结果格式化失败: {str(e)}"
    
    def _parse_scene_analysis(self, scene_analysis: str) -> Dict[str, Any]:
        """
        解析场景分析文本为结构化数据
        
        Args:
            scene_analysis: 场景分析文本
            
        Returns:
            Dict[str, Any]: 结构化的场景数据
        """
        try:
            # 优先使用预设的场景数据（用于测试）
            if hasattr(self, '_current_scene_data') and self._current_scene_data:
                self.logger.info("🎯 使用预设场景数据进行分析")
                return self._current_scene_data
            
            # 如果没有预设数据，则解析文本
            # 这里实现简单的文本解析逻辑
            # 在实际应用中，可以使用更复杂的NLP技术
            
            parsed_data = {
                "traffic_participants": {
                    "motor_vehicles": {"count": 0, "types": [], "behaviors": []},
                    "pedestrians": {"count": 0, "locations": [], "behaviors": []}
                },
                "road_environment": {"road_type": "未知", "lane_config": "未知"},
                "traffic_facilities": {},
                "environment": {"weather": "未知", "lighting": "未知"},
                "key_observations": []
            }
            
            # 简单的关键词匹配解析
            if "机动车" in scene_analysis or "汽车" in scene_analysis:
                parsed_data["traffic_participants"]["motor_vehicles"]["count"] = 1
                parsed_data["traffic_participants"]["motor_vehicles"]["types"] = ["机动车"]
                
            if "行人" in scene_analysis:
                parsed_data["traffic_participants"]["pedestrians"]["count"] = 1
                parsed_data["traffic_participants"]["pedestrians"]["locations"] = ["道路"]
                
            if "人行横道" in scene_analysis:
                parsed_data["key_observations"].append("涉及人行横道")
                
            if "未礼让" in scene_analysis or "未让行" in scene_analysis:
                parsed_data["key_observations"].append("机动车未礼让行人")
                
            # 如果解析结果为空，添加默认观察
            if not parsed_data["key_observations"]:
                parsed_data["key_observations"].append(scene_analysis.strip())
                
            return parsed_data
            
        except Exception as e:
            self.logger.error(f"❌ 场景分析解析失败: {e}")
            return {"raw_analysis": scene_analysis}
    
    def _parse_analysis_data(self, analysis_data: str) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        解析前序分析数据，提取场景分析和违规检测结果
        
        Args:
            analysis_data: 前序分析数据文本
            
        Returns:
            Tuple[Dict[str, Any], Dict[str, Any]]: 场景分析数据和违规检测结果
        """
        try:
            # 初始化结果
            scene_analysis = {}
            violation_results = {"violations_detected": []}
            
            # 检查是否包含违规检测结果
            if "违规检测结果" in analysis_data:
                # 简单解析违规信息
                if "检测到" in analysis_data and "个违规行为" in analysis_data:
                    # 提取违规数量
                    import re
                    violation_match = re.search(r'检测到\s*(\d+)\s*个违规行为', analysis_data)
                    if violation_match:
                        violation_count = int(violation_match.group(1))
                        # 创建基本的违规结果结构
                        for i in range(violation_count):
                            violation_results["violations_detected"].append({
                                "name": "解析的违规行为",
                                "severity": "medium",
                                "confidence": 0.8
                            })
                
                violation_results["success"] = True
                violation_results["total_violations"] = len(violation_results["violations_detected"])
            
            # 解析场景信息
            scene_analysis = self._parse_scene_analysis(analysis_data)
            
            return scene_analysis, violation_results
            
        except Exception as e:
            self.logger.error(f"❌ 分析数据解析失败: {e}")
            return {}, {"violations_detected": [], "success": False}
    
    def _parse_comprehensive_data(self, assessment_data: str) -> Tuple[Dict[str, Any], Dict[str, Any], Dict[str, Any]]:
        """
        解析全面的分析数据，提取场景分析、违规检测和安全评估结果
        
        Args:
            assessment_data: 全面的分析数据文本
            
        Returns:
            Tuple: 场景分析数据、违规检测结果、安全评估结果
        """
        try:
            # 解析基础数据
            scene_analysis, violation_results = self._parse_analysis_data(assessment_data)
            
            # 解析安全评估结果
            safety_assessment = {"safety_assessment": {}}
            
            if "安全评估结果" in assessment_data:
                # 提取安全等级
                import re
                level_match = re.search(r'安全等级：([A-E])级', assessment_data)
                if level_match:
                    safety_level = level_match.group(1)
                    safety_assessment["safety_assessment"]["safety_level"] = safety_level
                    safety_assessment["risk_level"] = safety_level
                
                # 提取安全分数
                score_match = re.search(r'总体安全分数：([\d.]+)', assessment_data)
                if score_match:
                    safety_score = float(score_match.group(1))
                    safety_assessment["safety_assessment"]["overall_score"] = safety_score
                
                safety_assessment["success"] = True
            
            return scene_analysis, violation_results, safety_assessment
            
        except Exception as e:
            self.logger.error(f"❌ 综合数据解析失败: {e}")
            return {}, {"violations_detected": []}, {"safety_assessment": {}}