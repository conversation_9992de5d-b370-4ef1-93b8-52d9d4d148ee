#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交通多模态智能助手 - RAG增强版本

基于原有的TrafficMultimodalAgent，集成RAG知识检索系统，
将RAG作为Agent的"知识增强器"而不是工具。

设计理念：
1. RAG系统在查询处理前先检索相关专业知识
2. 检索到的知识作为系统上下文注入Agent
3. Agent基于增强的知识上下文进行推理和工具调用
4. 保持原有5个多模态工具不变
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

# LangChain imports
from langchain.schema import HumanMessage, SystemMessage

# 项目导入
from .traffic_multimodal_agent import TrafficMultimodalAgent
from ..rag.langchain_rag import LangChainRAGSystem
from ..utils.logger import get_logger
from config.settings import RAG_CONFIG

logger = get_logger(__name__)

class TrafficRAGAgent(TrafficMultimodalAgent):
    """
    RAG增强的交通多模态智能助手
    
    在原有TrafficMultimodalAgent基础上集成RAG知识检索，
    提供专业的交通法规知识支持。
    """
    
    def __init__(self, 
                 rag_model_name: str = "BAAI/bge-large-zh-v1.5",
                 enable_rag: bool = True):
        """
        初始化RAG增强的Agent
        
        Args:
            rag_model_name: RAG嵌入模型名称
            enable_rag: 是否启用RAG功能
        """
        # 初始化父类
        super().__init__()
        
        self.enable_rag = enable_rag
        self.rag_system = None
        
        self.rag_model_name = rag_model_name
        
        # RAG系统会在异步初始化时创建
        if self.enable_rag:
            self._rag_initialized = False
        else:
            self._rag_initialized = True
    
    async def _initialize_rag_system(self):
        """异步初始化RAG系统"""
        if self._rag_initialized or not self.enable_rag:
            return
            
        try:
            logger.info("🧠 初始化RAG知识增强系统...")
            
            self.rag_system = LangChainRAGSystem(
                model_name=RAG_CONFIG["model_name"],
                persist_directory=RAG_CONFIG["persist_directory"],
                collection_name=RAG_CONFIG["collection_name"]
            )
            
            self._rag_initialized = True
            logger.info("✅ RAG系统初始化成功")
            
        except Exception as e:
            logger.error(f"❌ RAG系统初始化失败: {e}")
            logger.warning("🔄 降级为普通模式（无RAG增强）")
            self.enable_rag = False
            self.rag_system = None
            self._rag_initialized = True
    
    async def _analyze_image_scene(self, image_path: str) -> str:
        """
        图像预分析 - 提取场景信息用于RAG检索
        
        Args:
            image_path: 图像路径
            
        Returns:
            str: 场景描述信息
        """
        try:
            logger.info(f"🔍 开始图像预分析: {image_path}")
            
            # 确保基础Agent已初始化
            await self.initialize()
            
            # 从基础Agent获取图像分析工具
            from ..tools.traffic_image_analyzer import TrafficImageAnalyzer
            image_analyzer = TrafficImageAnalyzer()
            
            # 进行快速场景分析
            analysis_result = await image_analyzer.analyze(image_input=image_path)
            
            # 提取实际的分析数据（analysis_result包含success、analysis_result等字段）
            actual_analysis = analysis_result.get('analysis_result', {})
            
            # 提取关键场景信息
            scene_info = self._extract_scene_keywords(actual_analysis)
            
            logger.info(f"✅ 图像预分析完成: {scene_info[:50]}...")
            return scene_info
            
        except Exception as e:
            logger.error(f"❌ 图像预分析失败: {e}")
            return ""
    
    def _extract_scene_keywords(self, analysis_result: Dict[str, Any]) -> str:
        """
        从图像分析结果中提取关键场景信息，支持新的结构化数据格式
        
        Args:
            analysis_result: 图像分析结果（支持新的结构化格式）
            
        Returns:
            str: 提取的场景关键词
        """
        try:
            scene_keywords = []
            
            # 兼容旧格式
            description = analysis_result.get('description', '')
            traffic_elements = analysis_result.get('traffic_elements', {})
            violations = analysis_result.get('violations', [])
            
            # 处理新的结构化格式
            road_env = analysis_result.get('road_environment', {})
            traffic_participants = analysis_result.get('traffic_participants', {})
            traffic_facilities = analysis_result.get('traffic_facilities', {})
            key_observations = analysis_result.get('key_observations', [])
            
            # 1. 从key_observations提取违规信息（最重要）
            for observation in key_observations:
                if '闯红灯' in observation or '闯灯' in observation:
                    scene_keywords.extend(['闯红灯', '信号灯违规'])
                if '未礼让' in observation or '不礼让' in observation:
                    scene_keywords.extend(['未礼让行人', '礼让行人'])
                if '占用' in observation and ('人行横道' in observation or '非机动车道' in observation):
                    scene_keywords.extend(['占用车道', '违规停车'])
                if '停在人行横道' in observation or '占用人行横道' in observation:
                    scene_keywords.extend(['占用人行横道', '违规停车', '人行横道违规'])
                if '超速' in observation or '超速行驶' in observation:
                    scene_keywords.extend(['超速行驶', '超速违法', '限速'])
                if '违规' in observation or '违法' in observation:
                    scene_keywords.append('交通违规')
                    
            # 2. 从交通设施信息提取场景类型
            signals = traffic_facilities.get('signals', '')
            if '红灯' in signals or '绿灯' in signals:
                scene_keywords.append('信号灯控制')
            if '人行横道' in str(traffic_facilities):
                scene_keywords.append('人行横道')
            
            # 检查限速标志
            signs = traffic_facilities.get('signs', '')
            if '限速' in signs:
                scene_keywords.append('限速')
                
            # 3. 从交通参与者信息提取
            motor_vehicles = traffic_participants.get('motor_vehicles', {})
            pedestrians = traffic_participants.get('pedestrians', {})
            non_motor_vehicles = traffic_participants.get('non_motor_vehicles', {})
            
            if motor_vehicles.get('count', 0) > 0:
                scene_keywords.append('机动车')
                # 检查机动车行为
                behaviors = motor_vehicles.get('behaviors', [])
                for behavior in behaviors:
                    if '超速' in behavior:
                        scene_keywords.extend(['超速行驶', '超速违法', '限速'])
                    if '停在人行横道' in behavior or '占用人行横道' in behavior:
                        scene_keywords.extend(['占用人行横道', '违规停车', '人行横道违规'])
            if pedestrians.get('count', 0) > 0:
                scene_keywords.append('行人')
            if non_motor_vehicles.get('count', 0) > 0:
                scene_keywords.append('非机动车')
                
            # 4. 从道路环境提取场景类型
            road_type = road_env.get('road_type', '')
            if '城市道路' in road_type:
                scene_keywords.append('城市道路')
            elif '高速公路' in road_type:
                scene_keywords.append('高速公路')
                
            # 5. 兼容旧格式的违规行为处理
            for violation in violations:
                if '未礼让行人' in violation:
                    scene_keywords.extend(['未礼让行人', '礼让行人'])
                if '占用人行横道' in violation:
                    scene_keywords.extend(['占用人行横道', '人行横道违规'])
                if '闯红灯' in violation:
                    scene_keywords.extend(['闯红灯', '信号灯违规'])
                    
            # 6. 从描述中提取（兼容性处理）
            full_text = f"{description} {' '.join(key_observations)}"
            if '人行横道' in full_text or '斑马线' in full_text:
                scene_keywords.append('人行横道')
            if '信号灯' in full_text:
                scene_keywords.append('交通信号')
                
            # 去重并构建场景描述
            unique_keywords = list(dict.fromkeys(scene_keywords))
            
            if unique_keywords:
                return f"场景识别: {', '.join(unique_keywords[:10])}"
            else:
                # 如果没有提取到关键词，使用基本场景信息
                if road_type:
                    return f"场景识别: {road_type}"
                elif description:
                    return f"一般交通场景: {description[:100]}"
                else:
                    return "交通场景分析"
                    
        except Exception as e:
            logger.error(f"场景关键词提取失败: {e}")
            return "交通场景分析"
    
    async def _retrieve_relevant_knowledge(self, user_input: str) -> List[Dict[str, Any]]:
        """
        检索与用户查询相关的专业知识
        
        Args:
            user_input: 用户输入
            
        Returns:
            List[Dict]: 相关知识列表
        """
        if not self.enable_rag or not self.rag_system:
            return []
        
        try:
            logger.info(f"🔍 检索相关知识: {user_input[:50]}...")
            
            # 使用RAG系统检索相关知识
            knowledge_results = self.rag_system.search_knowledge(user_input, k=3)
            
            if knowledge_results:
                logger.info(f"✅ 找到 {len(knowledge_results)} 条相关知识")
                return knowledge_results
            else:
                logger.info("📚 未找到直接相关的专业知识")
                return []
                
        except Exception as e:
            logger.error(f"❌ 知识检索失败: {e}")
            return []
    
    def _format_knowledge_context(self, knowledge_results: List[Dict[str, Any]]) -> str:
        """
        格式化检索到的知识为上下文
        
        Args:
            knowledge_results: 检索结果
            
        Returns:
            str: 格式化的知识上下文
        """
        if not knowledge_results:
            return ""
        
        context_parts = ["# 专业知识库参考\n"]
        
        for i, result in enumerate(knowledge_results, 1):
            content = result.get('content', '')
            metadata = result.get('metadata', {})
            
            context_parts.append(f"## 参考资料 {i}")
            context_parts.append(f"**内容**: {content}")
            
            # 添加元数据信息
            if 'law' in metadata:
                context_parts.append(f"**法律依据**: {metadata['law']}")
            if 'article' in metadata:
                context_parts.append(f"**条文**: {metadata['article']}")
            if 'collection' in metadata:
                context_parts.append(f"**来源**: {metadata['collection']}")
            
            context_parts.append("")  # 空行分隔
        
        return "\n".join(context_parts)
    
    def _build_enhanced_system_prompt(self, knowledge_context: str) -> str:
        """
        构建增强的系统提示词
        
        Args:
            knowledge_context: 知识上下文
            
        Returns:
            str: 增强的系统提示词
        """
        base_prompt = """你是一个专业的交通多模态智能助手，具备以下能力：

🔧 **核心工具**：
1. **图像分析工具** - 分析交通场景图像，识别车辆、道路、标志等
2. **视频分析工具** - 处理交通监控视频，检测异常行为
3. **音频分析工具** - 分析交通相关音频（车辆声音、警报等）
4. **文档分析工具** - 处理交通相关文档和报告
5. **OCR文字识别工具** - 识别图像中的文字信息（车牌、标志等）

🎯 **工作原则**：
- 优先使用专业知识库的权威信息
- 结合多模态分析提供准确判断
- 给出具体的法规依据和处理建议
- 保持专业、准确、有用的回答风格"""

        if knowledge_context:
            enhanced_prompt = f"""{base_prompt}

{knowledge_context}

💡 **特别说明**：
- 优先参考上述专业知识库的权威信息
- 如果专业知识与你的训练数据有冲突，以专业知识库为准
- 在回答时明确引用相关法规条文和案例"""
        else:
            enhanced_prompt = base_prompt
        
        return enhanced_prompt
    
    async def process_query(self, user_input: str, image_path: Optional[str] = None, **kwargs) -> str:
        """
        处理用户查询（图像先行RAG增强版本）
        
        优化后的流程：
        1. 如果有图像，先进行预分析提取场景信息
        2. 结合场景信息和用户输入构建增强查询
        3. 基于增强查询进行精准RAG检索
        4. 将检索结果注入Agent进行推理
        
        Args:
            user_input: 用户输入
            image_path: 图像路径（可选）
            **kwargs: 其他参数
            
        Returns:
            str: Agent回复
        """
        try:
            logger.info(f"🚀 处理用户查询（图像先行模式): {user_input[:100]}...")
            
            # 1. 确保Agent和RAG系统都已初始化
            await self.initialize()  # 初始化父类Agent
            await self._initialize_rag_system()  # 初始化RAG系统
            
            # 2. 图像先行：如果有图像，先进行预分析
            scene_info = ""
            if image_path:
                logger.info("🖼️ 启动图像先行模式...")
                scene_info = await self._analyze_image_scene(image_path)
                
            # 3. 构建增强查询（结合用户输入和图像场景信息）
            enhanced_query = self._build_enhanced_query(user_input, scene_info)
            
            # 4. 基于增强查询进行精准RAG检索
            knowledge_results = await self._retrieve_relevant_knowledge(enhanced_query)
            
            # 5. 格式化知识上下文
            knowledge_context = self._format_knowledge_context(knowledge_results)
            
            # 6. 如果有RAG知识，构建知识增强的输入
            if knowledge_results:
                logger.info(f"✅ 基于图像场景信息检索到 {len(knowledge_results)} 条精准知识")
                
                # 构建包含场景信息和专业知识的增强输入
                final_enhanced_input = f"""请基于以下信息进行专业分析：

# 图像场景信息
{scene_info if scene_info else '未提供图像'}

# 专业知识库参考
{knowledge_context}

# 用户问题
{user_input}

请结合图像分析和专业知识，提供准确的法规依据和专业建议。优先参考专业知识库的权威信息，并在回答时明确引用相关法规条文。"""
                
                # 调用父类的process_query方法
                return await super().process_query(final_enhanced_input, image_path=image_path, **kwargs)
            else:
                # 没有找到相关知识，但如果有场景信息也要利用
                if scene_info:
                    contextual_input = f"""基于图像分析结果回答问题：

图像场景信息：{scene_info}
用户问题：{user_input}

请结合图像分析进行专业回答。"""
                    return await super().process_query(contextual_input, image_path=image_path, **kwargs)
                else:
                    # 直接调用父类方法
                    return await super().process_query(user_input, image_path=image_path, **kwargs)
            
        except Exception as e:
            logger.error(f"❌ 查询处理失败: {e}")
            import traceback
            traceback.print_exc()
            
            # 降级处理：使用原始Agent
            try:
                logger.info("🔄 降级使用原始Agent...")
                await self.initialize()  # 确保父类已初始化
                return await super().process_query(user_input, image_path=image_path, **kwargs)
            except Exception as fallback_error:
                logger.error(f"❌ 降级处理也失败: {fallback_error}")
                return "抱歉，系统暂时无法处理您的请求，请稍后再试。"
    
    def _build_enhanced_query(self, user_input: str, scene_info: str) -> str:
        """
        构建增强查询，结合用户输入和图像场景信息
        
        Args:
            user_input: 用户原始输入
            scene_info: 图像场景信息
            
        Returns:
            str: 增强后的查询字符串
        """
        if scene_info:
            # 将场景信息与用户查询结合，提高RAG检索精度
            enhanced_query = f"{user_input} {scene_info}"
            logger.info(f"📝 构建增强查询: {enhanced_query}")
            return enhanced_query
        else:
            return user_input
    
    def get_rag_status(self) -> Dict[str, Any]:
        """
        获取RAG系统状态信息
        
        Returns:
            Dict: RAG状态信息
        """
        if not self.enable_rag or not self.rag_system:
            return {
                "enabled": False,
                "status": "disabled",
                "message": "RAG功能未启用"
            }
        
        try:
            collection_info = self.rag_system.get_collection_info()
            return {
                "enabled": True,
                "status": "active",
                "collection_info": collection_info,
                "message": "RAG系统运行正常"
            }
        except Exception as e:
            return {
                "enabled": True,
                "status": "error",
                "error": str(e),
                "message": "RAG系统出现错误"
            }
    
    async def test_rag_knowledge(self, test_query: str) -> Dict[str, Any]:
        """
        测试RAG知识检索功能
        
        Args:
            test_query: 测试查询
            
        Returns:
            Dict: 测试结果
        """
        knowledge_results = await self._retrieve_relevant_knowledge(test_query)
        
        return {
            "query": test_query,
            "knowledge_count": len(knowledge_results),
            "knowledge_results": knowledge_results,
            "formatted_context": self._format_knowledge_context(knowledge_results)
        }

# 便捷创建函数
def create_traffic_rag_agent(
    rag_model_name: str = "BAAI/bge-large-zh-v1.5",
    enable_rag: bool = True
) -> TrafficRAGAgent:
    """
    创建RAG增强的交通多模态Agent
    
    Args:
        rag_model_name: RAG嵌入模型名称
        enable_rag: 是否启用RAG功能
        
    Returns:
        TrafficRAGAgent: RAG增强的Agent实例
    """
    return TrafficRAGAgent(
        rag_model_name=rag_model_name,
        enable_rag=enable_rag
    )

# 使用示例
if __name__ == "__main__":
    async def test_rag_agent():
        """测试RAG增强Agent"""
        print("🚀 测试RAG增强的交通多模态Agent")
        print("=" * 60)
        
        # 创建Agent
        agent = create_traffic_rag_agent()
        
        # 检查RAG状态
        rag_status = agent.get_rag_status()
        print(f"RAG状态: {rag_status}")
        
        # 测试查询
        test_queries = [
            "超速违法的处罚标准是什么？",
            "雨天驾驶需要注意什么？",
            "在人行横道前应该怎么做？"
        ]
        
        for query in test_queries:
            print(f"\n{'='*50}")
            print(f"测试查询: {query}")
            print(f"{'='*50}")
            
            response = await agent.process_query(query)
            print(f"Agent回复: {response}")
    
    # 运行测试
    asyncio.run(test_rag_agent())