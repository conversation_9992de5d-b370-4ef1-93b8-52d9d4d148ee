# -*- coding: utf-8 -*-
"""
交通法规知识库数据

这个文件包含了结构化的交通法规数据，用于填充RAG系统的knowledge base。
数据按照不同的集合分类存储，包括：
1. traffic_regulations - 交通法规条文
2. traffic_cases - 违规案例和处罚情况  
3. traffic_standards - 技术标准和规范
4. traffic_faqs - 常见问题解答

每个文档包含以下字段：
- content: 文档主要内容
- metadata: 元数据信息（来源、分类、时间等）
- type: 文档类型
- source: 数据来源
"""

# 交通法规条文数据
TRAFFIC_REGULATIONS = [
    {
        "content": """第四十二条　机动车上道路行驶，不得超过限速标志标明的最高时速。在没有限速标志的路段，应当保持安全车速。夜间行驶或者在容易发生危险的路段行驶，以及遇有沙尘、冰雹、雨、雪、雾、结冰等气象条件时，应当降低行驶速度。""",
        "metadata": {
            "law": "道路交通安全法",
            "article": "第四十二条",
            "category": "行驶规定",
            "keywords": "限速,安全车速,恶劣天气",
            "effective_date": "2004-05-01"
        },
        "type": "regulation",
        "source": "中华人民共和国道路交通安全法"
    },
    {
        "content": """第四十三条　同方向行驶的机动车，后车应当与前车保持足以采取紧急制动措施的安全距离。有下列情形之一的，不得超车：（一）前车正在左转弯、掉头、超车的；（二）与对面来车有会车可能的；（三）前车为执行紧急任务的警车、消防车、救护车、工程救险车的；（四）行经铁路道口、交叉路口、窄桥、弯道、陡坡、隧道、人行横道、市区交通流量大的路段等没有超车条件的。""",
        "metadata": {
            "law": "道路交通安全法", 
            "article": "第四十三条",
            "category": "超车规定",
            "keywords": "安全距离,超车,禁止超车",
            "effective_date": "2004-05-01"
        },
        "type": "regulation",
        "source": "中华人民共和国道路交通安全法"
    },
    {
        "content": """第四十七条　机动车行经人行横道时，应当减速行驶；遇行人正在通过人行横道，应当停车让行。机动车行经没有交通信号的道路时，遇行人横过道路，应当避让。""",
        "metadata": {
            "law": "道路交通安全法",
            "article": "第四十七条", 
            "category": "行人保护",
            "keywords": "人行横道,减速,停车让行",
            "effective_date": "2004-05-01"
        },
        "type": "regulation",
        "source": "中华人民共和国道路交通安全法"
    },
    {
        "content": """第四十八条　机动车载物应当符合核定的载质量，严禁超载；载物的长、宽、高不得违反装载要求，不得遗洒、飘散载运物。机动车运载超限的不可解体的物品，影响交通安全的，应当按照公安机关交通管理部门指定的时间、路线、速度行驶，悬挂明显标志。在公路上运载超限的不可解体的物品，并应当依照公路法的规定执行。""",
        "metadata": {
            "law": "道路交通安全法",
            "article": "第四十八条",
            "category": "载物规定", 
            "keywords": "载重,超载,装载要求",
            "effective_date": "2004-05-01"
        },
        "type": "regulation",
        "source": "中华人民共和国道路交通安全法"
    },
    {
        "content": """第五十一条　机动车行驶时，驾驶人、乘坐人员应当按规定使用安全带，摩托车驾驶人及乘坐人员应当按规定戴安全头盔。""",
        "metadata": {
            "law": "道路交通安全法",
            "article": "第五十一条",
            "category": "安全防护",
            "keywords": "安全带,安全头盔,摩托车",
            "effective_date": "2004-05-01"
        },
        "type": "regulation", 
        "source": "中华人民共和国道路交通安全法"
    }
]

# 交通案例数据
TRAFFIC_CASES = [
    {
        "content": """案例：张某驾驶小型汽车在城市道路超速行驶，经测速设备检测，实际行驶速度为80km/h，限速标志为50km/h，超速60%。根据《道路交通安全法》相关规定，张某将面临扣6分，罚款200元的处罚。该案例说明了超速违法的严重后果，驾驶员应严格遵守限速规定。""",
        "metadata": {
            "case_type": "超速违法",
            "violation": "超速行驶",
            "penalty": "扣6分，罚款200元", 
            "speed_limit": "50km/h",
            "actual_speed": "80km/h",
            "excess_rate": "60%"
        },
        "type": "case",
        "source": "交通违法案例库"
    },
    {
        "content": """案例：李某醉酒后驾驶机动车被交警查获，经呼气酒精检测，血液酒精含量为120mg/100ml，属于醉酒驾驶。根据相关法律规定，李某驾驶证被吊销，5年内不得重新申领，并依法追究刑事责任。此案警示广大驾驶员，酒后驾驶害人害己，切勿心存侥幸。""",
        "metadata": {
            "case_type": "酒驾违法",
            "violation": "醉酒驾驶",
            "penalty": "吊销驾驶证，5年禁驾，刑事责任",
            "alcohol_content": "120mg/100ml",
            "violation_level": "醉驾"
        },
        "type": "case",
        "source": "交通违法案例库"
    },
    {
        "content": """案例：王某在人行横道前未减速礼让行人，导致与正在过马路的行人发生碰撞。经调查，王某负事故全部责任，不仅要承担医疗费用，还面临扣3分、罚款200元的处罚。该案例提醒驾驶员在人行横道前必须减速慢行，主动礼让行人。""",
        "metadata": {
            "case_type": "未礼让行人",
            "violation": "人行横道未礼让",
            "penalty": "扣3分，罚款200元，承担事故责任",
            "consequence": "与行人发生碰撞",
            "responsibility": "全责"
        },
        "type": "case", 
        "source": "交通违法案例库"
    }
]

# 技术标准数据
TRAFFIC_STANDARDS = [
    {
        "content": """GB5768.2-2009道路交通标志和标线：限速标志应设置在限速路段起点的显著位置，并在适当位置重复设置。限速值应当根据道路条件、交通状况和安全要求确定。城市道路一般限速为30-60km/h，城市快速路为60-80km/h，高速公路为120km/h。""",
        "metadata": {
            "standard": "GB5768.2-2009",
            "category": "交通标志标线",
            "content_type": "限速标准",
            "road_types": "城市道路,城市快速路,高速公路"
        },
        "type": "standard",
        "source": "国家标准"
    },
    {
        "content": """JTG D20-2017公路路线设计规范：安全距离的确定应考虑车速、路面状况、制动性能等因素。在干燥路面上，当车速为60km/h时，安全距离不应小于60米；车速为80km/h时，安全距离不应小于100米；车速为100km/h时，安全距离不应小于160米。""",
        "metadata": {
            "standard": "JTG D20-2017",
            "category": "公路设计",
            "content_type": "安全距离标准",
            "conditions": "干燥路面"
        },
        "type": "standard",
        "source": "行业标准"
    }
]

# 常见问题解答数据
TRAFFIC_FAQS = [
    {
        "content": """问：在雨天驾驶时应该注意什么？
答：雨天驾驶应注意以下几点：1）降低行驶速度，保持比平时更长的安全距离；2）正确使用灯光，开启示廓灯、近光灯和前后雾灯；3）避免急刹车、急转弯等危险动作；4）注意积水路段，缓慢通过；5）定期检查轮胎、雨刷器等设备。雨天路滑，制动距离延长，务必谨慎驾驶。""",
        "metadata": {
            "category": "恶劣天气驾驶",
            "weather": "雨天",
            "keywords": "雨天驾驶,安全距离,灯光使用",
            "difficulty": "常见"
        },
        "type": "faq",
        "source": "驾驶指南"
    },
    {
        "content": """问：什么情况下会被扣12分？
答：一次性扣12分的主要情况包括：1）饮酒后驾驶机动车；2）造成交通事故后逃逸，尚不构成犯罪；3）使用伪造、变造机动车号牌、行驶证、驾驶证；4）驾驶与准驾车型不符的机动车；5）超速50%以上等。扣满12分需要重新参加理论考试。""",
        "metadata": {
            "category": "扣分规定", 
            "penalty": "扣12分",
            "keywords": "酒驾,肇事逃逸,超速",
            "consequence": "重新考试"
        },
        "type": "faq",
        "source": "驾驶指南"
    },
    {
        "content": """问：新手驾驶员实习期有什么特殊规定？
答：实习期驾驶员需要注意：1）实习期为12个月，期间需在车后贴实习标志；2）不得单独驾驶上高速公路，需由持证3年以上驾驶员陪同；3）实习期扣满12分将注销驾驶证；4）实习期结束后扣分达到6-11分需延长实习期一年。建议新手在实习期内特别谨慎驾驶。""",
        "metadata": {
            "category": "实习期规定",
            "target": "新手驾驶员", 
            "duration": "12个月",
            "keywords": "实习标志,高速限制,扣分规定"
        },
        "type": "faq",
        "source": "驾驶指南"
    }
]

# 合并所有数据
ALL_KNOWLEDGE_DATA = {
    "traffic_regulations": TRAFFIC_REGULATIONS,
    "traffic_cases": TRAFFIC_CASES, 
    "traffic_standards": TRAFFIC_STANDARDS,
    "traffic_faqs": TRAFFIC_FAQS
}

def get_all_documents():
    """获取所有文档数据"""
    all_docs = []
    for category, docs in ALL_KNOWLEDGE_DATA.items():
        for doc in docs:
            # 添加集合信息到元数据
            doc['metadata']['collection'] = category
            all_docs.append(doc)
    return all_docs

def get_documents_by_category(category: str):
    """根据分类获取文档"""
    return ALL_KNOWLEDGE_DATA.get(category, [])

if __name__ == "__main__":
    # 打印统计信息
    total_docs = sum(len(docs) for docs in ALL_KNOWLEDGE_DATA.values())
    print(f"交通知识库统计：")
    print(f"总文档数: {total_docs}")
    for category, docs in ALL_KNOWLEDGE_DATA.items():
        print(f"  {category}: {len(docs)} 个文档")