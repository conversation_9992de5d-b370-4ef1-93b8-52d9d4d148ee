#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
初始化交通知识库脚本

这个脚本用于将交通法规数据导入到RAG系统的向量数据库中。
主要功能：
1. 加载交通知识数据
2. 初始化向量存储和嵌入模型
3. 批量导入文档到各个集合
4. 验证导入结果

使用方法:
python scripts/init_knowledge_base.py
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from data.knowledge.traffic_regulations import ALL_KNOWLEDGE_DATA
from src.rag.vector_store import TrafficVectorStore
from src.rag.embeddings import BGEEmbeddings
from src.rag.retrieval import TrafficKnowledgeRetriever

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('init_knowledge_base.log')
    ]
)

logger = logging.getLogger(__name__)

async def initialize_knowledge_base():
    """初始化知识库"""
    logger.info("🚀 开始初始化交通知识库...")
    
    try:
        # 1. 初始化组件
        logger.info("📦 初始化RAG组件...")
        vector_store = TrafficVectorStore()
        embeddings = BGEEmbeddings()
        retriever = TrafficKnowledgeRetriever(vector_store, embeddings)
        
        # 2. 获取当前集合状态
        logger.info("📊 检查现有集合状态...")
        collection_info = vector_store.get_collection_info()
        logger.info(f"现有集合信息: {collection_info}")
        
        # 3. 导入数据到各个集合
        total_imported = 0
        for collection_name, documents in ALL_KNOWLEDGE_DATA.items():
            logger.info(f"📚 开始导入集合: {collection_name}")
            logger.info(f"📄 文档数量: {len(documents)}")
            
            # 批量添加文档
            result = vector_store.add_documents(
                collection_name=collection_name,
                documents=documents,
                batch_size=50
            )
            
            if result["success"]:
                logger.info(f"✅ 集合 {collection_name} 导入成功:")
                logger.info(f"   - 成功: {result['success_count']}/{result['total_documents']}")
                logger.info(f"   - 耗时: {result['processing_time']}秒")
                total_imported += result['success_count']
            else:
                logger.error(f"❌ 集合 {collection_name} 导入失败:")
                logger.error(f"   - 错误: {result.get('errors', [])}")
        
        # 4. 验证导入结果
        logger.info("🔍 验证导入结果...")
        final_info = vector_store.get_collection_info()
        logger.info("📊 最终集合统计:")
        for collection, info in final_info.items():
            if collection != "summary":
                logger.info(f"   - {collection}: {info['document_count']} 个文档")
        
        logger.info(f"📈 总计导入: {total_imported} 个文档")
        
        # 5. 测试检索功能
        logger.info("🧪 测试检索功能...")
        test_queries = [
            "超速违法的处罚标准是什么？",
            "雨天驾驶需要注意什么？", 
            "醉酒驾驶的法律后果",
            "人行横道礼让规定"
        ]
        
        for query in test_queries:
            logger.info(f"🔍 测试查询: {query}")
            result = await retriever.retrieve(query, top_k=3)
            
            if result["success"]:
                logger.info(f"✅ 找到 {result['returned_count']} 个相关结果")
                logger.info(f"⏱️ 检索耗时: {result['processing_time']}秒")
                
                # 显示最相关的结果
                if result["results"]:
                    top_result = result["results"][0]
                    logger.info(f"🎯 最相关结果 (相似度: {top_result.get('similarity_score', 0):.3f}):")
                    logger.info(f"   内容片段: {top_result['content'][:100]}...")
            else:
                logger.error(f"❌ 查询失败: {result.get('error', 'Unknown error')}")
        
        # 6. 性能基准测试
        logger.info("⚡ 执行性能基准测试...")
        benchmark_result = embeddings.benchmark_performance(num_samples=50)
        if "error" not in benchmark_result:
            logger.info("📊 性能测试结果:")
            logger.info(f"   - 批量嵌入: {benchmark_result['batch_embedding']['docs_per_second']:.1f} docs/sec")
            logger.info(f"   - 单次查询: {benchmark_result['single_query']['queries_per_second']:.1f} queries/sec")
            logger.info(f"   - 设备: {benchmark_result['system_info']['device']}")
        
        logger.info("🎉 知识库初始化完成!")
        return True
        
    except Exception as e:
        logger.error(f"❌ 知识库初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚗 交通多模态智能助手 - 知识库初始化")
    print("=" * 60)
    
    # 确保数据目录存在
    data_dirs = [
        "data/vectors",
        "data/models", 
        "logs"
    ]
    
    for dir_path in data_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        logger.info(f"📁 确保目录存在: {dir_path}")
    
    # 运行初始化
    success = asyncio.run(initialize_knowledge_base())
    
    if success:
        print("\n🎉 知识库初始化成功!")
        print("💡 现在可以使用RAG功能进行智能问答了")
    else:
        print("\n❌ 知识库初始化失败!")
        print("🔧 请检查日志文件 init_knowledge_base.log 获取详细错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()