# 智能交通多模态RAG助手 - 依赖包列表
# Python 3.8+ required

# ================================
# 已安装的核心依赖包 (确切版本)
# ================================

# 核心深度学习框架
torch==2.7.1+cu118  # 已安装
torchvision==0.22.1+cu118  # 已安装
torchaudio==2.7.1+cu118  # 已安装
transformers==4.53.2  # 已安装
accelerate==1.9.0  # 已安装

# 本地模型管理
ollama==0.5.1  # 已安装

# LangChain框架生态 (已安装)
langchain==0.3.26  # 已安装
langchain-community==0.3.27  # 已安装
langchain-core==0.3.70  # 已安装
langchain-ollama==0.3.5  # 已安装
langchain-text-splitters==0.3.8  # 已安装
langsmith==0.4.8  # 已安装

# RAG相关 (已安装)
chromadb==1.0.15  # 已安装
sentence-transformers==5.0.0  # 已安装
rank-bm25==0.2.2  # 已安装

# Web框架 (已安装)
fastapi==0.115.13  # 已安装
uvicorn==0.35.0  # 已安装
streamlit==1.47.0  # 已安装
streamlit-chat==0.1.1  # 已安装

# 网络和异步处理 (已安装)
aiohttp==3.12.14  # 已安装
httpx==0.28.1  # 已安装
requests==2.32.3  # 已安装

# 数据处理 (已安装)
pandas==2.3.1  # 已安装
numpy==2.2.6  # 已安装
pillow==11.3.0  # 已安装
opencv-python==4.12.0.88  # 已安装

# 数据库
psycopg2-binary==2.9.10  # 已安装

# 系统监控和工具 (已安装)
psutil==7.0.0  # 已安装
prometheus-client==0.22.1  # 已安装

# 配置和环境管理 (已安装)
python-dotenv==1.1.1  # 已安装
pydantic==2.10.3  # 已安装
pydantic-settings==2.10.1  # 已安装

# 基础依赖 (已安装)
PyYAML==6.0.2  # 已安装
python-dateutil==2.9.0.post0  # 已安装
typing-extensions==4.12.2  # 已安装
packaging==24.2  # 已安装

# ================================
# 可选依赖包 (需要时安装)
# ================================

# 测试框架
pytest>=7.4.0
pytest-asyncio>=0.23.0
pytest-mock>=3.11.1

# 代码质量
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0

# 数据处理扩展
redis>=4.5.0
joblib>=1.3.0

# 文档处理
pypdf2>=3.0.0
python-docx>=0.8.11
beautifulsoup4>=4.12.0

# 中文文本处理
jieba>=0.42.1

# 数据可视化
plotly>=5.15.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Jupyter支持
jupyter>=1.0.0
ipykernel>=6.25.0
ipywidgets>=8.0.0

# 机器学习扩展
scikit-learn>=1.3.0
lightgbm>=4.0.0

# 监控和调试
tensorboard>=2.13.0
wandb>=0.15.0

# API文档
mkdocs>=1.5.0
mkdocs-material>=9.0.0

# ================================
# 生产环境扩展包 (生产部署时需要)
# ================================

# 性能优化
gunicorn>=21.0.0
gevent>=23.0.0

# 安全
cryptography>=41.0.0
python-jose>=3.3.0

# 监控告警
sentry-sdk>=1.30.0

# 负载均衡
nginx  # 系统级安装

# 容器编排
docker-compose  # 系统级安装
kubernetes  # 系统级安装

# ================================
# 开发环境工具 (开发时使用)
# ================================

# 代码格式化和检查
pre-commit>=3.3.0
mypy>=1.5.0
bandit>=1.7.5

# 依赖管理
pip-tools>=7.0.0
pipreqs>=0.4.13

# 性能分析
memory-profiler>=0.61.0
line-profiler>=4.1.0

# 文档生成
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0

# ================================
# 注意事项
# ================================
# 
# 1. 核心依赖已全部安装并验证可用
# 2. 可选依赖可根据需要选择性安装
# 3. 生产环境包在部署时安装
# 4. 开发工具包在开发环境中安装
# 
# 安装命令:
# pip install -r requirements.txt  # 安装所有依赖
# pip install package_name         # 安装单个包
# 
# 更新命令:
# pip install --upgrade package_name
# 
# 查看已安装:
# pip list
# pip show package_name