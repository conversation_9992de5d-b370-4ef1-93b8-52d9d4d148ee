"""
交通知识检索系统

基于向量相似度和混合检索策略的智能知识检索器，为RAG系统提供高质量的知识检索服务。

核心功能：
1. 多策略检索 - 结合语义相似度、关键词匹配、分类过滤等多种策略
2. 查询理解 - 智能解析用户查询意图，提取关键信息  
3. 结果重排序 - 基于相关性和质量分数对检索结果进行重新排序
4. 上下文构建 - 将检索结果整合为结构化的上下文信息
5. 缓存优化 - 常见查询结果缓存，提升响应速度

检索策略：
- 语义检索: 基于向量相似度的深度语义匹配
- 关键词检索: 基于关键词的精确匹配和模糊匹配
- 混合检索: 融合多种检索方式，平衡准确性和召回率
- 分类检索: 根据查询类型选择相应的知识集合

质量保证：
- 相关性评分: 多维度评估检索结果的相关性
- 结果去重: 避免重复信息影响用户体验
- 质量过滤: 过滤低质量和无关结果
- 答案完整性: 确保检索结果能够充分回答用户问题
"""

from typing import List, Dict, Any, Optional, Tuple, Set
import logging
import time
import re
import asyncio
from collections import defaultdict
import hashlib

from .vector_store import TrafficVectorStore
from .embeddings import BGEEmbeddings

# 获取日志记录器
logger = logging.getLogger(__name__)

class TrafficKnowledgeRetriever:
    """
    交通知识检索器
    
    整合向量数据库和嵌入模型，提供智能化的交通知识检索服务。
    支持多种检索策略和查询优化，确保检索结果的准确性和完整性。
    
    Attributes:
        vector_store (TrafficVectorStore): 向量数据库实例
        embeddings (BGEEmbeddings): 嵌入模型实例
        query_cache (Dict): 查询结果缓存
        
    学习要点：
    - 多策略检索的设计和实现
    - 查询意图识别和处理
    - 检索结果的评估和优化
    - 缓存机制的应用
    """
    
    def __init__(self, 
                 vector_store: Optional[TrafficVectorStore] = None,
                 embeddings: Optional[BGEEmbeddings] = None,
                 cache_size: int = 1000):
        """
        初始化知识检索器
        
        Args:
            vector_store: 向量数据库实例
            embeddings: 嵌入模型实例  
            cache_size: 缓存大小限制
        """
        # 初始化核心组件
        self.vector_store = vector_store or TrafficVectorStore()
        self.embeddings = embeddings or BGEEmbeddings()
        
        # 查询缓存
        self.query_cache = {}
        self.cache_size = cache_size
        
        # 检索统计
        self.retrieval_stats = {
            "total_queries": 0,
            "cache_hits": 0,
            "avg_response_time": 0.0,
            "successful_queries": 0
        }
        
        # 预定义的检索配置
        self.retrieval_config = {
            "default_top_k": 5,
            "similarity_threshold": 0.3,
            "max_context_length": 4000,
            "rerank_enabled": True,
            "cache_enabled": True
        }
        
        # 查询分类规则
        self.query_categories = {
            "violation": ["违规", "违法", "处罚", "罚款", "扣分", "禁止"],
            "regulation": ["法规", "条例", "规定", "法律", "标准"],
            "safety": ["安全", "事故", "风险", "危险", "预防"],
            "procedure": ["流程", "程序", "步骤", "办理", "申请"],
            "penalty": ["处罚", "罚金", "吊销", "暂扣", "记分"]
        }
        
        logger.info("✅ 交通知识检索器初始化完成")
    
    async def retrieve(self, 
                      query: str, 
                      top_k: int = None,
                      collection_filter: Optional[List[str]] = None,
                      enable_rerank: bool = None) -> Dict[str, Any]:
        """
        执行知识检索
        
        Args:
            query: 用户查询文本
            top_k: 返回结果数量
            collection_filter: 限制搜索的集合列表
            enable_rerank: 是否启用重排序
            
        Returns:
            Dict: 检索结果，包含文档、分数、元数据等信息
            
        学习要点：
        - 异步检索的实现
        - 多集合并行搜索
        - 结果合并和排序
        - 性能监控和优化
        """
        # 更新检索统计
        self.retrieval_stats["total_queries"] += 1
        start_time = time.time()
        
        try:
            logger.info(f"🔍 开始检索: {query[:100]}...")
            
            # 参数处理
            top_k = top_k or self.retrieval_config["default_top_k"]
            enable_rerank = enable_rerank if enable_rerank is not None else self.retrieval_config["rerank_enabled"]
            
            # 检查缓存
            if self.retrieval_config["cache_enabled"]:
                cache_key = self._generate_cache_key(query, top_k, collection_filter)
                if cache_key in self.query_cache:
                    self.retrieval_stats["cache_hits"] += 1
                    logger.info("⚡ 命中查询缓存")
                    return self.query_cache[cache_key]
            
            # 查询预处理和分析
            processed_query = self._preprocess_query(query)
            query_info = self._analyze_query(processed_query)
            
            # 确定搜索集合
            target_collections = collection_filter or self._select_collections(query_info)
            
            # 执行多集合并行检索
            all_results = await self._parallel_search(
                processed_query, 
                target_collections, 
                top_k * 2  # 获取更多结果用于重排序
            )
            
            # 结果处理和重排序
            if enable_rerank:
                ranked_results = self._rerank_results(processed_query, all_results, query_info)
            else:
                ranked_results = all_results
            
            # 截取最终结果
            final_results = ranked_results[:top_k]
            
            # 构建返回结果
            result = {
                "success": True,
                "query": query,
                "processed_query": processed_query,
                "query_info": query_info,
                "results": final_results,
                "total_found": len(all_results),
                "returned_count": len(final_results),
                "collections_searched": target_collections,
                "processing_time": round(time.time() - start_time, 3),
                "from_cache": False
            }
            
            # 更新缓存
            if self.retrieval_config["cache_enabled"]:
                self._update_cache(cache_key, result)
            
            # 更新统计
            self.retrieval_stats["successful_queries"] += 1
            avg_time = self.retrieval_stats["avg_response_time"]
            total_queries = self.retrieval_stats["successful_queries"]
            self.retrieval_stats["avg_response_time"] = (
                (avg_time * (total_queries - 1) + result["processing_time"]) / total_queries
            )
            
            logger.info(f"✅ 检索完成: 找到 {len(final_results)} 个结果, 耗时 {result['processing_time']:.3f}秒")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 检索失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "processing_time": time.time() - start_time
            }
    
    def _preprocess_query(self, query: str) -> str:
        """
        查询预处理
        
        Args:
            query: 原始查询文本
            
        Returns:
            str: 预处理后的查询文本
            
        学习要点：
        - 文本清理和标准化
        - 同义词扩展
        - 查询优化技术
        """
        if not query or not query.strip():
            return ""
        
        # 基础清理
        processed = query.strip()
        
        # 去除多余空格和标点
        processed = re.sub(r'\s+', ' ', processed)
        processed = re.sub(r'[^\w\s\u4e00-\u9fff]', ' ', processed)
        
        # 同义词替换(简单示例)
        synonyms = {
            "汽车": "机动车",
            "开车": "驾驶", 
            "罚钱": "罚款",
            "扣钱": "罚款"
        }
        
        for synonym, standard in synonyms.items():
            processed = processed.replace(synonym, standard)
        
        logger.debug(f"查询预处理: '{query}' -> '{processed}'")
        return processed
    
    def _analyze_query(self, query: str) -> Dict[str, Any]:
        """
        分析查询意图和特征
        
        Args:
            query: 预处理后的查询文本
            
        Returns:
            Dict: 查询分析结果
            
        学习要点：
        - 意图识别技术
        - 关键词提取
        - 查询分类方法
        """
        analysis_result = {
            "categories": [],
            "keywords": [],
            "intent": "general",
            "urgency": "normal",
            "entities": []
        }
        
        if not query:
            return analysis_result
        
        # 分类识别
        for category, keywords in self.query_categories.items():
            if any(keyword in query for keyword in keywords):
                analysis_result["categories"].append(category)
        
        # 关键词提取(简单实现)
        # 在实际应用中可以使用更复杂的NLP技术
        words = query.split()
        important_words = [word for word in words if len(word) > 1]
        analysis_result["keywords"] = important_words[:5]  # 限制关键词数量
        
        # 意图识别
        if "如何" in query or "怎么" in query:
            analysis_result["intent"] = "how_to"
        elif "什么" in query or "哪些" in query:
            analysis_result["intent"] = "what_is"
        elif "多少" in query or "几" in query:
            analysis_result["intent"] = "quantity"
        elif any(cat in ["violation", "penalty"] for cat in analysis_result["categories"]):
            analysis_result["intent"] = "penalty_inquiry"
        
        # 紧急程度
        urgent_indicators = ["紧急", "急", "马上", "立即"]
        if any(indicator in query for indicator in urgent_indicators):
            analysis_result["urgency"] = "high"
        
        logger.debug(f"查询分析结果: {analysis_result}")
        return analysis_result
    
    def _select_collections(self, query_info: Dict[str, Any]) -> List[str]:
        """
        根据查询信息选择搜索集合
        
        Args:
            query_info: 查询分析结果
            
        Returns:
            List[str]: 目标集合列表
            
        学习要点：
        - 集合选择策略
        - 查询路由技术
        - 性能优化考虑
        """
        selected_collections = []
        
        # 根据查询分类选择集合
        category_mapping = {
            "violation": ["traffic_regulations", "traffic_cases"],
            "regulation": ["traffic_regulations", "traffic_standards"],
            "safety": ["traffic_cases", "traffic_standards", "traffic_faqs"],
            "procedure": ["traffic_faqs", "traffic_standards"],
            "penalty": ["traffic_regulations", "traffic_cases"]
        }
        
        for category in query_info.get("categories", []):
            if category in category_mapping:
                selected_collections.extend(category_mapping[category])
        
        # 去重并保持顺序
        if selected_collections:
            selected_collections = list(dict.fromkeys(selected_collections))
        else:
            # 默认搜索所有集合
            selected_collections = ["traffic_regulations", "traffic_cases", "traffic_standards", "traffic_faqs"]
        
        logger.debug(f"选择搜索集合: {selected_collections}")
        return selected_collections
    
    async def _parallel_search(self, 
                              query: str, 
                              collections: List[str], 
                              top_k: int) -> List[Dict[str, Any]]:
        """
        并行搜索多个集合
        
        Args:
            query: 查询文本
            collections: 目标集合列表
            top_k: 每个集合返回的结果数量
            
        Returns:
            List[Dict]: 合并后的搜索结果
            
        学习要点：
        - 异步并行搜索
        - 结果合并策略
        - 异常处理和容错
        """
        search_tasks = []
        
        # 为每个集合创建搜索任务
        for collection in collections:
            task = asyncio.create_task(
                self._search_single_collection(collection, query, top_k)
            )
            search_tasks.append((collection, task))
        
        # 并行执行搜索任务
        all_results = []
        for collection, task in search_tasks:
            try:
                results = await task
                for result in results:
                    result["source_collection"] = collection
                all_results.extend(results)
                
                logger.debug(f"集合 '{collection}' 返回 {len(results)} 个结果")
                
            except Exception as e:
                logger.error(f"❌ 集合 '{collection}' 搜索失败: {e}")
        
        # 按相似度分数排序
        all_results.sort(key=lambda x: x.get("similarity_score", 0), reverse=True)
        
        return all_results
    
    async def _search_single_collection(self, 
                                       collection: str, 
                                       query: str, 
                                       top_k: int) -> List[Dict[str, Any]]:
        """
        搜索单个集合
        
        Args:
            collection: 集合名称
            query: 查询文本
            top_k: 返回结果数量
            
        Returns:
            List[Dict]: 搜索结果
        """
        try:
            # 这里使用同步的搜索方法，在实际应用中可以进一步优化为异步
            results = self.vector_store.search_documents(
                collection_name=collection,
                query=query,
                n_results=top_k
            )
            
            return results
            
        except Exception as e:
            logger.error(f"集合 '{collection}' 搜索异常: {e}")
            return []
    
    def _rerank_results(self, 
                       query: str, 
                       results: List[Dict[str, Any]], 
                       query_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        重排序搜索结果
        
        Args:
            query: 查询文本
            results: 原始搜索结果
            query_info: 查询分析信息
            
        Returns:
            List[Dict]: 重排序后的结果
            
        学习要点：
        - 多因子评分机制
        - 相关性计算方法
        - 排序算法优化
        """
        if not results:
            return results
        
        logger.debug(f"开始重排序 {len(results)} 个结果")
        
        # 为每个结果计算综合分数
        for result in results:
            score_components = {}
            
            # 1. 原始相似度分数 (权重: 0.4)
            similarity_score = result.get("similarity_score", 0)
            score_components["similarity"] = similarity_score * 0.4
            
            # 2. 关键词匹配分数 (权重: 0.3)
            keyword_score = self._calculate_keyword_score(query, result.get("content", ""))
            score_components["keyword"] = keyword_score * 0.3
            
            # 3. 集合优先级分数 (权重: 0.1)
            collection_score = self._get_collection_priority(
                result.get("source_collection", ""), 
                query_info
            )
            score_components["collection"] = collection_score * 0.1
            
            # 4. 文档质量分数 (权重: 0.1)
            quality_score = self._assess_document_quality(result)
            score_components["quality"] = quality_score * 0.1
            
            # 5. 长度适应性分数 (权重: 0.1)
            length_score = self._calculate_length_score(result.get("content", ""))
            score_components["length"] = length_score * 0.1
            
            # 计算总分
            total_score = sum(score_components.values())
            result["rerank_score"] = total_score
            result["score_components"] = score_components
        
        # 按重排序分数排序
        reranked_results = sorted(results, key=lambda x: x["rerank_score"], reverse=True)
        
        logger.debug(f"重排序完成，前3个结果分数: {[r['rerank_score'] for r in reranked_results[:3]]}")
        
        return reranked_results
    
    def _calculate_keyword_score(self, query: str, content: str) -> float:
        """计算关键词匹配分数"""
        if not query or not content:
            return 0.0
        
        query_words = set(query.lower().split())
        content_words = set(content.lower().split())
        
        if not query_words:
            return 0.0
        
        # 计算交集比例
        intersection = query_words.intersection(content_words)
        return len(intersection) / len(query_words)
    
    def _get_collection_priority(self, collection: str, query_info: Dict[str, Any]) -> float:
        """获取集合优先级分数"""
        collection_priorities = {
            "traffic_regulations": 1.0,
            "traffic_cases": 0.8,
            "traffic_standards": 0.6,
            "traffic_faqs": 0.4
        }
        
        return collection_priorities.get(collection, 0.5)
    
    def _assess_document_quality(self, result: Dict[str, Any]) -> float:
        """评估文档质量"""
        content = result.get("content", "")
        metadata = result.get("metadata", {})
        
        quality_score = 0.5  # 基础分数
        
        # 内容长度适中
        content_length = len(content)
        if 100 <= content_length <= 2000:
            quality_score += 0.2
        
        # 有结构化元数据
        if metadata.get("source") and metadata.get("article"):
            quality_score += 0.2
        
        # 内容完整性
        if content.count("。") >= 2:  # 至少包含2个句子
            quality_score += 0.1
        
        return min(quality_score, 1.0)
    
    def _calculate_length_score(self, content: str) -> float:
        """计算长度适应性分数"""
        length = len(content)
        
        # 理想长度范围: 200-1000字符
        if 200 <= length <= 1000:
            return 1.0
        elif 100 <= length < 200 or 1000 < length <= 2000:
            return 0.7
        elif 50 <= length < 100 or 2000 < length <= 3000:
            return 0.4
        else:
            return 0.2
    
    def _generate_cache_key(self, 
                           query: str, 
                           top_k: int, 
                           collection_filter: Optional[List[str]]) -> str:
        """生成缓存键"""
        key_components = [
            query.lower().strip(),
            str(top_k),
            str(sorted(collection_filter) if collection_filter else "all")
        ]
        key_string = "|".join(key_components)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def _update_cache(self, cache_key: str, result: Dict[str, Any]):
        """更新查询缓存"""
        # 缓存大小限制
        if len(self.query_cache) >= self.cache_size:
            # 删除最旧的缓存项 (简单LRU实现)
            oldest_key = next(iter(self.query_cache))
            del self.query_cache[oldest_key]
        
        # 添加新缓存项
        cached_result = result.copy()
        cached_result["from_cache"] = True
        self.query_cache[cache_key] = cached_result
    
    def get_retrieval_stats(self) -> Dict[str, Any]:
        """获取检索统计信息"""
        stats = self.retrieval_stats.copy()
        stats["cache_hit_rate"] = (
            stats["cache_hits"] / max(stats["total_queries"], 1)
        )
        stats["success_rate"] = (
            stats["successful_queries"] / max(stats["total_queries"], 1)
        )
        return stats
    
    def clear_cache(self):
        """清空查询缓存"""
        self.query_cache.clear()
        logger.info("🧹 查询缓存已清空")