"""
交通领域向量数据库实现

基于ChromaDB构建的专业交通知识向量存储系统，为RAG检索提供高效的向量化知识管理。

主要功能：
1. 多集合管理 - 分别存储法规、案例、标准等不同类型知识
2. 向量化存储 - 自动将文档转换为向量并持久化存储  
3. 相似度检索 - 支持语义相似度查询和混合检索策略
4. 元数据管理 - 丰富的文档元信息支持精确过滤
5. 性能优化 - 批量操作和缓存机制提升检索效率

技术特点：
- 使用duckdb+parquet后端，保证数据持久化和查询性能
- 支持cosine相似度计算，优化中文语义理解
- 集合级别的独立管理，便于不同知识类型的组织
- 完整的错误处理和日志记录，保证系统稳定性
"""

import chromadb
from chromadb.config import Settings
from typing import List, Dict, Any, Optional, Union
import logging
from pathlib import Path
import uuid
import time

# 获取日志记录器
logger = logging.getLogger(__name__)

class TrafficVectorStore:
    """
    交通领域向量数据库管理类
    
    封装ChromaDB的操作，提供交通领域专用的向量存储和检索功能。
    支持多种文档类型的存储，包括交通法规、违规案例、技术标准等。
    
    Attributes:
        persist_directory (str): 数据持久化目录
        client (chromadb.PersistentClient): ChromaDB客户端实例
        collections (Dict): 已创建的集合缓存
        
    学习要点：
    - ChromaDB的初始化和配置
    - 向量集合的创建和管理
    - 批量文档添加和查询优化
    - 元数据的有效利用
    """
    
    def __init__(self, persist_directory: str = "./data/vectors"):
        """
        初始化向量数据库
        
        Args:
            persist_directory: 数据持久化目录路径
        """
        self.persist_directory = persist_directory
        self.collections = {}  # 集合缓存
        
        # 确保持久化目录存在
        Path(persist_directory).mkdir(parents=True, exist_ok=True)
        
        try:
            # 初始化ChromaDB客户端 (新版本API)
            # 使用PersistentClient确保数据持久化
            self.client = chromadb.PersistentClient(
                path=persist_directory
            )
            
            # 预创建核心集合
            self._initialize_collections()
            
            logger.info(f"✅ 向量数据库初始化成功，目录: {persist_directory}")
            
        except Exception as e:
            logger.error(f"❌ 向量数据库初始化失败: {e}")
            raise e
    
    def _initialize_collections(self):
        """初始化核心集合"""
        collections_config = {
            "traffic_regulations": {
                "metadata": {"hnsw:space": "cosine", "description": "交通法规知识库"},
                "description": "存储交通法律法规、规章制度等官方文件"
            },
            "traffic_cases": {
                "metadata": {"hnsw:space": "cosine", "description": "交通案例库"},
                "description": "存储交通违规案例、处罚决定、典型事故等"
            },
            "traffic_standards": {
                "metadata": {"hnsw:space": "cosine", "description": "技术标准库"},
                "description": "存储道路设计标准、交通设施规范等技术文档"
            },
            "traffic_faqs": {
                "metadata": {"hnsw:space": "cosine", "description": "常见问题库"},
                "description": "存储常见交通问题及解答"
            }
        }
        
        for collection_name, config in collections_config.items():
            try:
                collection = self.client.get_or_create_collection(
                    name=collection_name,
                    metadata=config["metadata"]
                )
                self.collections[collection_name] = collection
                logger.info(f"📚 集合 '{collection_name}' 初始化完成")
                
            except Exception as e:
                logger.error(f"❌ 集合 '{collection_name}' 初始化失败: {e}")
    
    def add_documents(self, 
                     collection_name: str,
                     documents: List[Dict[str, Any]],
                     batch_size: int = 100) -> Dict[str, Any]:
        """
        批量添加文档到指定集合
        
        Args:
            collection_name: 目标集合名称
            documents: 文档列表，每个文档包含content, metadata等字段
            batch_size: 批处理大小，默认100
            
        Returns:
            Dict: 添加结果，包含成功数量、失败数量等统计信息
            
        学习要点：
        - 批量处理提高性能
        - 文档ID的自动生成策略
        - 元数据的标准化处理
        - 错误处理和统计反馈
        """
        if collection_name not in self.collections:
            raise ValueError(f"集合 '{collection_name}' 不存在")
        
        collection = self.collections[collection_name]
        
        total_docs = len(documents)
        success_count = 0
        error_count = 0
        errors = []
        
        logger.info(f"📝 开始向集合 '{collection_name}' 添加 {total_docs} 个文档")
        start_time = time.time()
        
        # 分批处理文档
        for i in range(0, total_docs, batch_size):
            batch_docs = documents[i:i + batch_size]
            
            try:
                # 准备批量数据
                ids = []
                texts = []
                metadatas = []
                
                for doc in batch_docs:
                    # 生成唯一ID
                    doc_id = doc.get("id", str(uuid.uuid4()))
                    ids.append(doc_id)
                    
                    # 提取文档内容
                    content = doc.get("content", "")
                    if not content:
                        logger.warning(f"文档 {doc_id} 内容为空，跳过")
                        continue
                    texts.append(content)
                    
                    # 处理元数据
                    metadata = doc.get("metadata", {})
                    # 添加时间戳和集合信息
                    metadata.update({
                        "collection": collection_name,
                        "added_time": time.time(),
                        "document_type": doc.get("type", "unknown"),
                        "source": doc.get("source", "unknown")
                    })
                    metadatas.append(metadata)
                
                # 批量添加到ChromaDB
                if texts:  # 确保有有效内容
                    collection.add(
                        documents=texts,
                        metadatas=metadatas,
                        ids=ids[:len(texts)]  # 确保ID数量匹配
                    )
                    success_count += len(texts)
                    
                logger.info(f"✅ 批次 {i//batch_size + 1}: 成功添加 {len(texts)} 个文档")
                
            except Exception as e:
                batch_error = f"批次 {i//batch_size + 1} 处理失败: {str(e)}"
                logger.error(f"❌ {batch_error}")
                errors.append(batch_error)
                error_count += len(batch_docs)
        
        processing_time = time.time() - start_time
        
        # 构建结果统计
        result = {
            "success": error_count == 0,
            "total_documents": total_docs,
            "success_count": success_count,
            "error_count": error_count,
            "processing_time": round(processing_time, 2),
            "collection": collection_name,
            "errors": errors
        }
        
        logger.info(f"📊 文档添加完成: 成功 {success_count}/{total_docs}, 耗时 {processing_time:.2f}秒")
        
        return result
    
    def search_documents(self,
                        collection_name: str,
                        query: str,
                        n_results: int = 5,
                        filter_metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        在指定集合中搜索相关文档
        
        Args:
            collection_name: 搜索的集合名称
            query: 查询文本
            n_results: 返回结果数量
            filter_metadata: 元数据过滤条件
            
        Returns:
            List[Dict]: 搜索结果列表，包含文档内容、相似度分、元数据等
            
        学习要点：
        - 向量相似度检索的实现
        - 元数据过滤的应用
        - 结果格式化和排序
        - 检索性能的监控
        """
        if collection_name not in self.collections:
            logger.error(f"集合 '{collection_name}' 不存在")
            return []
        
        collection = self.collections[collection_name]
        
        try:
            logger.info(f"🔍 在集合 '{collection_name}' 中搜索: {query[:50]}...")
            start_time = time.time()
            
            # 构建查询参数
            query_params = {
                "query_texts": [query],
                "n_results": n_results
            }
            
            # 添加元数据过滤
            if filter_metadata:
                query_params["where"] = filter_metadata
            
            # 执行查询
            results = collection.query(**query_params)
            
            search_time = time.time() - start_time
            
            # 格式化结果
            formatted_results = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    result_item = {
                        "content": doc,
                        "similarity_score": 1 - results['distances'][0][i] if results['distances'] and results['distances'][0] else 0.0,
                        "metadata": results['metadatas'][0][i] if results['metadatas'] and results['metadatas'][0] else {},
                        "document_id": results['ids'][0][i] if results['ids'] and results['ids'][0] else f"doc_{i}",
                        "collection": collection_name
                    }
                    formatted_results.append(result_item)
            
            logger.info(f"✅ 搜索完成: 找到 {len(formatted_results)} 个结果, 耗时 {search_time:.2f}秒")
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"❌ 搜索失败: {e}")
            return []
    
    def get_collection_info(self, collection_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取集合统计信息
        
        Args:
            collection_name: 指定集合名称，为None时返回所有集合信息
            
        Returns:
            Dict: 集合统计信息
        """
        try:
            if collection_name:
                # 返回指定集合信息
                if collection_name not in self.collections:
                    return {"error": f"集合 '{collection_name}' 不存在"}
                
                collection = self.collections[collection_name]
                count = collection.count()
                
                return {
                    "collection_name": collection_name,
                    "document_count": count,
                    "status": "active"
                }
            else:
                # 返回所有集合信息
                all_info = {}
                total_documents = 0
                
                for name, collection in self.collections.items():
                    count = collection.count()
                    all_info[name] = {
                        "document_count": count,
                        "status": "active"
                    }
                    total_documents += count
                
                all_info["summary"] = {
                    "total_collections": len(self.collections),
                    "total_documents": total_documents,
                    "persist_directory": self.persist_directory
                }
                
                return all_info
                
        except Exception as e:
            logger.error(f"❌ 获取集合信息失败: {e}")
            return {"error": str(e)}
    
    def delete_documents(self, 
                        collection_name: str, 
                        document_ids: List[str]) -> Dict[str, Any]:
        """
        删除指定文档
        
        Args:
            collection_name: 集合名称
            document_ids: 要删除的文档ID列表
            
        Returns:
            Dict: 删除操作结果
        """
        if collection_name not in self.collections:
            return {"success": False, "error": f"集合 '{collection_name}' 不存在"}
        
        collection = self.collections[collection_name]
        
        try:
            collection.delete(ids=document_ids)
            
            logger.info(f"🗑️ 从集合 '{collection_name}' 删除了 {len(document_ids)} 个文档")
            
            return {
                "success": True,
                "deleted_count": len(document_ids),
                "collection": collection_name
            }
            
        except Exception as e:
            logger.error(f"❌ 删除文档失败: {e}")
            return {"success": False, "error": str(e)}
    
    def reset_collection(self, collection_name: str) -> Dict[str, Any]:
        """
        重置指定集合（清空所有数据）
        
        Args:
            collection_name: 要重置的集合名称
            
        Returns:
            Dict: 重置操作结果
        """
        try:
            if collection_name in self.collections:
                # 删除现有集合
                self.client.delete_collection(collection_name)
                
                # 重新创建集合
                collection = self.client.create_collection(
                    name=collection_name,
                    metadata={"hnsw:space": "cosine"}
                )
                self.collections[collection_name] = collection
                
                logger.info(f"🔄 集合 '{collection_name}' 重置完成")
                
                return {
                    "success": True,
                    "message": f"集合 '{collection_name}' 已重置",
                    "collection": collection_name
                }
            else:
                return {"success": False, "error": f"集合 '{collection_name}' 不存在"}
                
        except Exception as e:
            logger.error(f"❌ 集合重置失败: {e}")
            return {"success": False, "error": str(e)}