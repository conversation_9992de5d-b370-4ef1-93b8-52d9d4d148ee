#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TrafficMultimodalAgent真实图像完整功能测试

这个测试脚本用于验证Agent对真实图像的完整分析能力，包括：
1. Agent初始化和工具加载
2. 真实图像读取和base64转换
3. 各个工具对真实图像的分析能力
4. Agent完整查询处理流程
5. 工具统计和性能评估

使用用户提供的真实交通场景图片：截图 2025-07-24 20-37-56.png

运行方法：
    cd /home/<USER>/lhp/projects/0714agent/my-agent1
    python tests/test_agent_with_real_image.py
"""

import asyncio
import sys
import base64
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.agent.traffic_multimodal_agent import TrafficMultimodalAgent
from src.utils.logger import get_logger

logger = get_logger(__name__)

def load_real_image():
    """
    加载用户提供的真实图像
    
    Returns:
        dict: 包含图像数据的字典
    """
    image_path = project_root / "data" / "test_images" / "截图 2025-07-24 20-37-56.png"
    
    try:
        # 读取图像文件
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        # 转换为base64
        base64_image = base64.b64encode(image_data).decode('utf-8')
        
        logger.info(f"✅ 成功加载图像: {image_path.name}")
        logger.info(f"📊 图像大小: {len(image_data)} bytes")
        logger.info(f"🔗 Base64长度: {len(base64_image)} 字符")
        
        return {
            "base64_image": base64_image,
            "file_path": str(image_path),
            "file_name": image_path.name,
            "file_size": len(image_data),
            "format": "PNG"
        }
        
    except Exception as e:
        logger.error(f"❌ 加载图像失败: {e}")
        return None

async def test_agent_initialization():
    """测试Agent初始化"""
    logger.info("🧪 测试Agent初始化...")
    
    try:
        agent = TrafficMultimodalAgent()
        await agent.initialize()
        
        # 验证关键组件
        assert agent.image_analyzer is not None, "图像分析工具未初始化"
        assert agent.violation_detector is not None, "违规检测工具未初始化"
        assert agent.regulation_searcher is not None, "法规检索工具未初始化"
        assert agent.safety_assessor is not None, "安全评估工具未初始化"
        assert agent.suggestion_generator is not None, "建议生成工具未初始化"
        assert len(agent.tools) == 5, f"工具数量不正确，期望5个，实际{len(agent.tools)}"
        
        logger.info("✅ Agent初始化测试通过")
        return agent
        
    except Exception as e:
        logger.error(f"❌ Agent初始化失败: {e}")
        raise e

async def test_image_loading():
    """测试图像加载功能"""
    logger.info("🧪 测试真实图像加载...")
    
    try:
        image_data = load_real_image()
        assert image_data is not None, "图像加载失败"
        assert "base64_image" in image_data, "缺少base64图像数据"
        assert len(image_data["base64_image"]) > 0, "base64图像数据为空"
        
        logger.info("✅ 图像加载测试通过")
        return image_data
        
    except Exception as e:
        logger.error(f"❌ 图像加载测试失败: {e}")
        raise e

async def test_individual_tools_with_real_image(agent, image_data):
    """测试各个工具对真实图像的分析能力"""
    logger.info("🧪 测试各工具对真实图像的分析能力...")
    
    # 设置Agent的当前图像数据
    agent._current_image_data = image_data
    
    results = {}
    
    try:
        # 1. 测试图像分析工具
        logger.info("🖼️ 测试图像分析工具...")
        image_analysis_result = await agent.image_analyzer.analyze(
            image_input=image_data["base64_image"],
            analysis_focus="交通场景分析",
            output_format="structured"
        )
        results["image_analyzer"] = {
            "success": image_analysis_result.get("success", False),
            "has_scene_description": "scene_description" in image_analysis_result,
            "has_participants": "traffic_participants" in image_analysis_result,
            "analysis_length": len(str(image_analysis_result))
        }
        logger.info(f"   图像分析结果长度: {results['image_analyzer']['analysis_length']} 字符")
        
        # 2. 测试违规检测工具 (基于图像分析结果)
        logger.info("🚨 测试违规检测工具...")
        if image_analysis_result.get("success", False):
            violation_result = await agent.violation_detector.detect(image_analysis_result)
            results["violation_detector"] = {
                "success": violation_result.get("success", False),
                "violations_found": len(violation_result.get("violations", [])),
                "total_violations": violation_result.get("total_violations", 0)
            }
            logger.info(f"   检测到违规数量: {results['violation_detector']['violations_found']}")
        else:
            results["violation_detector"] = {"success": False, "error": "图像分析失败"}
        
        # 3. 测试法规检索工具
        logger.info("📖 测试法规检索工具...")
        regulation_result = await agent.regulation_searcher.search("行人过街 车辆礼让")
        results["regulation_searcher"] = {
            "success": regulation_result.get("success", False),
            "results_count": len(regulation_result.get("results", [])),
            "has_legal_basis": any("法" in str(r) for r in regulation_result.get("results", []))
        }
        logger.info(f"   找到法规条目: {results['regulation_searcher']['results_count']}条")
        
        # 4. 测试安全评估工具
        logger.info("🛡️ 测试安全评估工具...")
        if image_analysis_result.get("success", False):
            safety_result = await agent.safety_assessor.assess(
                image_analysis_result, 
                results.get("violation_detector", {})
            )
            results["safety_assessor"] = {
                "success": safety_result.get("success", False),
                "safety_level": safety_result.get("safety_level", "未知"),
                "has_risk_factors": "risk_factors" in safety_result
            }
            logger.info(f"   安全等级: {results['safety_assessor']['safety_level']}")
        else:
            results["safety_assessor"] = {"success": False, "error": "依赖图像分析结果"}
        
        # 5. 测试建议生成工具
        logger.info("💡 测试建议生成工具...")
        if image_analysis_result.get("success", False):
            suggestion_result = await agent.suggestion_generator.generate(
                image_analysis_result,
                results.get("violation_detector", {}),
                {"safety_assessment": results.get("safety_assessor", {})}
            )
            results["suggestion_generator"] = {
                "success": suggestion_result.get("success", False),
                "suggestions_count": len(suggestion_result.get("suggestions", [])),
                "has_improvement_measures": "improvement_measures" in suggestion_result
            }
            logger.info(f"   生成建议数量: {results['suggestion_generator']['suggestions_count']}")
        else:
            results["suggestion_generator"] = {"success": False, "error": "依赖图像分析结果"}
        
        # 汇总工具测试结果
        logger.info("📊 各工具测试结果:")
        successful_tools = 0
        for tool_name, result in results.items():
            success = result.get("success", False)
            status = "✅ 通过" if success else "❌ 失败"
            logger.info(f"   - {tool_name}: {status}")
            if success:
                successful_tools += 1
        
        logger.info(f"🎯 工具测试成功率: {successful_tools}/{len(results)}")
        
        if successful_tools >= 3:  # 至少3个工具成功
            logger.info("✅ 工具功能测试通过")
            return True, results
        else:
            logger.error("❌ 工具功能测试失败 - 成功工具数量不足")
            return False, results
            
    except Exception as e:
        logger.error(f"❌ 工具功能测试失败: {e}")
        return False, results

async def test_complete_query_with_real_image(agent, image_data):
    """测试Agent对真实图像的完整查询处理"""
    logger.info("🧪 测试Agent完整查询处理...")
    
    # 设置Agent的当前图像数据
    agent._current_image_data = image_data
    
    # 测试查询列表
    test_queries = [
        "请分析这张交通场景图片，识别其中的违规行为",
        "这个场景中车辆和行人的互动情况如何？",
        "评估这个交通场景的安全等级",
        "针对图片中的情况给出改进建议"
    ]
    
    results = []
    
    for i, query in enumerate(test_queries, 1):
        logger.info(f"🔍 测试查询 {i}: {query}")
        
        try:
            # 执行查询，设置超时时间
            result = await asyncio.wait_for(
                agent.process_query(query), 
                timeout=60  # 增加超时时间，因为图像分析需要更多时间
            )
            
            if result.get("success", False):
                answer = result.get("answer", "")
                
                # 分析答案质量
                quality_metrics = analyze_real_image_answer_quality(answer, query)
                
                logger.info(f"✅ 查询 {i} 成功")
                logger.info(f"📝 回答长度: {len(answer)} 字符")
                logger.info(f"⭐ 质量评分: {quality_metrics['total_score']}/10")
                logger.info(f"📄 回答预览: {answer[:200]}...")
                
                results.append({
                    "query": query,
                    "success": True,
                    "answer_length": len(answer),
                    "quality_metrics": quality_metrics,
                    "answer_preview": answer[:300]
                })
            else:
                logger.error(f"❌ 查询 {i} 失败: {result.get('error', '未知错误')}")
                results.append({
                    "query": query,
                    "success": False,
                    "error": result.get('error', '未知错误')
                })
                
        except asyncio.TimeoutError:
            logger.error(f"⏰ 查询 {i} 超时")
            results.append({
                "query": query,
                "success": False,
                "error": "查询超时"
            })
        except Exception as e:
            logger.error(f"❌ 查询 {i} 出现异常: {e}")
            results.append({
                "query": query,
                "success": False,
                "error": str(e)
            })
        
        logger.info("-" * 40)
    
    # 汇总查询测试结果
    successful_queries = sum(1 for r in results if r["success"])
    total_queries = len(results)
    
    logger.info(f"🎯 查询测试成功率: {successful_queries}/{total_queries}")
    
    if successful_queries > 0:
        avg_length = sum(r.get("answer_length", 0) for r in results if r["success"]) / successful_queries
        avg_quality = sum(r.get("quality_metrics", {}).get("total_score", 0) for r in results if r["success"]) / successful_queries
        
        logger.info(f"📝 平均回答长度: {avg_length:.0f} 字符")
        logger.info(f"⭐ 平均质量评分: {avg_quality:.1f}/10")
    
    if successful_queries >= 3:  # 至少3个查询成功
        logger.info("✅ 完整查询测试通过")
        return True, results
    else:
        logger.error("❌ 完整查询测试失败")
        return False, results

def analyze_real_image_answer_quality(answer, query):
    """
    分析真实图像回答质量
    
    Args:
        answer (str): Agent的回答
        query (str): 用户查询
        
    Returns:
        dict: 质量分析结果
    """
    metrics = {
        "length_score": 0,      # 长度得分 (0-2)
        "content_score": 0,     # 内容相关性得分 (0-3)
        "structure_score": 0,   # 结构完整性得分 (0-2)
        "specificity_score": 0, # 具体性得分 (0-3)
        "total_score": 0
    }
    
    # 1. 长度评分
    if len(answer) > 200:
        metrics["length_score"] = 2
    elif len(answer) > 100:
        metrics["length_score"] = 1
    
    # 2. 内容相关性评分
    content_keywords = {
        "交通元素": ["车辆", "行人", "道路", "人行横道", "交通"],
        "安全分析": ["安全", "风险", "危险", "评估", "等级"],
        "法规相关": ["法", "条", "规定", "《", "》", "违规", "违法"]
    }
    
    content_score = 0
    for category, keywords in content_keywords.items():
        if any(keyword in answer for keyword in keywords):
            content_score += 1
    metrics["content_score"] = min(content_score, 3)
    
    # 3. 结构完整性评分
    structure_indicators = ["。", "；", "：", "\n"]
    if sum(1 for indicator in structure_indicators if indicator in answer) >= 3:
        metrics["structure_score"] = 2
    elif sum(1 for indicator in structure_indicators if indicator in answer) >= 1:
        metrics["structure_score"] = 1
    
    # 4. 具体性评分
    specificity_keywords = ["图片中", "场景中", "可以看到", "观察到", "显示", "图像"]
    specificity_score = sum(1 for keyword in specificity_keywords if keyword in answer)
    metrics["specificity_score"] = min(specificity_score, 3)
    
    # 总分
    metrics["total_score"] = sum([
        metrics["length_score"],
        metrics["content_score"], 
        metrics["structure_score"],
        metrics["specificity_score"]
    ])
    
    return metrics

async def run_complete_tests():
    """运行完整的真实图像测试套件"""
    logger.info("🚀 开始TrafficMultimodalAgent真实图像完整功能测试")
    logger.info("🖼️ 使用用户提供的真实交通场景图片")
    logger.info("=" * 70)
    
    test_results = {}
    agent = None
    image_data = None
    
    try:
        # 1. 测试Agent初始化
        logger.info("🧪 执行测试: Agent初始化")
        agent = await test_agent_initialization()
        test_results["agent_initialization"] = True
        
        # 2. 测试图像加载
        logger.info("🧪 执行测试: 真实图像加载")
        image_data = await test_image_loading()
        test_results["image_loading"] = True
        
        # 3. 测试各工具功能
        logger.info("🧪 执行测试: 各工具对真实图像的分析")
        tools_success, tools_results = await test_individual_tools_with_real_image(agent, image_data)
        test_results["tools_functionality"] = tools_success
        
        # 4. 测试完整查询处理
        logger.info("🧪 执行测试: Agent完整查询处理")
        query_success, query_results = await test_complete_query_with_real_image(agent, image_data)
        test_results["complete_query"] = query_success
        
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {e}")
        test_results["error"] = str(e)
    
    # 汇总测试结果
    logger.info("=" * 70)
    logger.info("📋 真实图像完整功能测试结果汇总:")
    
    passed_tests = sum(1 for result in test_results.values() if result is True)
    total_tests = len([k for k in test_results.keys() if k != "error"])
    
    for test_name, success in test_results.items():
        if test_name != "error":
            status = "✅ 通过" if success else "❌ 失败"
            logger.info(f"   - {test_name}: {status}")
    
    if "error" in test_results:
        logger.info(f"   ⚠️ 测试错误: {test_results['error']}")
    
    logger.info("=" * 70)
    logger.info(f"🎯 测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        logger.info("🎉 所有真实图像测试通过！Agent能够成功分析真实交通场景")
        return True
    elif passed_tests >= total_tests * 0.75:  # 75%通过率
        logger.info("👍 大部分测试通过，Agent基本功能正常")
        return True
    else:
        logger.warning(f"⚠️ 多项测试失败，需要进一步优化Agent功能")
        return False

async def main():
    """主函数"""
    try:
        success = await run_complete_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        logger.info("🛑 测试被用户中断")
        return 1
    except Exception as e:
        logger.error(f"❌ 测试过程出现意外错误: {e}")
        return 1

if __name__ == "__main__":
    # Windows兼容性设置
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行测试
    exit_code = asyncio.run(main())
    sys.exit(exit_code)