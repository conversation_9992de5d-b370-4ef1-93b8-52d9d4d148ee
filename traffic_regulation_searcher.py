# -*- coding: utf-8 -*-
"""
交通法规检索工具 - Traffic Regulation Searcher

这个模块实现了从RAG知识库中检索交通法规的功能，是整个系统的法律依据核心。

核心思路：
1. 接收查询文本（违规描述、法规关键词等）
2. 使用向量相似度检索相关法规条文
3. 结合重排序算法优化检索结果
4. 返回结构化的法规信息和处罚标准

主要功能：
1. 语义检索 - 基于文本语义相似度的法规匹配
2. 关键词检索 - 基于精确关键词的法规查找
3. 混合检索 - 结合语义和关键词的综合检索
4. 结果重排序 - 基于相关性和权威性的结果优化

设计特点：
- 支持多种检索策略，适应不同查询需求
- 提供详细的法规条文和处罚标准
- 考虑法规的层级关系和适用范围
- 包含执法案例和实践指导

学习要点：
- RAG系统的检索器设计
- 向量数据库的查询优化
- 多策略检索的实现方法
- 法律文本的结构化处理

注意：由于Phase 3才会实现真正的RAG系统，这里先实现模拟版本，
提供基础的法规检索功能，为后续集成做准备。
"""

import re
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from src.utils.logger import get_logger
from config.settings import REGULATION_SEARCH_CONFIG

# 获取专用日志记录器
logger = get_logger(__name__)

class SearchStrategy(Enum):
    """
    检索策略枚举
    
    定义了不同的法规检索策略，用于：
    - 适应不同类型的查询需求
    - 优化检索精度和召回率
    - 支持用户自定义检索偏好
    """
    SEMANTIC = "semantic"      # 语义相似度检索
    KEYWORD = "keyword"        # 关键词精确匹配
    HYBRID = "hybrid"          # 混合检索策略
    FUZZY = "fuzzy"           # 模糊匹配检索

@dataclass
class RegulationItem:
    """
    法规条文数据类
    
    定义了法规信息的标准结构，包含：
    - 基本信息（法规名称、条文编号等）
    - 内容信息（条文内容、适用范围等）
    - 处罚信息（罚款标准、记分等）
    - 元数据（生效时间、修订版本等）
    
    设计目的：
    - 统一法规信息的数据格式
    - 便于序列化和反序列化
    - 支持结构化的法规管理
    - 提供清晰的数据接口
    """
    regulation_id: str          # 法规唯一标识
    law_name: str              # 法律法规名称
    article_number: str        # 条文编号
    content: str              # 条文内容
    category: str             # 违规类别
    penalty_type: str         # 处罚类型
    fine_amount: str          # 罚款金额
    points_deduction: int     # 记分数值
    applicable_scope: str     # 适用范围
    effective_date: str       # 生效日期
    authority_level: str      # 权威等级
    related_cases: List[str]  # 相关案例

class TrafficRegulationSearcher:
    """
    交通法规检索器
    
    这个类实现了完整的交通法规检索功能。
    
    核心方法：
    - search(): 主要检索接口
    - _semantic_search(): 语义相似度检索
    - _keyword_search(): 关键词精确检索
    - _hybrid_search(): 混合检索策略
    - _rerank_results(): 结果重排序优化
    
    设计原则：
    - 检索准确性：确保返回相关度高的法规条文
    - 响应速度：优化检索性能，支持实时查询
    - 结果完整性：提供全面的法规信息和处罚标准
    - 可扩展性：支持新增法规和检索策略
    """
    
    def __init__(self):
        """
        初始化交通法规检索器
        
        初始化步骤：
        1. 设置日志记录器
        2. 加载法规数据库（模拟版本）
        3. 初始化检索配置
        4. 建立关键词索引
        """
        # 设置专用日志记录器
        self.logger = get_logger(self.__class__.__name__)
        
        # 检索统计信息
        self.search_count = 0
        self.hit_count = 0
        
        # 检索配置参数
        self.max_results = REGULATION_SEARCH_CONFIG.get("max_results", 10)
        self.similarity_threshold = REGULATION_SEARCH_CONFIG.get("similarity_threshold", 0.7)
        self.rerank_enabled = REGULATION_SEARCH_CONFIG.get("rerank_enabled", True)
        
        # 加载法规数据（模拟版本，Phase 3中将连接真实的向量数据库）
        self.regulation_database = self._load_regulation_database()
        
        # 建立关键词索引，用于快速关键词检索
        self.keyword_index = self._build_keyword_index()
        
        # 初始化完成日志
        self.logger.info(f"📚 交通法规检索器初始化完成，加载了 {len(self.regulation_database)} 条法规")
    
    def _load_regulation_database(self) -> List[RegulationItem]:
        """
        加载法规数据库
        
        在真实的RAG系统中，这里会连接到向量数据库（如ChromaDB）
        来获取法规数据。目前提供模拟数据来验证系统架构。
        
        Returns:
            List[RegulationItem]: 法规条文列表
            
        学习要点：
        - 数据库连接和数据加载
        - 数据类实例化和管理
        - 法规数据的结构化表示
        - 错误处理和容错机制
        """
        regulations = []
        
        try:
            # 模拟的法规数据
            # 在Phase 3中，这些数据将从ChromaDB向量数据库中加载
            mock_regulations = [
                {
                    "regulation_id": "road_safety_law_57",
                    "law_name": "中华人民共和国道路交通安全法",
                    "article_number": "第五十七条",
                    "content": "非机动车应当在非机动车道内行驶；在没有非机动车道的道路上，应当靠车行道的右侧行驶。",
                    "category": "非机动车违规",
                    "penalty_type": "罚款",
                    "fine_amount": "5-50元",
                    "points_deduction": 0,
                    "applicable_scope": "全国",
                    "effective_date": "2004-05-01",
                    "authority_level": "法律",
                    "related_cases": ["电动车占用机动车道案例", "自行车违规行驶案例"]
                },
                {
                    "regulation_id": "road_safety_law_47",
                    "law_name": "中华人民共和国道路交通安全法",
                    "article_number": "第四十七条",
                    "content": "机动车行经人行横道时，应当减速行驶；遇行人正在通过人行横道，应当停车让行。机动车行经没有交通信号的道路时，遇行人横过道路，应当避让。",
                    "category": "机动车违规",
                    "penalty_type": "罚款记分",
                    "fine_amount": "200元",
                    "points_deduction": 3,
                    "applicable_scope": "全国",
                    "effective_date": "2004-05-01",
                    "authority_level": "法律",
                    "related_cases": ["机动车未礼让行人案例", "人行横道违规案例"]
                },
                {
                    "regulation_id": "road_safety_law_38",
                    "law_name": "中华人民共和国道路交通安全法",
                    "article_number": "第三十八条",
                    "content": "车辆、行人应当按照交通信号通行；遇有交通警察现场指挥时，应当按照交通警察的指挥通行；在没有交通信号的道路上，应当在确保安全、畅通的原则下通行。",
                    "category": "信号违规",
                    "penalty_type": "罚款记分",
                    "fine_amount": "200元",
                    "points_deduction": 6,
                    "applicable_scope": "全国",
                    "effective_date": "2004-05-01",
                    "authority_level": "法律",
                    "related_cases": ["闯红灯案例", "违反交通信号案例"]
                },
                {
                    "regulation_id": "road_safety_law_56",
                    "law_name": "中华人民共和国道路交通安全法",
                    "article_number": "第五十六条",
                    "content": "机动车应当在规定地点停放。禁止在人行道、网状线区域内停放机动车；禁止在设有禁止停车标志、标线的路段停放机动车。",
                    "category": "停车违规",
                    "penalty_type": "罚款",
                    "fine_amount": "100-200元",
                    "points_deduction": 0,
                    "applicable_scope": "全国",
                    "effective_date": "2004-05-01",
                    "authority_level": "法律",
                    "related_cases": ["违法停车案例", "占用人行道停车案例"]
                },
                {
                    "regulation_id": "road_safety_impl_reg_42",
                    "law_name": "道路交通安全法实施条例",
                    "article_number": "第四十二条",
                    "content": "机动车上道路行驶，不得超过限速标志标明的最高时速。在没有限速标志的路段，应当保持安全车速。",
                    "category": "超速违规",
                    "penalty_type": "罚款记分",
                    "fine_amount": "200-2000元",
                    "points_deduction": 12,
                    "applicable_scope": "全国",
                    "effective_date": "2004-05-01",
                    "authority_level": "行政法规",
                    "related_cases": ["超速行驶案例", "高速公路超速案例"]
                }
            ]
            
            # 将模拟数据转换为RegulationItem对象
            for reg_data in mock_regulations:
                regulation = RegulationItem(**reg_data)
                regulations.append(regulation)
                self.logger.debug(f"📋 加载法规: {regulation.law_name} {regulation.article_number}")
            
            return regulations
            
        except Exception as e:
            # 数据加载失败的处理
            self.logger.error(f"❌ 法规数据库加载失败: {e}")
            
            # 返回空列表，确保系统可以继续运行
            return []
    
    def _build_keyword_index(self) -> Dict[str, List[RegulationItem]]:
        """
        构建关键词索引
        
        为了提高关键词检索的速度，预先建立关键词到法规的映射索引。
        这种倒排索引的设计大大提高了检索效率。
        
        Returns:
            Dict[str, List[RegulationItem]]: 关键词索引
            
        学习要点：
        - 倒排索引的构建原理
        - 中文文本的分词处理
        - 索引优化和内存管理
        - 多值字典的使用技巧
        """
        keyword_index = {}
        
        try:
            # 定义关键词提取规则
            # 在实际应用中，可以使用更复杂的NLP技术，如jieba分词
            for regulation in self.regulation_database:
                # 从法规内容中提取关键词
                keywords = self._extract_keywords_from_regulation(regulation)
                
                # 建立关键词到法规的映射
                for keyword in keywords:
                    if keyword not in keyword_index:
                        keyword_index[keyword] = []
                    keyword_index[keyword].append(regulation)
            
            # 记录索引统计信息
            total_keywords = len(keyword_index)
            self.logger.info(f"🔍 关键词索引构建完成，共 {total_keywords} 个关键词")
            
            return keyword_index
            
        except Exception as e:
            self.logger.error(f"❌ 关键词索引构建失败: {e}")
            return {}
    
    def _extract_keywords_from_regulation(self, regulation: RegulationItem) -> List[str]:
        """
        从法规条文中提取关键词
        
        这个方法使用规则和模式匹配来提取法规中的关键术语。
        在实际应用中，可以结合NLP技术提高提取精度。
        
        Args:
            regulation: 法规条文对象
            
        Returns:
            List[str]: 提取的关键词列表
            
        学习要点：
        - 文本关键词提取的基本方法
        - 正则表达式的高级应用
        - 领域术语的识别和处理
        - 关键词权重和重要性评估
        """
        keywords = []
        
        try:
            # 基于规则的关键词提取
            # 这里使用简单的规则，实际应用中可以更复杂
            
            # 1. 提取法规类别关键词
            category_keywords = {
                "非机动车违规": ["非机动车", "电动车", "自行车", "机动车道"],
                "机动车违规": ["机动车", "汽车", "礼让行人", "人行横道"],
                "信号违规": ["交通信号", "红绿灯", "信号灯", "闯红灯"],
                "停车违规": ["停车", "停放", "人行道", "禁停"],
                "超速违规": ["超速", "限速", "车速", "最高时速"]
            }
            
            if regulation.category in category_keywords:
                keywords.extend(category_keywords[regulation.category])
            
            # 2. 从条文内容中提取关键词
            content_keywords = self._extract_content_keywords(regulation.content)
            keywords.extend(content_keywords)
            
            # 3. 添加法规标识关键词
            keywords.extend([
                regulation.law_name,
                regulation.article_number,
                regulation.category
            ])
            
            # 去重并过滤
            keywords = list(set([kw for kw in keywords if len(kw) >= 2]))
            
            return keywords
            
        except Exception as e:
            self.logger.error(f"❌ 关键词提取失败: {e}")
            return []
    
    def _extract_content_keywords(self, content: str) -> List[str]:
        """
        从条文内容中提取关键词
        
        使用正则表达式和规则匹配提取条文中的重要术语。
        
        Args:
            content: 条文内容
            
        Returns:
            List[str]: 提取的关键词
        """
        keywords = []
        
        # 定义重要术语的正则模式
        patterns = [
            r"(机动车|非机动车|行人|车辆)",
            r"(道路|车道|人行横道|人行道)",
            r"(交通信号|信号灯|标志|标线)",
            r"(停车|停放|行驶|通行)",
            r"(违法|违规|禁止|应当)",
            r"(罚款|记分|处罚|吊销)"
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content)
            keywords.extend(matches)
        
        return keywords
    
    async def search(self, 
                    query: str,
                    strategy: SearchStrategy = SearchStrategy.HYBRID,
                    max_results: Optional[int] = None,
                    category_filter: Optional[str] = None) -> Dict[str, Any]:
        """
        执行交通法规检索的主要接口
        
        这是法规检索的核心方法，负责：
        1. 查询预处理和分析
        2. 选择合适的检索策略
        3. 执行检索操作
        4. 结果后处理和优化
        5. 返回结构化的检索结果
        
        Args:
            query: 检索查询文本，可以是违规描述、法规关键词等
            strategy: 检索策略，决定使用哪种检索方法
            max_results: 最大返回结果数量，默认使用配置值
            category_filter: 违规类别过滤器，只返回特定类别的法规
            
        Returns:
            Dict[str, Any]: 检索结果，包含：
            {
                "success": bool,                # 检索是否成功
                "results": List[Dict],          # 检索到的法规列表
                "total_found": int,             # 总计找到的法规数量
                "search_metadata": Dict,        # 检索元数据
                "suggestions": List[str]        # 相关检索建议
            }
            
        学习要点：
        - 多策略检索的协调管理
        - 异步检索操作的实现
        - 结果质量评估和优化
        - 用户体验的考虑和改进
        """
        # 记录检索开始时间
        import time
        start_time = time.time()
        
        try:
            # 更新检索统计
            self.search_count += 1
            
            # 设置检索参数
            max_results = max_results or self.max_results
            
            self.logger.info(f"🔍 开始法规检索，策略：{strategy.value}，查询：{query[:50]}...")
            
            # 步骤1：查询预处理
            processed_query = self._preprocess_query(query)
            if not processed_query:
                return {
                    "success": False,
                    "error": "查询文本为空或无效",
                    "results": [],
                    "total_found": 0
                }
            
            # 步骤2：根据策略执行检索
            if strategy == SearchStrategy.SEMANTIC:
                raw_results = await self._semantic_search(processed_query, max_results * 2)
            elif strategy == SearchStrategy.KEYWORD:
                raw_results = await self._keyword_search(processed_query, max_results * 2)
            elif strategy == SearchStrategy.FUZZY:
                raw_results = await self._fuzzy_search(processed_query, max_results * 2)
            else:  # HYBRID策略
                raw_results = await self._hybrid_search(processed_query, max_results * 2)
            
            # 步骤3：应用类别过滤器
            if category_filter:
                raw_results = [r for r in raw_results if r["regulation"].category == category_filter]
            
            # 步骤4：结果重排序和优化
            if self.rerank_enabled and len(raw_results) > 1:
                reranked_results = await self._rerank_results(processed_query, raw_results)
            else:
                reranked_results = raw_results
            
            # 步骤5：限制返回结果数量
            final_results = reranked_results[:max_results]
            
            # 步骤6：格式化检索结果
            formatted_results = [self._format_regulation_result(r) for r in final_results]
            
            # 步骤7：生成相关建议
            suggestions = self._generate_search_suggestions(query, final_results)
            
            # 步骤8：构建完整的检索结果
            processing_time = time.time() - start_time
            result = {
                "success": True,
                "results": formatted_results,
                "total_found": len(raw_results),
                "returned_count": len(final_results),
                "search_metadata": {
                    "processing_time": round(processing_time, 3),
                    "strategy_used": strategy.value,
                    "query_processed": processed_query,
                    "category_filter": category_filter,
                    "rerank_applied": self.rerank_enabled,
                    "total_searches": self.search_count
                },
                "suggestions": suggestions
            }
            
            # 更新命中统计
            if len(final_results) > 0:
                self.hit_count += 1
            
            # 记录检索完成日志
            self.logger.info(f"✅ 法规检索完成，找到 {len(final_results)} 条结果，耗时 {processing_time:.3f}秒")
            
            return result
            
        except Exception as e:
            # 异常处理：确保检索失败不会影响整个系统
            error_msg = f"法规检索过程中发生错误: {str(e)}"
            self.logger.error(f"❌ {error_msg}")
            
            return {
                "success": False,
                "error": error_msg,
                "results": [],
                "total_found": 0,
                "processing_time": time.time() - start_time
            }
    
    def _preprocess_query(self, query: str) -> str:
        """
        预处理查询文本
        
        查询预处理是检索质量的重要保证，包括：
        1. 文本清理和标准化
        2. 关键词提取和扩展
        3. 同义词识别和替换
        4. 无关词汇过滤
        
        Args:
            query: 原始查询文本
            
        Returns:
            str: 预处理后的查询文本
            
        学习要点：
        - 文本预处理的重要性
        - 中文文本处理的特殊考虑
        - 查询扩展和优化技术
        - 噪声数据的识别和过滤
        """
        try:
            if not query or not query.strip():
                return ""
            
            # 1. 基本清理
            processed = query.strip()
            
            # 2. 移除特殊字符和多余空格
            processed = re.sub(r'[^\w\s\u4e00-\u9fff]', ' ', processed)
            processed = re.sub(r'\s+', ' ', processed)
            
            # 3. 关键词标准化
            # 将常见的同义词统一为标准术语
            synonyms = {
                "电瓶车": "电动车",
                "单车": "自行车", 
                "汽车": "机动车",
                "人行道": "人行横道",
                "红灯": "交通信号"
            }
            
            for old_term, new_term in synonyms.items():
                processed = processed.replace(old_term, new_term)
            
            # 4. 查询扩展
            # 为提高召回率，添加相关术语
            expansion_rules = {
                "违规": ["违法", "不规范"],
                "停车": ["停放", "泊车"],
                "超速": ["速度超限", "限速"]
            }
            
            expanded_terms = []
            for keyword, expansions in expansion_rules.items():
                if keyword in processed:
                    expanded_terms.extend(expansions)
            
            if expanded_terms:
                processed += " " + " ".join(expanded_terms)
            
            return processed.strip()
            
        except Exception as e:
            self.logger.error(f"❌ 查询预处理失败: {e}")
            return query.strip()
    
    async def _semantic_search(self, query: str, max_results: int) -> List[Dict[str, Any]]:
        """
        语义相似度检索
        
        这个方法实现基于语义相似度的法规检索。在真实的RAG系统中，
        会使用向量数据库计算查询和法规条文的语义相似度。
        
        当前版本使用简化的文本匹配模拟语义检索，在Phase 3中
        将集成真实的向量检索功能。
        
        Args:
            query: 预处理后的查询文本
            max_results: 最大返回结果数
            
        Returns:
            List[Dict[str, Any]]: 检索结果列表
            
        学习要点：
        - 语义相似度计算的原理
        - 向量检索的实现方法
        - 文本相似度评估技术
        - 检索结果的排序策略
        """
        try:
            results = []
            
            # 模拟语义相似度计算
            # 在Phase 3中，这里会使用ChromaDB的向量相似度检索
            for regulation in self.regulation_database:
                # 计算查询与法规内容的相似度
                similarity = self._calculate_text_similarity(query, regulation.content)
                
                # 同时考虑与法规类别的相似度
                category_similarity = self._calculate_text_similarity(query, regulation.category)
                
                # 综合相似度计算
                combined_similarity = similarity * 0.7 + category_similarity * 0.3
                
                # 如果相似度超过阈值，加入结果
                if combined_similarity >= self.similarity_threshold:
                    results.append({
                        "regulation": regulation,
                        "similarity_score": combined_similarity,
                        "match_type": "semantic",
                        "match_details": {
                            "content_similarity": similarity,
                            "category_similarity": category_similarity,
                            "combined_score": combined_similarity
                        }
                    })
            
            # 按相似度降序排序
            results.sort(key=lambda x: x["similarity_score"], reverse=True)
            
            # 限制返回数量
            return results[:max_results]
            
        except Exception as e:
            self.logger.error(f"❌ 语义检索失败: {e}")
            return []
    
    async def _keyword_search(self, query: str, max_results: int) -> List[Dict[str, Any]]:
        """
        关键词精确检索
        
        基于预建的关键词索引进行精确匹配检索。
        这种方法速度快，精确度高，适合明确的法规查询。
        
        Args:
            query: 预处理后的查询文本
            max_results: 最大返回结果数
            
        Returns:
            List[Dict[str, Any]]: 检索结果列表
        """
        try:
            results = []
            query_keywords = query.split()
            
            # 为每个查询关键词找到匹配的法规
            keyword_matches = {}
            
            for keyword in query_keywords:
                # 在关键词索引中查找匹配
                for index_keyword, regulations in self.keyword_index.items():
                    if keyword in index_keyword or index_keyword in keyword:
                        for regulation in regulations:
                            reg_id = regulation.regulation_id
                            if reg_id not in keyword_matches:
                                keyword_matches[reg_id] = {
                                    "regulation": regulation,
                                    "matched_keywords": [],
                                    "match_count": 0
                                }
                            keyword_matches[reg_id]["matched_keywords"].append(index_keyword)
                            keyword_matches[reg_id]["match_count"] += 1
            
            # 将匹配结果转换为标准格式
            for reg_id, match_info in keyword_matches.items():
                # 计算关键词匹配分数
                match_score = min(match_info["match_count"] / len(query_keywords), 1.0)
                
                results.append({
                    "regulation": match_info["regulation"],
                    "similarity_score": match_score,
                    "match_type": "keyword",
                    "match_details": {
                        "matched_keywords": match_info["matched_keywords"],
                        "match_count": match_info["match_count"],
                        "total_query_keywords": len(query_keywords)
                    }
                })
            
            # 按匹配分数降序排序
            results.sort(key=lambda x: x["similarity_score"], reverse=True)
            
            return results[:max_results]
            
        except Exception as e:
            self.logger.error(f"❌ 关键词检索失败: {e}")
            return []
    
    async def _fuzzy_search(self, query: str, max_results: int) -> List[Dict[str, Any]]:
        """
        模糊匹配检索
        
        使用模糊匹配算法进行更宽松的检索，
        适合处理用户输入的不准确查询。
        
        Args:
            query: 预处理后的查询文本
            max_results: 最大返回结果数
            
        Returns:
            List[Dict[str, Any]]: 检索结果列表
        """
        try:
            results = []
            
            for regulation in self.regulation_database:
                # 使用编辑距离进行模糊匹配
                fuzzy_score = self._calculate_fuzzy_similarity(query, regulation.content)
                
                # 设置较低的阈值，允许更多模糊匹配
                if fuzzy_score >= 0.3:
                    results.append({
                        "regulation": regulation,
                        "similarity_score": fuzzy_score,
                        "match_type": "fuzzy",
                        "match_details": {
                            "fuzzy_score": fuzzy_score,
                            "match_method": "edit_distance"
                        }
                    })
            
            # 按模糊匹配分数排序
            results.sort(key=lambda x: x["similarity_score"], reverse=True)
            
            return results[:max_results]
            
        except Exception as e:
            self.logger.error(f"❌ 模糊检索失败: {e}")
            return []
    
    async def _hybrid_search(self, query: str, max_results: int) -> List[Dict[str, Any]]:
        """
        混合检索策略
        
        结合语义检索和关键词检索的优势，
        提供最佳的检索效果和用户体验。
        
        Args:
            query: 预处理后的查询文本
            max_results: 最大返回结果数
            
        Returns:
            List[Dict[str, Any]]: 检索结果列表
        """
        try:
            # 并行执行多种检索策略
            semantic_results = await self._semantic_search(query, max_results)
            keyword_results = await self._keyword_search(query, max_results)
            
            # 合并结果并去重
            combined_results = {}
            
            # 添加语义检索结果
            for result in semantic_results:
                reg_id = result["regulation"].regulation_id
                combined_results[reg_id] = result
                # 标记为混合检索结果
                result["match_type"] = "hybrid_semantic"
            
            # 添加关键词检索结果，如果已存在则合并分数
            for result in keyword_results:
                reg_id = result["regulation"].regulation_id
                if reg_id in combined_results:
                    # 合并分数：语义相似度 + 关键词匹配度
                    existing = combined_results[reg_id]
                    combined_score = (existing["similarity_score"] * 0.6 + 
                                    result["similarity_score"] * 0.4)
                    existing["similarity_score"] = combined_score
                    existing["match_type"] = "hybrid_combined"
                    
                    # 合并匹配详情
                    existing["match_details"]["keyword_match"] = result["match_details"]
                else:
                    combined_results[reg_id] = result
                    result["match_type"] = "hybrid_keyword"
            
            # 转换为列表并排序
            final_results = list(combined_results.values())
            final_results.sort(key=lambda x: x["similarity_score"], reverse=True)
            
            return final_results[:max_results]
            
        except Exception as e:
            self.logger.error(f"❌ 混合检索失败: {e}")
            return []
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """
        计算两个文本的相似度
        
        使用简单的词汇重叠算法计算相似度。
        在实际应用中，可以使用更复杂的语义相似度算法。
        
        Args:
            text1: 第一个文本
            text2: 第二个文本
            
        Returns:
            float: 相似度分数 (0.0-1.0)
        """
        try:
            # 简单的词汇重叠相似度
            words1 = set(text1.split())
            words2 = set(text2.split())
            
            if not words1 or not words2:
                return 0.0
            
            intersection = words1.intersection(words2)
            union = words1.union(words2)
            
            # Jaccard相似度
            similarity = len(intersection) / len(union) if union else 0.0
            
            return similarity
            
        except Exception as e:
            self.logger.error(f"❌ 相似度计算失败: {e}")
            return 0.0
    
    def _calculate_fuzzy_similarity(self, text1: str, text2: str) -> float:
        """
        计算模糊相似度
        
        使用简化的编辑距离算法计算模糊相似度。
        
        Args:
            text1: 第一个文本
            text2: 第二个文本
            
        Returns:
            float: 模糊相似度分数 (0.0-1.0)
        """
        try:
            # 简化的编辑距离计算
            len1, len2 = len(text1), len(text2)
            
            if len1 == 0:
                return 0.0 if len2 > 0 else 1.0
            if len2 == 0:
                return 0.0
            
            # 计算最长公共子序列长度
            max_len = max(len1, len2)
            common_chars = sum(1 for c1, c2 in zip(text1, text2) if c1 == c2)
            
            similarity = common_chars / max_len
            return similarity
            
        except Exception as e:
            self.logger.error(f"❌ 模糊相似度计算失败: {e}")
            return 0.0
    
    async def _rerank_results(self, query: str, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        结果重排序优化
        
        基于多个因素对检索结果进行重排序，提高结果质量：
        1. 相关性分数
        2. 法规权威等级
        3. 法规适用范围
        4. 执法频率
        
        Args:
            query: 原始查询
            results: 待重排序的结果列表
            
        Returns:
            List[Dict[str, Any]]: 重排序后的结果列表
            
        学习要点：
        - 多因素排序算法的设计
        - 业务规则在排序中的应用
        - 用户体验优化考虑
        - 排序策略的评估和调优
        """
        try:
            if not results:
                return results
            
            # 为每个结果计算重排序分数
            for result in results:
                regulation = result["regulation"]
                
                # 基础相关性分数
                base_score = result["similarity_score"]
                
                # 权威等级加权
                authority_weight = {
                    "法律": 1.0,
                    "行政法规": 0.9,
                    "部门规章": 0.8,
                    "地方法规": 0.7
                }.get(regulation.authority_level, 0.6)
                
                # 适用范围加权
                scope_weight = {
                    "全国": 1.0,
                    "省级": 0.8,
                    "市级": 0.6,
                    "县级": 0.4
                }.get(regulation.applicable_scope, 0.5)
                
                # 违规严重性加权（罚款越重，排序越靠前）
                penalty_weight = 1.0
                if "元" in regulation.fine_amount:
                    # 提取罚款金额进行加权
                    amounts = re.findall(r'\d+', regulation.fine_amount)
                    if amounts:
                        max_amount = max(int(amt) for amt in amounts)
                        penalty_weight = min(1.0 + max_amount / 1000, 2.0)
                
                # 计算综合重排序分数
                rerank_score = (
                    base_score * 0.6 +           # 基础相关性
                    authority_weight * 0.2 +     # 权威等级
                    scope_weight * 0.1 +         # 适用范围
                    penalty_weight * 0.1         # 处罚严重性
                )
                
                # 更新结果分数
                result["rerank_score"] = rerank_score
                result["rerank_factors"] = {
                    "authority_weight": authority_weight,
                    "scope_weight": scope_weight,
                    "penalty_weight": penalty_weight,
                    "original_score": base_score
                }
            
            # 按重排序分数排序
            results.sort(key=lambda x: x.get("rerank_score", x["similarity_score"]), reverse=True)
            
            self.logger.debug(f"🔄 完成结果重排序，共 {len(results)} 条结果")
            return results
            
        except Exception as e:
            self.logger.error(f"❌ 结果重排序失败: {e}")
            return results
    
    def _format_regulation_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化法规检索结果
        
        将内部的检索结果转换为用户友好的输出格式。
        
        Args:
            result: 内部检索结果
            
        Returns:
            Dict[str, Any]: 格式化的结果
        """
        try:
            regulation = result["regulation"]
            
            return {
                "regulation_id": regulation.regulation_id,
                "law_name": regulation.law_name,
                "article_number": regulation.article_number,
                "content": regulation.content,
                "category": regulation.category,
                "penalty_info": {
                    "type": regulation.penalty_type,
                    "fine_amount": regulation.fine_amount,
                    "points_deduction": regulation.points_deduction
                },
                "authority_info": {
                    "level": regulation.authority_level,
                    "scope": regulation.applicable_scope,
                    "effective_date": regulation.effective_date
                },
                "related_cases": regulation.related_cases,
                "match_info": {
                    "similarity_score": result["similarity_score"],
                    "match_type": result["match_type"],
                    "details": result.get("match_details", {})
                }
            }
            
        except Exception as e:
            self.logger.error(f"❌ 结果格式化失败: {e}")
            return {"error": "结果格式化失败"}
    
    def _generate_search_suggestions(self, original_query: str, results: List[Dict]) -> List[str]:
        """
        生成相关检索建议
        
        基于检索结果和查询历史，生成相关的检索建议，
        帮助用户发现更多相关法规。
        
        Args:
            original_query: 原始查询
            results: 检索结果列表
            
        Returns:
            List[str]: 检索建议列表
        """
        suggestions = []
        
        try:
            if not results:
                # 没有结果时的建议
                suggestions = [
                    "尝试使用更通用的关键词",
                    "检查查询是否有拼写错误",
                    "尝试使用法规条文的部分内容进行检索"
                ]
            else:
                # 基于检索结果生成建议
                categories = set()
                law_names = set()
                
                for result in results[:3]:  # 只考虑前3个结果
                    regulation = result["regulation"]
                    categories.add(regulation.category)
                    law_names.add(regulation.law_name)
                
                # 生成类别相关建议
                for category in list(categories)[:2]:
                    suggestions.append(f"查看更多关于「{category}」的法规")
                
                # 生成法律相关建议
                for law_name in list(law_names)[:2]:
                    if law_name != "中华人民共和国道路交通安全法":
                        suggestions.append(f"浏览《{law_name}》的其他条文")
            
            return suggestions[:4]  # 最多返回4个建议
            
        except Exception as e:
            self.logger.error(f"❌ 建议生成失败: {e}")
            return ["尝试调整查询关键词"]
    
    def get_search_statistics(self) -> Dict[str, Any]:
        """
        获取检索统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "total_searches": self.search_count,
            "successful_searches": self.hit_count,
            "hit_rate": round(self.hit_count / max(self.search_count, 1), 2),
            "total_regulations": len(self.regulation_database),
            "indexed_keywords": len(self.keyword_index),
            "similarity_threshold": self.similarity_threshold
        }