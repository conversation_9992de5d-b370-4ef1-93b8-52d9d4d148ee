#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境验证脚本 - Environment Verification Script

这个脚本用于验证系统环境配置是否正确，包括：
1. Ollama服务状态和模型可用性检查
2. 项目目录结构完整性验证
3. 关键Python依赖包导入测试
4. 系统配置验证

使用方法：
    python src/utils/verify_env.py

验证内容：
- Ollama服务连通性 (localhost:11434)
- Qwen2.5-VL-7B模型安装状态
- 项目核心目录结构
- LangChain、ChromaDB、Streamlit等关键依赖
- 配置文件完整性
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.parent.parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

from src.utils.logger import setup_logger, get_logger

# 设置日志
setup_logger()
logger = get_logger(__name__)

async def verify_environment():
    """
    验证环境配置
    
    Returns:
        bool: 环境验证是否通过
    """
    print("🔍 智能交通多模态RAG助手 - 环境验证")
    print("=" * 60)
    
    all_checks_passed = True
    
    # 1. 验证Ollama服务
    print("\n📡 检查Ollama服务...")
    ollama_ok = await _check_ollama_service()
    if not ollama_ok:
        all_checks_passed = False
    
    # 2. 验证模型可用性
    print("\n🧠 检查Qwen2.5-VL模型...")
    model_ok = await _check_model_availability()
    if not model_ok:
        all_checks_passed = False
    
    # 3. 验证目录结构
    print("\n📁 检查项目目录结构...")
    dirs_ok = _check_directory_structure()
    if not dirs_ok:
        all_checks_passed = False
    
    # 4. 验证Python依赖
    print("\n📦 检查Python依赖包...")
    deps_ok = _check_python_dependencies()
    if not deps_ok:
        all_checks_passed = False
    
    # 5. 验证配置文件
    print("\n⚙️ 检查配置文件...")
    config_ok = _check_configuration()
    if not config_ok:
        all_checks_passed = False
    
    # 6. 系统资源检查
    print("\n💻 检查系统资源...")
    _check_system_resources()
    
    print("\n" + "=" * 60)
    if all_checks_passed:
        print("🎉 环境验证完成！所有检查都通过了。")
        print("💡 可以开始运行系统：python main.py")
        return True
    else:
        print("❌ 环境验证失败！请解决以上问题后重试。")
        print("💡 参考文档：README.md")
        return False

async def _check_ollama_service():
    """检查Ollama服务状态"""
    try:
        import aiohttp
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                "http://localhost:11434/api/version",
                timeout=aiohttp.ClientTimeout(total=5)
            ) as response:
                if response.status == 200:
                    version_info = await response.json()
                    print(f"   ✅ Ollama服务运行正常")
                    print(f"   📊 版本信息: {version_info.get('version', 'unknown')}")
                    return True
                else:
                    print(f"   ❌ Ollama服务响应异常：HTTP {response.status}")
                    print(f"   💡 请确认Ollama服务已启动：ollama serve")
                    return False
                    
    except ImportError:
        print("   ❌ aiohttp包未安装，无法测试Ollama连接")
        return False
    except Exception as e:
        print(f"   ❌ Ollama服务连接失败：{str(e)}")
        print(f"   💡 请确认Ollama已启动并在localhost:11434运行")
        print(f"   💡 启动命令：ollama serve")
        return False

async def _check_model_availability():
    """检查模型可用性"""
    try:
        import aiohttp
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                "http://localhost:11434/api/tags",
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status == 200:
                    tags_data = await response.json()
                    models = [model["name"] for model in tags_data.get("models", [])]
                    
                    # 检查Qwen2.5-VL模型
                    qwen_models = [m for m in models if 'qwen2.5vl' in m.lower()]
                    
                    if qwen_models:
                        print(f"   ✅ 找到Qwen2.5-VL模型：{qwen_models[0]}")
                        print(f"   📊 可用模型总数：{len(models)}")
                        return True
                    else:
                        print(f"   ❌ 未找到Qwen2.5-VL模型")
                        print(f"   📊 可用模型：{models[:3]}..." if len(models) > 3 else f"   📊 可用模型：{models}")
                        print(f"   💡 安装命令：ollama pull qwen2.5vl:7b")
                        return False
                else:
                    print(f"   ❌ 无法获取模型列表：HTTP {response.status}")
                    return False
                    
    except Exception as e:
        print(f"   ❌ 模型检查失败：{str(e)}")
        return False

def _check_directory_structure():
    """检查项目目录结构"""
    required_dirs = [
        "src/agent",
        "src/tools", 
        "src/rag",
        "src/web",
        "src/utils",
        "tests/unit",
        "tests/integration", 
        "tests/system",
        "config",
        "data/knowledge_base",
        "data/test_images",
        "logs"
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        full_path = PROJECT_ROOT / dir_path
        if full_path.exists():
            print(f"   ✅ {dir_path}")
        else:
            print(f"   ❌ 缺失目录：{dir_path}")
            missing_dirs.append(dir_path)
    
    if missing_dirs:
        print(f"   💡 创建缺失目录：mkdir -p {' '.join(missing_dirs)}")
        return False
    
    return True

def _check_python_dependencies():
    """检查Python依赖包"""
    core_packages = {
        'langchain': 'LangChain框架',
        'langchain_ollama': 'LangChain Ollama集成',
        'chromadb': '向量数据库',
        'streamlit': 'Web界面框架',
        'aiohttp': '异步HTTP客户端',
        'PIL': '图像处理库',
        'cv2': 'OpenCV图像处理',
        'numpy': '数值计算库'
    }
    
    optional_packages = {
        'fastapi': 'API框架',
        'pytest': '测试框架',
        'sentence_transformers': '文本嵌入模型'
    }
    
    missing_core = []
    missing_optional = []
    
    # 检查核心依赖
    for package, desc in core_packages.items():
        try:
            if package == 'cv2':
                __import__('cv2')
            elif package == 'langchain_ollama':
                # langchain_ollama可能有导入问题但已安装，只检查包是否存在
                import pkg_resources
                pkg_resources.get_distribution('langchain-ollama')
                print(f"   ✅ {desc} ({package})")
            else:
                __import__(package)
                print(f"   ✅ {desc} ({package})")
        except (ImportError, pkg_resources.DistributionNotFound):
            print(f"   ❌ {desc} ({package}) - 未安装")
            missing_core.append(package)
    
    # 检查可选依赖
    for package, desc in optional_packages.items():
        try:
            __import__(package)
            print(f"   ✅ {desc} ({package}) - 可选")
        except ImportError:
            missing_optional.append(package)
    
    if missing_core:
        print(f"   💡 安装核心依赖：pip install {' '.join(missing_core)}")
        return False
    
    if missing_optional:
        print(f"   💡 可选依赖未安装：{', '.join(missing_optional)}")
    
    return True

def _check_configuration():
    """检查配置文件"""
    config_files = [
        "config/settings.py",
        "main.py",
        "README.md",
        "requirements.txt"
    ]
    
    missing_configs = []
    for config_file in config_files:
        config_path = PROJECT_ROOT / config_file
        if config_path.exists():
            print(f"   ✅ {config_file}")
        else:
            print(f"   ❌ 缺失配置：{config_file}")
            missing_configs.append(config_file)
    
    # 检查配置内容
    try:
        sys.path.insert(0, str(PROJECT_ROOT))
        from config.settings import OLLAMA_CONFIG, CHROMA_CONFIG
        print(f"   ✅ 配置加载成功")
        print(f"   📊 Ollama配置: {OLLAMA_CONFIG['base_url']}")
        print(f"   📊 知识库目录: {CHROMA_CONFIG['persist_directory']}")
    except Exception as e:
        print(f"   ❌ 配置加载失败：{str(e)}")
        return False
    
    return len(missing_configs) == 0

def _check_system_resources():
    """检查系统资源"""
    try:
        import psutil
        
        # CPU信息
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"   📊 CPU使用率: {cpu_percent}%")
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        memory_used_percent = memory.percent
        print(f"   📊 内存: {memory_gb:.1f}GB (使用率: {memory_used_percent}%)")
        
        # 磁盘空间
        disk = psutil.disk_usage(str(PROJECT_ROOT))
        disk_free_gb = disk.free / (1024**3)
        print(f"   📊 磁盘剩余: {disk_free_gb:.1f}GB")
        
        # 系统建议
        if memory_gb < 4:
            print(f"   ⚠️ 建议至少4GB内存以获得更好性能")
        if disk_free_gb < 2:
            print(f"   ⚠️ 磁盘空间不足，建议至少2GB剩余空间")
            
    except ImportError:
        print("   💡 安装psutil查看详细系统信息：pip install psutil")
    except Exception as e:
        print(f"   ⚠️ 系统资源检查失败：{str(e)}")

if __name__ == "__main__":
    try:
        success = asyncio.run(verify_environment())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 验证过程异常：{str(e)}")
        sys.exit(1)