# -*- coding: utf-8 -*-
"""
单元测试模块 - Unit Tests Module

这个模块包含了系统各个组件的单元测试。
测试范围：

1. Agent核心功能测试
   - TrafficMultimodalAgent初始化和配置测试
   - 工具调用和推理链执行测试
   - 错误处理和异常情况测试

2. 工具模块测试
   - 图像分析工具测试（模拟Ollama响应）
   - 违规检测逻辑测试
   - 法规检索功能测试
   - 安全评估算法测试

3. RAG系统测试
   - 向量数据库操作测试
   - 嵌入模型功能测试  
   - 检索策略和排序测试
   - 查询处理逻辑测试

4. 工具模块测试
   - 图像处理功能测试
   - Ollama客户端通信测试
   - 日志记录功能测试
   - 配置管理测试

测试使用pytest框架，包含mock对象模拟外部依赖，
确保单元测试的独立性和可重复性。
"""