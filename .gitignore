# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Models and Data (large files)
data/models/
data/langchain_vectors/
data/vectors/
*.bin
*.safetensors
*.pt
*.pth

# API Keys and Config
.env
config/secrets.py
*.key

# Test outputs
test_results*.json
test_output.log

# Temporary files
tmp/
temp/
*.tmp

# ChromaDB data
chroma.sqlite3

# Ollama models (too large for git)
*.gguf