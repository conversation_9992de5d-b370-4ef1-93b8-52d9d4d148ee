#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入完整交通知识库数据
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from data.knowledge.traffic_regulations import ALL_KNOWLEDGE_DATA
from src.rag.vector_store import TrafficVectorStore
from src.rag.embeddings import BGEEmbeddings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def import_complete_knowledge():
    """导入完整知识库数据"""
    print("🚀 导入完整交通知识库数据")
    print("=" * 50)
    
    try:
        # 1. 初始化组件
        print("📦 初始化组件...")
        vector_store = TrafficVectorStore()
        embeddings = BGEEmbeddings()
        
        # 2. 检查当前状态
        print(f"\n📊 检查当前数据库状态...")
        initial_info = vector_store.get_collection_info()
        print("导入前状态:")
        for name, info in initial_info.items():
            if name != "summary":
                print(f"   - {name}: {info['document_count']} 个文档")
        
        # 3. 导入所有数据
        print(f"\n📚 开始导入知识数据...")
        total_imported = 0
        
        for collection_name, documents in ALL_KNOWLEDGE_DATA.items():
            current_count = initial_info.get(collection_name, {}).get('document_count', 0)
            
            if current_count >= len(documents):
                print(f"✅ 集合 {collection_name}: 已有 {current_count} 个文档，跳过导入")
                total_imported += current_count
                continue
                
            print(f"\n📚 导入集合: {collection_name}")
            print(f"   - 文档数量: {len(documents)}")
            print(f"   - 当前已有: {current_count}")
            
            # 如果集合不为空，先清空再导入（避免重复）
            if current_count > 0:
                print(f"🔄 重置集合以避免重复...")
                reset_result = vector_store.reset_collection(collection_name)
                if not reset_result["success"]:
                    print(f"❌ 重置失败: {reset_result.get('error', 'Unknown error')}")
                    continue
            
            # 分批导入
            batch_size = 5  # 使用较小批次确保稳定性
            success_count = 0
            
            for i in range(0, len(documents), batch_size):
                batch_docs = documents[i:i + batch_size]
                batch_num = i // batch_size + 1
                
                print(f"   📝 导入批次 {batch_num}: {len(batch_docs)} 个文档")
                
                result = vector_store.add_documents(
                    collection_name=collection_name,
                    documents=batch_docs,
                    batch_size=len(batch_docs)
                )
                
                if result["success"]:
                    success_count += result["success_count"]
                    print(f"      ✅ 成功: {result['success_count']}/{result['total_documents']}")
                else:
                    print(f"      ❌ 失败: {result.get('errors', [])}")
            
            print(f"📊 集合 {collection_name} 导入完成: {success_count}/{len(documents)}")
            total_imported += success_count
        
        # 4. 验证最终结果
        print(f"\n🔍 验证导入结果...")
        final_info = vector_store.get_collection_info()
        print("导入后状态:")
        for name, info in final_info.items():
            if name != "summary":
                print(f"   - {name}: {info['document_count']} 个文档")
            else:
                print(f"   - 总结: 总计 {info['total_documents']} 个文档")
        
        print(f"\n📈 本次导入: {total_imported} 个文档")
        print(f"🎉 完整知识库导入成功!")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = import_complete_knowledge()
    if not success:
        sys.exit(1)