#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于LangChain的RAG系统实现

使用LangChain框架构建的检索增强生成系统，
专门针对交通领域知识进行优化。

技术特点：
1. 使用BGE中文嵌入模型进行向量化
2. ChromaDB作为向量数据库
3. 与Agent系统无缝集成
4. 支持动态知识库更新
"""

import os
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

# LangChain imports
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_community.vectorstores import Chroma
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document

# 项目导入
from ..utils.logger import get_logger
from config.settings import RAG_CONFIG

logger = get_logger(__name__)

class LangChainRAGSystem:
    """
    基于LangChain的RAG系统
    
    集成BGE中文嵌入模型和ChromaDB向量数据库，
    为交通专业知识检索提供高效、准确的RAG服务。
    """
    
    def __init__(self, 
                 model_name: str = "BAAI/bge-large-zh-v1.5",
                 persist_directory: str = "./data/langchain_vectors",
                 collection_name: str = "traffic_knowledge"):
        """
        初始化RAG系统
        
        Args:
            model_name: 嵌入模型名称
            persist_directory: ChromaDB持久化目录
            collection_name: 集合名称
        """
        self.model_name = model_name
        self.persist_directory = persist_directory
        self.collection_name = collection_name
        
        # 确保目录存在
        Path(persist_directory).mkdir(parents=True, exist_ok=True)
        
        # 初始化组件
        self._embedding_model = None
        self._vectorstore = None
        self._retriever = None
        
        # 初始化嵌入模型和向量存储
        self._initialize_components()
    
    def _initialize_components(self):
        """初始化RAG系统组件"""
        try:
            logger.info(f"🧠 初始化BGE嵌入模型: {self.model_name}")
            
            # 初始化嵌入模型 - 使用本地已下载的模型
            local_model_path = "./data/models/models--BAAI--bge-large-zh-v1.5/snapshots/79e7739b6ab944e86d6171e44d24c997fc1e0116"
            
            # 检查本地模型是否存在
            if Path(local_model_path).exists():
                logger.info(f"🎯 使用本地BGE模型: {local_model_path}")
                self._embedding_model = HuggingFaceEmbeddings(
                    model_name=local_model_path,  # 使用本地路径
                    model_kwargs={'device': 'cpu'},
                    encode_kwargs={'normalize_embeddings': True}
                )
            else:
                logger.info(f"🌐 本地模型不存在，使用在线模型: {self.model_name}")
                self._embedding_model = HuggingFaceEmbeddings(
                    model_name=self.model_name,
                    model_kwargs={'device': 'cpu'},
                    encode_kwargs={'normalize_embeddings': True}
                )
            
            logger.info(f"📚 初始化ChromaDB向量存储: {self.persist_directory}")
            
            # 初始化向量存储
            self._vectorstore = Chroma(
                collection_name=self.collection_name,
                embedding_function=self._embedding_model,
                persist_directory=self.persist_directory,
            )
            
            # 初始化检索器
            self._retriever = self._vectorstore.as_retriever(
                search_type="similarity",
                search_kwargs={"k": 3}
            )
            
            logger.info("✅ RAG系统组件初始化成功")
            
        except Exception as e:
            logger.error(f"❌ RAG系统初始化失败: {e}")
            raise e
    
    def add_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """
        添加文档到向量数据库
        
        Args:
            documents: 文档列表，每个文档包含content和metadata
            
        Returns:
            bool: 是否添加成功
        """
        try:
            logger.info(f"📝 准备添加 {len(documents)} 个文档到知识库...")
            
            # 转换为LangChain Document格式
            langchain_docs = []
            for doc in documents:
                content = doc.get('content', '')
                metadata = doc.get('metadata', {})
                
                # 添加额外的元数据
                metadata.update({
                    'source': doc.get('source', ''),
                    'type': doc.get('type', ''),
                    'law': metadata.get('law', ''),
                    'article': metadata.get('article', ''),
                    'category': metadata.get('category', ''),
                })
                
                langchain_docs.append(Document(
                    page_content=content,
                    metadata=metadata
                ))
            
            # 文本分割（如果需要）
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=1000,
                chunk_overlap=200,
                length_function=len,
            )
            
            # 分割文档
            split_docs = text_splitter.split_documents(langchain_docs)
            logger.info(f"📄 文档分割后得到 {len(split_docs)} 个文档块")
            
            # 添加到向量数据库
            self._vectorstore.add_documents(split_docs)
            
            # 持久化
            self._vectorstore.persist()
            
            logger.info(f"✅ 成功添加 {len(split_docs)} 个文档块到知识库")
            return True
            
        except Exception as e:
            logger.error(f"❌ 添加文档失败: {e}")
            return False
    
    def search_knowledge(self, query: str, k: int = 3) -> List[Dict[str, Any]]:
        """
        检索相关知识
        
        Args:
            query: 查询文本
            k: 返回结果数量
            
        Returns:
            List[Dict]: 检索结果列表
        """
        try:
            logger.info(f"🔍 检索知识: {query[:50]}...")
            
            # 使用向量检索
            search_results = self._vectorstore.similarity_search(query, k=k)
            
            # 转换结果格式
            results = []
            for doc in search_results:
                result = {
                    'content': doc.page_content,
                    'metadata': doc.metadata,
                    'source': doc.metadata.get('source', ''),
                    'law': doc.metadata.get('law', ''),
                    'article': doc.metadata.get('article', ''),
                    'category': doc.metadata.get('category', ''),
                }
                results.append(result)
            
            logger.info(f"✅ 找到 {len(results)} 条相关知识")
            return results
            
        except Exception as e:
            logger.error(f"❌ 知识检索失败: {e}")
            return []
    
    def search_with_scores(self, query: str, k: int = 3) -> List[tuple]:
        """
        带相似度分数的检索
        
        Args:
            query: 查询文本
            k: 返回结果数量
            
        Returns:
            List[tuple]: (Document, score) 列表
        """
        try:
            return self._vectorstore.similarity_search_with_score(query, k=k)
        except Exception as e:
            logger.error(f"❌ 带分数检索失败: {e}")
            return []
    
    def get_collection_info(self) -> Dict[str, Any]:
        """
        获取集合信息
        
        Returns:
            Dict: 集合统计信息
        """
        try:
            # 获取集合中的文档数量
            collection = self._vectorstore._collection
            count = collection.count()
            
            return {
                'status': 'active',
                'collection_name': self.collection_name,
                'document_count': count,
                'persist_directory': self.persist_directory,
                'embedding_model': self.model_name
            }
        except Exception as e:
            logger.error(f"❌ 获取集合信息失败: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def clear_collection(self) -> bool:
        """
        清空集合
        
        Returns:
            bool: 是否清空成功
        """
        try:
            logger.warning("🗑️ 清空知识库集合...")
            
            # 删除所有文档
            collection = self._vectorstore._collection
            collection.delete()
            
            logger.info("✅ 知识库集合已清空")
            return True
            
        except Exception as e:
            logger.error(f"❌ 清空集合失败: {e}")
            return False
    
    def update_retriever_config(self, search_type: str = "similarity", k: int = 3):
        """
        更新检索器配置
        
        Args:
            search_type: 检索类型 ('similarity', 'mmr', 'similarity_score_threshold')
            k: 返回结果数量
        """
        try:
            self._retriever = self._vectorstore.as_retriever(
                search_type=search_type,
                search_kwargs={"k": k}
            )
            logger.info(f"✅ 检索器配置已更新: {search_type}, k={k}")
        except Exception as e:
            logger.error(f"❌ 更新检索器配置失败: {e}")
    
    def get_retriever(self):
        """获取LangChain检索器对象"""
        return self._retriever
    
    def test_system(self) -> Dict[str, Any]:
        """
        测试RAG系统功能
        
        Returns:
            Dict: 测试结果
        """
        test_results = {
            'embedding_model': {'status': 'unknown'},
            'vectorstore': {'status': 'unknown'},
            'retriever': {'status': 'unknown'},
            'search_test': {'status': 'unknown'}
        }
        
        try:
            # 测试嵌入模型
            if self._embedding_model:
                test_text = "测试文本"
                embeddings = self._embedding_model.embed_query(test_text)
                test_results['embedding_model'] = {
                    'status': 'success',
                    'dimension': len(embeddings)
                }
            
            # 测试向量存储
            if self._vectorstore:
                info = self.get_collection_info()
                test_results['vectorstore'] = {
                    'status': 'success',
                    'collection_info': info
                }
            
            # 测试检索器
            if self._retriever:
                test_results['retriever'] = {'status': 'success'}
            
            # 测试搜索功能
            search_results = self.search_knowledge("测试查询", k=1)
            test_results['search_test'] = {
                'status': 'success',
                'results_count': len(search_results)
            }
            
        except Exception as e:
            test_results['error'] = str(e)
        
        return test_results

# 便捷函数
def create_rag_system(
    model_name: str = "BAAI/bge-large-zh-v1.5",
    persist_directory: str = "./data/langchain_vectors",
    collection_name: str = "traffic_knowledge"
) -> LangChainRAGSystem:
    """
    创建RAG系统实例
    
    Args:
        model_name: 嵌入模型名称
        persist_directory: 持久化目录
        collection_name: 集合名称
        
    Returns:
        LangChainRAGSystem: RAG系统实例
    """
    return LangChainRAGSystem(
        model_name=model_name,
        persist_directory=persist_directory,
        collection_name=collection_name
    )

# 使用示例
if __name__ == "__main__":
    # 创建RAG系统
    rag_system = create_rag_system()
    
    # 测试系统
    test_results = rag_system.test_system()
    print("RAG系统测试结果:")
    for component, result in test_results.items():
        print(f"  {component}: {result}")
    
    # 获取集合信息
    collection_info = rag_system.get_collection_info()
    print(f"\n集合信息: {collection_info}")