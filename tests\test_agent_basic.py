#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TrafficMultimodalAgent基本功能测试

这个测试脚本用于验证Agent的核心功能，包括：
1. Agent初始化和工具加载
2. 独立工具功能测试
3. 基本查询处理能力
4. 工具统计信息

运行方法：
    cd /home/<USER>/lhp/projects/0714agent/my-agent1
    python -m pytest tests/test_agent_basic.py -v
    
或者直接运行：
    python tests/test_agent_basic.py
"""

import asyncio
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.agent.traffic_multimodal_agent import TrafficMultimodalAgent
from src.utils.logger import get_logger

logger = get_logger(__name__)

def load_test_scenarios():
    """加载测试场景数据"""
    scenarios_file = project_root / "data" / "test_cases" / "traffic_scenarios.json"
    try:
        with open(scenarios_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data['test_scenarios']
    except Exception as e:
        logger.error(f"加载测试场景失败: {e}")
        return []

async def test_agent_initialization():
    """测试Agent初始化"""
    logger.info("🧪 测试Agent初始化...")
    
    try:
        agent = TrafficMultimodalAgent()
        await agent.initialize()
        
        # 验证关键组件
        assert agent.image_analyzer is not None, "图像分析工具未初始化"
        assert agent.violation_detector is not None, "违规检测工具未初始化"
        assert agent.regulation_searcher is not None, "法规检索工具未初始化"
        assert agent.safety_assessor is not None, "安全评估工具未初始化"
        assert agent.suggestion_generator is not None, "建议生成工具未初始化"
        assert len(agent.tools) == 5, f"工具数量不正确，期望5个，实际{len(agent.tools)}"
        
        logger.info("✅ Agent初始化测试通过")
        return agent
        
    except Exception as e:
        logger.error(f"❌ Agent初始化失败: {e}")
        raise e

async def test_tools_functionality(agent):
    """测试工具基本功能"""
    logger.info("🧪 测试工具基本功能...")
    
    # 加载真实测试场景数据
    scenarios = load_test_scenarios()
    if not scenarios:
        logger.error("❌ 无法加载测试场景数据")
        raise Exception("测试场景数据加载失败")
    
    # 使用第一个场景进行工具测试（机动车未礼让行人）
    test_scenario = scenarios[0]  # scenario_001
    scene_data = test_scenario["scene_data"]
    
    logger.info(f"📋 使用测试场景: {test_scenario['name']}")
    logger.info(f"📝 场景描述: {test_scenario['description']}")
    
    results = {}
    
    try:
        # 测试违规检测工具
        logger.info("🔍 测试违规检测工具...")
        violation_result = await agent.violation_detector.detect(scene_data)
        results["violation_detector"] = violation_result.get("success", False)
        logger.info(f"   检测到违规数量: {violation_result.get('total_violations', 0)}")
        
        # 测试法规检索工具
        logger.info("📖 测试法规检索工具...")
        regulation_result = await agent.regulation_searcher.search("机动车未礼让行人")
        results["regulation_searcher"] = regulation_result.get("success", False)
        logger.info(f"   找到法规条目: {len(regulation_result.get('results', []))}条")
        
        # 测试安全评估工具
        logger.info("🛡️ 测试安全评估工具...")
        safety_result = await agent.safety_assessor.assess(scene_data, violation_result)
        results["safety_assessor"] = safety_result.get("success", False)
        logger.info(f"   安全等级: {safety_result.get('safety_level', '未知')}")
        
        # 测试建议生成工具
        logger.info("💡 测试建议生成工具...")
        suggestion_result = await agent.suggestion_generator.generate(
            scene_data, violation_result, {"safety_assessment": safety_result}
        )
        results["suggestion_generator"] = suggestion_result.get("success", False)
        logger.info(f"   生成建议数量: {len(suggestion_result.get('suggestions', []))}")
        
        # 显示详细结果
        logger.info("📊 各工具测试结果:")
        for tool_name, success in results.items():
            status = "✅ 通过" if success else "❌ 失败"
            logger.info(f"   - {tool_name}: {status}")
        
        # 验证结果
        failed_tools = [tool for tool, success in results.items() if not success]
        if failed_tools:
            logger.error(f"❌ 以下工具测试失败: {failed_tools}")
            raise AssertionError(f"工具测试失败: {failed_tools}")
        
        logger.info("✅ 所有工具功能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 工具功能测试失败: {e}")
        raise e

async def test_basic_query():
    """测试基本查询功能"""
    logger.info("🧪 测试基本查询功能...")
    
    try:
        # 加载测试场景
        scenarios = load_test_scenarios()
        if not scenarios:
            raise Exception("无法加载测试场景数据")
        
        agent = TrafficMultimodalAgent()
        await agent.initialize()
        
        # 使用第一个场景进行查询测试
        test_scenario = scenarios[0]  # scenario_001: 机动车未礼让行人
        agent._current_scene_data = test_scenario["scene_data"]
        
        # 测试场景中的查询
        test_query = test_scenario["test_queries"][0]  # "分析这个交通场景中的违规行为"
        
        logger.info(f"📝 测试查询: {test_query}")
        logger.info(f"🎯 使用场景: {test_scenario['name']}")
        
        # 设置超时避免循环
        import asyncio
        result = await asyncio.wait_for(agent.process_query(test_query), timeout=45)
        
        # 验证结果
        assert result.get("success", False), f"查询处理失败: {result.get('error', '未知错误')}"
        assert "answer" in result, "返回结果缺少answer字段"
        
        answer = result["answer"]
        assert len(answer) > 50, f"回答内容过短 ({len(answer)} 字符): {answer}"
        
        # 检查回答质量 - 更全面的评估
        quality_indicators = {
            "法规引用": ["法", "条", "规定", "《", "》"],
            "违规识别": ["违", "违规", "违法", "问题"],
            "改进建议": ["建议", "措施", "改进", "优化"],
            "安全评估": ["安全", "风险", "等级", "评估"]
        }
        
        quality_results = {}
        for category, keywords in quality_indicators.items():
            score = sum(1 for keyword in keywords if keyword in answer)
            quality_results[category] = score > 0
        
        overall_quality = sum(quality_results.values())
        
        logger.info("✅ 基本查询测试通过")
        logger.info(f"📝 查询响应长度: {len(answer)} 字符")
        logger.info(f"📊 回答质量分析:")
        for category, passed in quality_results.items():
            status = "✅" if passed else "❌"
            logger.info(f"   - {category}: {status}")
        logger.info(f"📄 回答预览: {answer[:200]}...")
        
        if overall_quality >= 3:
            logger.info("🎯 回答质量优秀")
        elif overall_quality >= 2:
            logger.info("👍 回答质量良好")
        else:
            logger.warning("⚠️ 回答质量需要改进")
        
        return True
        
    except asyncio.TimeoutError:
        logger.error("❌ 查询超时 - Agent可能陷入循环")
        raise Exception("查询处理超时")
    except Exception as e:
        logger.error(f"❌ 基本查询测试失败: {e}")
        raise e

async def run_tests():
    """运行所有测试"""
    logger.info("🚀 开始TrafficMultimodalAgent基本功能测试")
    logger.info("=" * 60)
    
    tests = [
        ("Agent初始化", test_agent_initialization),
        ("基本查询功能", test_basic_query)
    ]
    
    results = []
    agent = None
    
    for test_name, test_func in tests:
        try:
            logger.info(f"🧪 执行测试: {test_name}")
            
            if test_name == "Agent初始化":
                agent = await test_func()
                results.append((test_name, True))
            elif test_name == "工具功能" and agent:
                await test_tools_functionality(agent)
                results.append((test_name, True))
            else:
                await test_func()
                results.append((test_name, True))
                
        except Exception as e:
            logger.error(f"❌ {test_name} 测试失败: {e}")
            results.append((test_name, False))
    
    # 如果Agent初始化成功，额外测试工具功能
    if agent:
        try:
            logger.info(f"🧪 执行测试: 工具功能")
            await test_tools_functionality(agent)
            results.append(("工具功能", True))
        except Exception as e:
            logger.error(f"❌ 工具功能测试失败: {e}")
            results.append(("工具功能", False))
    
    # 汇总结果
    logger.info("=" * 60)
    logger.info("📋 测试结果汇总:")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        logger.info(f"   - {test_name}: {status}")
    
    logger.info("=" * 60)
    logger.info(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！Agent基本功能正常")
        return True
    else:
        logger.warning(f"⚠️ 有 {total - passed} 项测试失败")
        return False

async def main():
    """主函数"""
    try:
        success = await run_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        logger.info("🛑 测试被用户中断")
        return 1
    except Exception as e:
        logger.error(f"❌ 测试过程出现意外错误: {e}")
        return 1

if __name__ == "__main__":
    # Windows兼容性设置
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行测试
    exit_code = asyncio.run(main())
    sys.exit(exit_code)