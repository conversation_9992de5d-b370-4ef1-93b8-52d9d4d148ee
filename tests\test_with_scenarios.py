#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于场景数据集的Agent测试

这个测试脚本使用预定义的交通场景数据集来测试Agent的各项功能，
包括违规检测、法规检索、安全评估和建议生成。

运行方法：
    cd /home/<USER>/lhp/projects/0714agent/my-agent1
    python tests/test_with_scenarios.py
    
或者测试特定场景：
    python tests/test_with_scenarios.py --scenario scenario_001
"""

import asyncio
import sys
import json
import argparse
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.agent.traffic_multimodal_agent import TrafficMultimodalAgent
from src.utils.logger import get_logger

logger = get_logger(__name__)

class ScenarioTester:
    """场景测试器 - 使用预定义数据集测试Agent功能"""
    
    def __init__(self, scenarios_file: str = None):
        """
        初始化测试器
        
        Args:
            scenarios_file: 场景数据文件路径，默认使用项目内置数据集
        """
        if scenarios_file is None:
            scenarios_file = project_root / "data" / "test_cases" / "traffic_scenarios.json"
        
        self.scenarios_file = Path(scenarios_file)
        self.scenarios = self._load_scenarios()
        self.agent = None
        
    def _load_scenarios(self) -> List[Dict[str, Any]]:
        """加载测试场景数据"""
        try:
            with open(self.scenarios_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data['test_scenarios']
        except Exception as e:
            logger.error(f"加载场景数据失败: {e}")
            return []
    
    async def initialize_agent(self):
        """初始化Agent"""
        logger.info("🤖 初始化TrafficMultimodalAgent...")
        self.agent = TrafficMultimodalAgent()
        await self.agent.initialize()
        logger.info("✅ Agent初始化完成")
    
    def get_scenario(self, scenario_id: str) -> Dict[str, Any]:
        """获取指定场景数据"""
        for scenario in self.scenarios:
            if scenario['id'] == scenario_id:
                return scenario
        return None
    
    def list_scenarios(self):
        """列出所有可用场景"""
        logger.info("📋 可用测试场景:")
        for scenario in self.scenarios:
            logger.info(f"  - {scenario['id']}: {scenario['name']}")
            logger.info(f"    {scenario['description']}")
    
    async def test_scenario(self, scenario_id: str, detailed: bool = True) -> Dict[str, Any]:
        """
        测试指定场景
        
        Args:
            scenario_id: 场景ID
            detailed: 是否显示详细结果
            
        Returns:
            测试结果字典
        """
        scenario = self.get_scenario(scenario_id)
        if not scenario:
            logger.error(f"❌ 场景 {scenario_id} 不存在")
            return {"success": False, "error": "场景不存在"}
        
        logger.info(f"🧪 测试场景: {scenario['name']}")
        logger.info(f"📝 描述: {scenario['description']}")
        
        # 准备场景数据给Agent
        # 将结构化场景数据设置为Agent的当前场景
        self.agent._current_scene_data = scenario['scene_data']
        
        results = {}
        
        # 测试各个查询
        for i, query in enumerate(scenario['test_queries'], 1):
            logger.info(f"📝 查询 {i}: {query}")
            
            try:
                # 直接使用文本查询，Agent会根据查询内容决定使用哪些工具
                result = await self.agent.process_query(query)
                
                if result.get('success'):
                    answer = result.get('answer', '')
                    results[f'query_{i}'] = {
                        'query': query,
                        'success': True,
                        'answer': answer,
                        'answer_length': len(answer)
                    }
                    
                    if detailed:
                        logger.info(f"✅ 查询 {i} 成功")
                        logger.info(f"📋 回答长度: {len(answer)} 字符")
                        logger.info(f"📄 回答预览: {answer[:200]}...")
                    else:
                        logger.info(f"✅ 查询 {i} 成功 (长度: {len(answer)})")
                        
                else:
                    error = result.get('error', '未知错误')
                    results[f'query_{i}'] = {
                        'query': query,
                        'success': False,
                        'error': error
                    }
                    logger.error(f"❌ 查询 {i} 失败: {error}")
                    
            except Exception as e:
                results[f'query_{i}'] = {
                    'query': query,
                    'success': False,
                    'error': str(e)
                }
                logger.error(f"❌ 查询 {i} 异常: {e}")
        
        # 统计结果
        total_queries = len(scenario['test_queries'])
        successful_queries = sum(1 for r in results.values() if r.get('success', False))
        
        results['summary'] = {
            'scenario_id': scenario_id,
            'scenario_name': scenario['name'],
            'total_queries': total_queries,
            'successful_queries': successful_queries,
            'success_rate': successful_queries / total_queries if total_queries > 0 else 0,
            'overall_success': successful_queries == total_queries
        }
        
        return results
    
    async def test_all_scenarios(self, detailed: bool = False) -> Dict[str, Any]:
        """测试所有场景"""
        logger.info("🚀 开始测试所有场景...")
        logger.info("=" * 60)
        
        all_results = {}
        overall_stats = {
            'total_scenarios': len(self.scenarios),
            'successful_scenarios': 0,
            'total_queries': 0,
            'successful_queries': 0
        }
        
        for scenario in self.scenarios:
            scenario_id = scenario['id']
            logger.info(f"\n🧪 测试场景: {scenario_id}")
            logger.info("-" * 40)
            
            result = await self.test_scenario(scenario_id, detailed=detailed)
            all_results[scenario_id] = result
            
            # 统计
            if result.get('summary', {}).get('overall_success', False):
                overall_stats['successful_scenarios'] += 1
            
            overall_stats['total_queries'] += result.get('summary', {}).get('total_queries', 0)
            overall_stats['successful_queries'] += result.get('summary', {}).get('successful_queries', 0)
        
        # 计算总体成功率
        overall_stats['scenario_success_rate'] = (
            overall_stats['successful_scenarios'] / overall_stats['total_scenarios']
            if overall_stats['total_scenarios'] > 0 else 0
        )
        overall_stats['query_success_rate'] = (
            overall_stats['successful_queries'] / overall_stats['total_queries']
            if overall_stats['total_queries'] > 0 else 0
        )
        
        # 显示总结
        logger.info("\n" + "=" * 60)
        logger.info("📊 测试结果总结:")
        logger.info(f"📋 总场景数: {overall_stats['total_scenarios']}")
        logger.info(f"✅ 成功场景: {overall_stats['successful_scenarios']}")
        logger.info(f"📝 总查询数: {overall_stats['total_queries']}")
        logger.info(f"✅ 成功查询: {overall_stats['successful_queries']}")
        logger.info(f"🎯 场景成功率: {overall_stats['scenario_success_rate']:.1%}")
        logger.info(f"🎯 查询成功率: {overall_stats['query_success_rate']:.1%}")
        
        if overall_stats['scenario_success_rate'] >= 0.8:
            logger.info("🎉 测试结果优秀！")
        elif overall_stats['scenario_success_rate'] >= 0.6:
            logger.info("👍 测试结果良好")
        else:
            logger.warning("⚠️ 需要进一步优化")
        
        all_results['overall_stats'] = overall_stats
        return all_results

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='基于场景数据集的Agent测试')
    parser.add_argument('--scenario', type=str, help='测试特定场景ID')
    parser.add_argument('--list', action='store_true', help='列出所有场景')
    parser.add_argument('--detailed', action='store_true', help='显示详细结果')
    
    args = parser.parse_args()
    
    try:
        # 初始化测试器
        tester = ScenarioTester()
        
        if args.list:
            tester.list_scenarios()
            return 0
        
        # 初始化Agent
        await tester.initialize_agent()
        
        if args.scenario:
            # 测试特定场景
            result = await tester.test_scenario(args.scenario, detailed=True)
            success = result.get('summary', {}).get('overall_success', False)
            return 0 if success else 1
        else:
            # 测试所有场景
            results = await tester.test_all_scenarios(detailed=args.detailed)
            success_rate = results.get('overall_stats', {}).get('scenario_success_rate', 0)
            return 0 if success_rate >= 0.8 else 1
            
    except KeyboardInterrupt:
        logger.info("🛑 测试被用户中断")
        return 1
    except Exception as e:
        logger.error(f"❌ 测试过程出现错误: {e}")
        return 1

if __name__ == "__main__":
    # Windows兼容性设置
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行测试
    exit_code = asyncio.run(main())
    sys.exit(exit_code)