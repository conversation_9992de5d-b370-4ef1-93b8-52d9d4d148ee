{"test_scenarios": [{"id": "scenario_001", "name": "机动车未礼让行人", "description": "城市道路人行横道处，行人正在过街，机动车未停车让行", "scene_data": {"traffic_participants": {"motor_vehicles": {"count": 1, "types": ["小汽车"], "behaviors": ["正常行驶", "未停车让行"], "locations": ["人行横道前"]}, "pedestrians": {"count": 2, "locations": ["人行横道"], "behaviors": ["正常过街"], "status": ["正在通过人行横道"]}}, "road_environment": {"road_type": "城市道路", "lane_config": "双向四车道", "surface_condition": "干燥", "visibility": "良好"}, "traffic_facilities": {"crosswalk": {"present": true, "condition": "良好", "marked": true}, "traffic_signals": {"present": false}, "signs": ["人行横道标志"]}, "environment": {"weather": "晴天", "lighting": "白天", "visibility": "良好"}, "key_observations": ["行人正在通过标有人行横道标线的区域", "机动车接近人行横道时未减速", "机动车未停车等待行人通过", "道路标识清晰可见"]}, "expected_violations": [{"type": "机动车未礼让行人", "severity": "high", "legal_reference": "《道路交通安全法》第47条", "penalty": "罚款200元，记3分"}], "expected_safety_level": "C", "test_queries": ["分析这个交通场景中的违规行为", "机动车在人行横道的行为是否合规？", "这种情况下应该如何处理？"]}, {"id": "scenario_002", "name": "违法停车", "description": "机动车在人行道上违法停车", "scene_data": {"traffic_participants": {"motor_vehicles": {"count": 1, "types": ["SUV"], "behaviors": ["违法停车"], "locations": ["人行道"]}, "pedestrians": {"count": 0, "locations": [], "behaviors": []}}, "road_environment": {"road_type": "城市道路", "lane_config": "单向三车道", "surface_condition": "干燥", "visibility": "良好"}, "traffic_facilities": {"sidewalk": {"present": true, "width": "2米", "condition": "良好"}, "no_parking_signs": {"present": true, "visibility": "清晰"}}, "environment": {"weather": "晴天", "lighting": "白天", "visibility": "良好"}, "key_observations": ["车辆完全停放在人行道上", "阻挡行人正常通行", "附近有禁止停车标志", "车主不在现场"]}, "expected_violations": [{"type": "违法停车", "severity": "medium", "legal_reference": "《道路交通安全法》第56条", "penalty": "罚款100-200元"}], "expected_safety_level": "B", "test_queries": ["这种停车行为是否违法？", "应该如何处罚这种违法停车？", "对行人通行有什么影响？"]}, {"id": "scenario_003", "name": "闯红灯", "description": "机动车在红灯时通过交叉口", "scene_data": {"traffic_participants": {"motor_vehicles": {"count": 1, "types": ["小轿车"], "behaviors": ["闯红灯"], "locations": ["交叉口"]}, "pedestrians": {"count": 0, "locations": [], "behaviors": []}}, "road_environment": {"road_type": "城市主干道", "lane_config": "双向六车道", "surface_condition": "干燥", "visibility": "良好"}, "traffic_facilities": {"traffic_signals": {"present": true, "current_state": "红灯", "visibility": "清晰"}, "stop_line": {"present": true, "condition": "清晰"}}, "environment": {"weather": "晴天", "lighting": "白天", "visibility": "良好"}, "key_observations": ["信号灯显示红灯", "机动车越过停止线继续行驶", "通过整个交叉口", "无紧急情况"]}, "expected_violations": [{"type": "闯红灯", "severity": "high", "legal_reference": "《道路交通安全法》第38条", "penalty": "罚款200元，记6分"}], "expected_safety_level": "D", "test_queries": ["机动车闯红灯的处罚标准是什么？", "这种行为的安全风险如何？", "如何预防闯红灯行为？"]}, {"id": "scenario_004", "name": "非机动车占用机动车道", "description": "电动车在机动车道内行驶", "scene_data": {"traffic_participants": {"motor_vehicles": {"count": 2, "types": ["小汽车", "公交车"], "behaviors": ["正常行驶"], "locations": ["机动车道"]}, "non_motor_vehicles": {"count": 1, "types": ["电动车"], "behaviors": ["占用机动车道"], "locations": ["机动车道右侧"]}}, "road_environment": {"road_type": "城市道路", "lane_config": "双向四车道+非机动车道", "surface_condition": "干燥", "visibility": "良好"}, "traffic_facilities": {"bike_lane": {"present": true, "condition": "良好", "separated": true}, "lane_markings": {"present": true, "condition": "清晰"}}, "environment": {"weather": "晴天", "lighting": "白天", "visibility": "良好"}, "key_observations": ["电动车在机动车道内行驶", "旁边有专用的非机动车道", "影响机动车正常行驶", "车道分界线清晰"]}, "expected_violations": [{"type": "非机动车违规", "severity": "medium", "legal_reference": "《道路交通安全法》第57条", "penalty": "罚款5-50元"}], "expected_safety_level": "C", "test_queries": ["非机动车可以在机动车道行驶吗？", "这种违规行为如何处罚？", "如何规范非机动车的通行？"]}, {"id": "scenario_005", "name": "正常交通场景", "description": "各类交通参与者都按规行驶的正常场景", "scene_data": {"traffic_participants": {"motor_vehicles": {"count": 3, "types": ["小汽车", "货车"], "behaviors": ["正常行驶", "保持车距"], "locations": ["各自车道"]}, "pedestrians": {"count": 1, "locations": ["人行道"], "behaviors": ["正常步行"]}, "non_motor_vehicles": {"count": 1, "types": ["自行车"], "behaviors": ["正常行驶"], "locations": ["非机动车道"]}}, "road_environment": {"road_type": "城市道路", "lane_config": "双向四车道+非机动车道", "surface_condition": "干燥", "visibility": "良好"}, "traffic_facilities": {"traffic_signals": {"present": true, "current_state": "绿灯", "visibility": "清晰"}, "crosswalk": {"present": true, "condition": "良好"}, "bike_lane": {"present": true, "condition": "良好"}}, "environment": {"weather": "晴天", "lighting": "白天", "visibility": "良好"}, "key_observations": ["所有车辆都在各自车道内行驶", "机动车与非机动车分道行驶", "行人在人行道正常步行", "交通秩序良好"]}, "expected_violations": [], "expected_safety_level": "A", "test_queries": ["这个交通场景是否存在问题？", "交通秩序如何？", "有什么值得表扬的地方？"]}], "metadata": {"version": "1.0", "created_date": "2024-07-23", "total_scenarios": 5, "categories": ["机动车违规", "停车违规", "信号违规", "非机动车违规", "正常场景"], "description": "智能交通多模态RAG助手测试数据集，包含典型交通场景的结构化描述数据"}}