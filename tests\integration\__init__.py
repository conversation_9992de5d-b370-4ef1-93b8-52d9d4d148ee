# -*- coding: utf-8 -*-
"""
集成测试模块 - Integration Tests Module

这个模块包含了系统组件间集成测试。
测试范围：

1. Agent-RAG集成测试
   - Agent与知识库的协同工作测试
   - 多模态查询的端到端处理测试
   - 工具链调用的完整流程测试

2. Agent-Tools集成测试  
   - Agent与各工具的交互测试
   - 工具调用序列和依赖关系测试
   - 错误传播和处理机制测试

3. RAG-Knowledge集成测试
   - 知识库与检索器的集成测试
   - 嵌入模型与向量数据库的配合测试
   - 实时更新和数据一致性测试

4. Web-Backend集成测试
   - 前端界面与后端服务的集成测试
   - 文件上传和处理流程测试
   - 会话管理和状态同步测试

5. 外部服务集成测试
   - Ollama服务连接和通信测试
   - ChromaDB数据库连接测试
   - 第三方API集成测试

集成测试验证各组件协同工作的正确性，
确保数据流转和接口对接的可靠性。
"""