#!/usr/bin/env python3
"""
下载测试图片脚本
从多个免费图片源获取交通场景测试图片

注意：此脚本提供了推荐的图片源和下载指导，
实际下载需要手动访问网站或使用API
"""

import os
import requests
from pathlib import Path

# 推荐的高质量交通场景图片下载链接
RECOMMENDED_IMAGES = {
    "scenario_001_pedestrian_crossing.jpg": {
        "description": "行人过街场景",
        "sources": [
            "https://stockcake.com/i/busy-crosswalk-intersection_1166594",
            "https://pixabay.com/photos/crosswalk-pedestrian-crossing-1031813/",
        ],
        "manual_search": "搜索关键词: pedestrian crossing busy street"
    },
    
    "scenario_002_illegal_parking.jpg": {
        "description": "违法停车场景", 
        "sources": [
            "https://stockcake.com/i/parked-car-street_892847",
            "https://pixabay.com/photos/parking-car-street-urban-city-1509216/",
        ],
        "manual_search": "搜索关键词: car parking street sidewalk"
    },
    
    "scenario_003_traffic_light.jpg": {
        "description": "交通信号灯场景",
        "sources": [
            "https://stockcake.com/i/traffic-light-intersection_743829",
            "https://pixabay.com/photos/traffic-lights-traffic-semaphore-876050/",
        ],
        "manual_search": "搜索关键词: traffic light intersection red"
    },
    
    "scenario_004_bike_lane.jpg": {
        "description": "自行车道场景",
        "sources": [
            "https://stockcake.com/i/bike-lane-street_567234",
            "https://pixabay.com/photos/bicycle-lane-bike-path-cycling-1867205/",
        ],
        "manual_search": "搜索关键词: bike lane bicycle path street"
    },
    
    "scenario_005_normal_traffic.jpg": {
        "description": "正常交通场景",
        "sources": [
            "https://stockcake.com/i/city-traffic-flow_334567",
            "https://pixabay.com/photos/traffic-cars-transportation-street-681623/",
        ], 
        "manual_search": "搜索关键词: normal traffic flow cars street"
    }
}

def print_download_guide():
    """打印下载指南"""
    print("🚗 交通场景测试图片下载指南")
    print("=" * 50)
    
    target_dir = "/home/<USER>/lhp/projects/0714agent/my-agent1/data/test_images"
    print(f"目标目录: {target_dir}")
    print()
    
    for filename, info in RECOMMENDED_IMAGES.items():
        print(f"📁 {filename}")
        print(f"   描述: {info['description']}")
        print(f"   推荐源:")
        for i, source in enumerate(info['sources'], 1):
            print(f"     {i}. {source}")
        print(f"   手动搜索: {info['manual_search']}")
        print()
    
    print("🔧 下载步骤:")
    print("1. 访问上述推荐网站")
    print("2. 搜索对应关键词找到合适图片")
    print("3. 下载高分辨率JPG格式")
    print("4. 重命名并保存到目标目录")
    print()
    
    print("💡 快速备选方案:")
    print("• StockCake: https://stockcake.com/s/traffic")
    print("• Pixabay: https://pixabay.com/images/search/traffic%20scene/")
    print("• Pexels: https://www.pexels.com/search/street%20traffic/")
    print()

def create_download_commands():
    """创建wget下载命令模板"""
    target_dir = "/home/<USER>/lhp/projects/0714agent/my-agent1/data/test_images"
    
    print("🚀 如果找到直接图片链接，可以使用以下命令下载:")
    print()
    
    for filename in RECOMMENDED_IMAGES.keys():
        print(f"# 下载 {filename}")
        print(f"# wget -O '{target_dir}/{filename}' 'YOUR_IMAGE_URL_HERE'")
        print()

def check_target_directory():
    """检查目标目录"""
    target_dir = Path("/home/<USER>/lhp/projects/0714agent/my-agent1/data/test_images")
    
    if not target_dir.exists():
        target_dir.mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目标目录: {target_dir}")
    else:
        print(f"📁 目标目录已存在: {target_dir}")
    
    # 检查现有文件
    existing_files = list(target_dir.glob("*.jpg")) + list(target_dir.glob("*.png"))
    if existing_files:
        print(f"📸 现有图片文件: {len(existing_files)} 个")
        for f in existing_files:
            print(f"   - {f.name}")
    else:
        print("📸 目录为空，需要下载测试图片")

def main():
    """主函数"""
    check_target_directory()
    print()
    print_download_guide()
    print()
    create_download_commands()
    
    print("\n🎯 推荐操作:")
    print("1. 运行此脚本查看下载指南")
    print("2. 手动访问推荐网站下载图片")
    print("3. 或者告诉我具体的图片URL，我帮您下载")

if __name__ == "__main__":
    main()