#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载BGE中文嵌入模型脚本
"""

from sentence_transformers import SentenceTransformer
import os

def main():
    print('🔄 开始下载BGE中文嵌入模型...')
    print('📍 模型: BAAI/bge-large-zh-v1.5')
    print('💾 缓存目录: ./data/models')

    # 确保目录存在
    os.makedirs('./data/models', exist_ok=True)

    try:
        # 下载模型（这会自动缓存到指定目录）
        print('⏬ 正在下载模型文件...')
        model = SentenceTransformer(
            'BAAI/bge-large-zh-v1.5',
            cache_folder='./data/models'
        )

        print('✅ BGE模型下载完成!')
        print(f'📏 向量维度: {model.get_sentence_embedding_dimension()}')

        # 测试嵌入
        print('🧪 测试模型功能...')
        test_text = '测试中文嵌入功能'
        embedding = model.encode([test_text])
        print(f'✅ 测试嵌入成功，维度: {len(embedding[0])}')
        print('🎉 BGE模型已准备就绪!')
        
    except Exception as e:
        print(f'❌ 下载失败: {e}')
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)