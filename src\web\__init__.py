# -*- coding: utf-8 -*-
"""
Web界面模块 - Web Interface Module

这个模块实现了基于Streamlit的专业Web用户界面。
主要功能包括：

1. 主应用程序 (main_app.py)
   - Streamlit主界面框架
   - 页面路由和状态管理
   - 响应式布局设计

2. 多模态输入界面 (input_interface.py)
   - 图像上传和预览功能
   - 文本查询输入框
   - 分析参数配置选项
   - 输入验证和错误处理

3. 结果展示界面 (result_display.py)
   - 分析结果的可视化展示
   - 违规行为卡片展示
   - 法规引用格式化显示
   - 安全评估图表可视化

4. 状态监控界面 (status_monitor.py)
   - 系统运行状态实时监控
   - 性能指标图表展示
   - 服务健康状态检查
   - 错误日志查看

5. 用户交互组件 (ui_components.py)
   - 自定义UI组件库
   - 样式和主题配置
   - 交互式图表组件
   - 响应式设计元素

6. 会话管理 (session_manager.py)
   - 用户会话状态管理
   - 分析历史记录
   - 结果缓存机制
   - 用户偏好设置

界面设计注重专业性和用户体验，提供直观的多模态交互方式，
清晰展示分析结果，支持移动端和桌面端访问。
"""