# ================================
# 智能交通多模态RAG助手 - 已安装包列表
# 环境: traffic-agent (Python 3.10)
# 更新时间: 2025-07-22
# ================================

# ================================
# 深度学习与AI核心框架
# ================================
torch==2.7.1+cu118           # PyTorch深度学习框架(CUDA 11.8版本)
torchaudio==2.7.1+cu118      # PyTorch音频处理库
torchvision==0.22.1+cu118    # PyTorch视觉处理库
triton==3.3.1                # PyTorch加速器
transformers==4.53.2         # HuggingFace Transformers库
accelerate==1.9.0            # 模型训练加速库
bitsandbytes==0.46.0         # 量化训练库
safetensors==0.5.3           # 安全张量存储
tokenizers==0.21.2           # 快速文本分词器
sentence-transformers==5.0.0 # 句向量模型库
huggingface-hub==0.33.2      # HuggingFace模型中心
modelscope==1.27.1           # 魔搭模型社区
deepspeed==0.17.1           # 微软分布式训练框架
datasets==3.6.0             # 数据集处理库
py-data-juicer==1.4.0       # 数据清洗工具

# ================================
# 本地模型管理
# ================================
ollama==0.5.1               # Ollama Python客户端

# ================================
# RAG与向量数据库
# ================================
chromadb==1.0.15            # 向量数据库
langchain==0.3.26           # LangChain框架
langchain-community==0.3.27 # LangChain社区版
langchain-core==0.3.69      # LangChain核心组件
langchain-text-splitters==0.3.8 # 文本分割器
langsmith==0.4.8            # LangChain调试工具
rank-bm25==0.2.2            # BM25检索算法

# ================================
# Web框架与API
# ================================
fastapi==0.115.13           # FastAPI Web框架
uvicorn==0.35.0             # ASGI服务器
starlette==0.46.2           # ASGI框架核心
streamlit==1.47.0           # 数据应用框架
streamlit-chat==0.1.1       # Streamlit聊天组件

# ================================
# 数据处理与科学计算
# ================================
numpy==1.26.4               # 数值计算基础库
pandas==2.3.1               # 数据分析库
scipy==1.15.3               # 科学计算库
scikit-learn==1.7.1         # 机器学习库
matplotlib==3.10.3          # 数据可视化
plotly==6.1.2               # 交互式可视化
altair==5.5.0               # 声明式可视化

# ================================
# 计算机视觉与图像处理
# ================================
opencv-python==*********    # OpenCV计算机视觉库
pillow==11.2.1              # Python图像处理库
timm==1.0.16                # PyTorch图像模型库
einops==0.8.1               # 张量操作简化

# ================================
# 自然语言处理
# ================================
spacy==3.7.0                # NLP工具库
jieba==0.42.1               # 中文分词
emoji==2.2.0                # 表情符号处理
langcodes==3.5.0            # 语言代码处理
language_data==1.3.0        # 语言数据
sentencepiece==0.2.0        # 文本分词器

# ================================
# 文档处理
# ================================
PyPDF2==3.0.1               # PDF处理库
python-docx==1.2.0          # Word文档处理
pdfplumber==0.11.7          # PDF文本提取
pdfminer.six==20250506      # PDF挖掘工具
beautifulsoup4==4.13.4      # HTML/XML解析
bs4==0.0.2                  # BeautifulSoup4简化接口
lxml==5.4.0                 # XML/HTML解析器

# ================================
# 数据库与缓存
# ================================
redis==6.2.0                # Redis缓存数据库
psycopg2-binary==2.9.10     # PostgreSQL适配器
SQLAlchemy==2.0.41          # SQL工具包
diskcache==5.6.3            # 磁盘缓存

# ================================
# 网络请求与通信
# ================================
requests==2.32.4            # HTTP请求库
aiohttp==3.12.13            # 异步HTTP客户端/服务器
httpx==0.28.1               # 现代HTTP客户端
httpx-sse==0.4.1            # Server-Sent Events支持
websockets==15.0.1          # WebSocket库
urllib3==2.5.0              # HTTP库
certifi==2025.7.14          # CA证书包

# ================================
# 异步编程
# ================================
asyncio相关:
anyio==4.9.0                # 异步抽象层
async-timeout==4.0.3        # 异步超时
aiohappyeyeballs==2.6.1     # 异步DNS解析
aiosignal==1.3.2            # 异步信号
uvloop==0.21.0              # 高性能事件循环

# ================================
# 监控与日志
# ================================
prometheus_client==0.22.1   # Prometheus监控客户端
psutil==7.0.0               # 系统资源监控
loguru==0.7.3               # 现代日志库
wandb==0.20.1               # 实验跟踪平台
sentry-sdk==2.30.0          # 错误监控
opentelemetry相关:
opentelemetry-api==1.35.0   # OpenTelemetry API
opentelemetry-sdk==1.35.0   # OpenTelemetry SDK
opentelemetry-exporter-otlp-proto-grpc==1.35.0 # OTLP导出器

# ================================
# 开发工具
# ================================
black==25.1.0               # Python代码格式化
isort==6.0.1                # import排序
pytest==8.4.1              # 测试框架
ipython==8.37.0             # 交互式Python
debugpy==1.8.14             # Python调试器
fire==0.7.0                 # 命令行接口生成器

# ================================
# Jupyter生态系统
# ================================
jupyter==1.1.1              # Jupyter元包
jupyterlab==4.4.3           # JupyterLab
notebook==7.4.3             # Jupyter Notebook
ipykernel==6.29.5           # Jupyter内核
ipywidgets==8.1.7           # Jupyter小部件

# ================================
# 序列化与配置
# ================================
PyYAML==6.0.2               # YAML解析器
toml==0.10.2                # TOML解析器
orjson==3.11.0              # 高性能JSON库
jsonlines==4.0.0            # JSON Lines格式
python-dotenv==1.1.1        # 环境变量管理

# ================================
# 数据验证与API文档
# ================================
pydantic==2.11.7            # 数据验证
pydantic-settings==2.10.1   # Pydantic设置
marshmallow==3.26.1         # 对象序列化/验证

# ================================
# NVIDIA GPU支持库
# ================================
nvidia-cublas-cu11==*********      # CUDA BLAS库
nvidia-cuda-cupti-cu11==11.8.87    # CUDA性能工具
nvidia-cuda-nvrtc-cu11==11.8.89    # CUDA运行时编译
nvidia-cuda-runtime-cu11==11.8.89  # CUDA运行时
nvidia-cudnn-cu11==********        # CUDA深度神经网络库
nvidia-cufft-cu11==10.9.0.58       # CUDA FFT库
nvidia-curand-cu11==10.3.0.86      # CUDA随机数生成
nvidia-cusolver-cu11==11.4.1.48    # CUDA求解器
nvidia-cusparse-cu11==11.7.5.86    # CUDA稀疏库
nvidia-nccl-cu11==2.21.5           # NVIDIA集合通信库
nvidia-nvtx-cu11==11.8.86          # NVIDIA工具扩展
nvidia-ml-py==12.575.51            # NVIDIA管理库

# ================================
# 音频处理
# ================================
librosa==0.11.0             # 音频分析库
soundfile==0.13.1           # 音频文件读写
audioread==3.0.1            # 音频文件读取
resampy==0.4.3              # 音频重采样
soxr==0.5.0.post1           # 高质量音频重采样
samplerate==0.1.0           # 采样率转换

# ================================
# 视频处理
# ================================
av==13.1.0                  # 视频处理库

# ================================
# 工具库与实用程序
# ================================
tqdm==4.67.1                # 进度条
rich==14.0.0                # 终端美化
click==8.2.1                # 命令行工具
typer==0.9.4                # 现代CLI框架
tabulate==0.9.0             # 表格格式化
wget==3.2                   # 文件下载
watchdog==6.0.0             # 文件系统监控
filelock==3.13.1            # 文件锁
tenacity==9.1.2             # 重试库
backoff==2.2.1              # 退避重试

# ================================
# 其他依赖库
# ================================
six==1.17.0                 # Python 2/3兼容库
packaging==25.0             # 包装工具
setuptools (系统包)          # 安装工具
wheel (系统包)              # 打包工具
pip (系统包)                # 包管理器

# ================================
# 数据科学与可视化相关
# ================================
pyarrow==20.0.0             # 高性能数据处理
wordcloud==1.9.4            # 词云生成
Shapely==1.8.5.post1        # 几何对象操作
pycocotools==2.0.10         # COCO数据集工具
nuscenes-devkit==1.1.11     # nuScenes数据集工具

# ================================
# 特定领域工具
# ================================
onnxruntime==1.22.1         # ONNX运行时
numba==0.61.2               # JIT编译器
llvmlite==0.44.0            # LLVM绑定

# 注意事项:
# 1. 带有cu118标记的包是为CUDA 11.8编译的版本
# 2. 所有版本号已固定，确保环境一致性
# 3. 如需添加新包，请在对应分类下添加并注明用途
# 4. 定期更新此文件以保持同步
