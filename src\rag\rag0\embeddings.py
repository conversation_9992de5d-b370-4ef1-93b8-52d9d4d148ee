"""
BGE中文嵌入模型实现

基于BAAI/bge-large-zh-v1.5的高质量中文文本嵌入服务，为RAG系统提供语义向量化能力。

技术特点：
1. 中文优化 - 专门针对中文语义理解优化的预训练模型
2. 高性能 - 支持GPU/CPU自适应，批量处理提升效率
3. 缓存机制 - 本地模型缓存，避免重复下载
4. 相似度计算 - 内置余弦相似度计算功能
5. 异常处理 - 完善的错误处理和降级策略

模型信息：
- 模型名称: BAAI/bge-large-zh-v1.5
- 向量维度: 1024
- 适用场景: 中文文本语义检索、相似度计算
- 性能特点: 在中文信息检索任务上表现优异

使用说明：
该模块为RAG系统的核心组件之一，负责将文本转换为高质量的向量表示，
支持ChromaDB向量数据库的语义检索功能。
"""

from sentence_transformers import SentenceTransformer
from typing import List, Any, Dict, Optional
import numpy as np
import logging
import os
import time
import torch
from pathlib import Path

# 获取日志记录器
logger = logging.getLogger(__name__)

class BGEEmbeddings:
    """
    BGE中文嵌入模型封装类
    
    提供高质量的中文文本向量化服务，基于百度研究院开源的BGE模型，
    专门优化了中文语义理解能力，是目前中文检索领域的SOTA模型之一。
    
    Attributes:
        model_name (str): 使用的模型名称
        cache_folder (str): 模型缓存目录
        device (str): 运行设备 (cuda/cpu)
        model (SentenceTransformer): 加载的模型实例
        
    学习要点：
    - Sentence-Transformers库的使用
    - GPU/CPU设备自适应
    - 批量处理优化技巧
    - 向量相似度计算方法
    """
    
    def __init__(self, 
                 model_name: str = "BAAI/bge-large-zh-v1.5", 
                 cache_folder: str = "./data/models",
                 device: Optional[str] = None):
        """
        初始化BGE嵌入模型
        
        Args:
            model_name: 嵌入模型名称，默认使用BGE-large-zh-v1.5
            cache_folder: 模型缓存目录，用于存储下载的模型文件
            device: 指定运行设备，None时自动检测最优设备
            
        学习要点：
        - 模型初始化的最佳实践
        - 设备选择的策略
        - 缓存目录的管理
        - 异常处理的重要性
        """
        self.model_name = model_name
        self.cache_folder = cache_folder
        self.device = device or self._detect_best_device()
        self.model = None
        self._model_info = {}
        
        # 确保缓存目录存在
        Path(cache_folder).mkdir(parents=True, exist_ok=True)
        
        # 初始化模型
        self._initialize_model()
        
    def _detect_best_device(self) -> str:
        """
        自动检测最佳运行设备
        
        Returns:
            str: 设备名称 ('cuda' 或 'cpu')
            
        学习要点：
        - GPU可用性检测
        - 设备选择策略
        - 性能优化考虑
        """
        try:
            if torch.cuda.is_available():
                # 检查CUDA设备数量和内存
                device_count = torch.cuda.device_count()
                if device_count > 0:
                    # 获取第一个GPU的内存信息
                    memory_info = torch.cuda.get_device_properties(0)
                    total_memory = memory_info.total_memory / (1024**3)  # 转换为GB
                    
                    logger.info(f"🎮 检测到 {device_count} 个CUDA设备")
                    logger.info(f"📊 GPU内存: {total_memory:.1f}GB")
                    
                    # BGE-large模型大约需要2-3GB GPU内存
                    if total_memory >= 3.0:
                        return "cuda"
                    else:
                        logger.warning("⚠️ GPU内存不足，将使用CPU")
                        return "cpu"
            
            logger.info("💻 将使用CPU运行")
            return "cpu"
            
        except Exception as e:
            logger.warning(f"⚠️ 设备检测异常，默认使用CPU: {e}")
            return "cpu"
    
    def _initialize_model(self):
        """
        初始化sentence-transformers模型
        
        学习要点：
        - 模型加载的错误处理
        - 模型信息的获取和记录
        - 性能监控的实现
        """
        try:
            logger.info(f"🔄 正在加载嵌入模型: {self.model_name}")
            logger.info(f"📍 缓存目录: {self.cache_folder}")
            logger.info(f"🖥️ 运行设备: {self.device}")
            
            start_time = time.time()
            
            # 初始化SentenceTransformer模型
            self.model = SentenceTransformer(
                self.model_name,
                cache_folder=self.cache_folder,
                device=self.device
            )
            
            load_time = time.time() - start_time
            
            # 获取模型信息
            self._model_info = {
                "model_name": self.model_name,
                "device": str(self.model.device),
                "max_seq_length": getattr(self.model, 'max_seq_length', 512),
                "embedding_dimension": self.model.get_sentence_embedding_dimension(),
                "load_time": round(load_time, 2)
            }
            
            logger.info(f"✅ 模型加载成功!")
            logger.info(f"📏 向量维度: {self._model_info['embedding_dimension']}")
            logger.info(f"📝 最大序列长度: {self._model_info['max_seq_length']}")
            logger.info(f"⏱️ 加载耗时: {load_time:.2f}秒")
            
        except Exception as e:
            logger.error(f"❌ 模型加载失败: {e}")
            raise RuntimeError(f"BGE嵌入模型初始化失败: {e}")
    
    def embed_documents(self, 
                       texts: List[str], 
                       batch_size: int = 32,
                       show_progress: bool = False) -> List[List[float]]:
        """
        批量嵌入文档
        
        Args:
            texts: 文本列表
            batch_size: 批处理大小，默认32
            show_progress: 是否显示进度条
            
        Returns:
            List[List[float]]: 嵌入向量列表
            
        学习要点：
        - 批量处理的性能优化
        - 内存管理和效率平衡
        - 进度监控的实现
        - 错误处理和数据验证
        """
        if not texts:
            logger.warning("⚠️ 输入文本列表为空")
            return []
        
        if self.model is None:
            raise RuntimeError("模型未正确初始化")
        
        try:
            logger.info(f"📝 开始批量嵌入 {len(texts)} 个文档")
            start_time = time.time()
            
            # 预处理文本：去除空文本和过长文本
            valid_texts = []
            original_indices = []
            
            for i, text in enumerate(texts):
                if text and text.strip():
                    # 截断过长文本
                    max_length = self._model_info.get('max_seq_length', 512)
                    if len(text) > max_length * 4:  # 粗略估算token数量
                        text = text[:max_length * 3] + "..."
                        logger.debug(f"文档 {i} 被截断到 {max_length * 3} 字符")
                    
                    valid_texts.append(text)
                    original_indices.append(i)
                else:
                    logger.warning(f"跳过空文档: 索引 {i}")
            
            if not valid_texts:
                logger.error("❌ 没有有效的文本进行嵌入")
                return []
            
            # 执行批量嵌入
            embeddings = self.model.encode(
                valid_texts,
                batch_size=batch_size,
                show_progress_bar=show_progress,
                convert_to_numpy=True,
                normalize_embeddings=False  # ChromaDB会处理归一化
            )
            
            processing_time = time.time() - start_time
            
            # 恢复原始顺序，为空文档填充零向量
            embedding_dim = self._model_info['embedding_dimension']
            result_embeddings = []
            valid_idx = 0
            
            for i in range(len(texts)):
                if i in original_indices:
                    result_embeddings.append(embeddings[valid_idx].tolist())
                    valid_idx += 1
                else:
                    # 为空文档创建零向量
                    result_embeddings.append([0.0] * embedding_dim)
            
            logger.info(f"✅ 文档嵌入完成: {len(valid_texts)}/{len(texts)} 有效文档")
            logger.info(f"⏱️ 处理耗时: {processing_time:.2f}秒")
            logger.info(f"🚀 平均速度: {len(valid_texts)/processing_time:.1f} docs/sec")
            
            return result_embeddings
            
        except Exception as e:
            logger.error(f"❌ 批量嵌入失败: {e}")
            return []
    
    def embed_query(self, text: str) -> List[float]:
        """
        嵌入单个查询文本
        
        Args:
            text: 查询文本
            
        Returns:
            List[float]: 嵌入向量
            
        学习要点：
        - 单文本嵌入的优化
        - 查询和文档嵌入的差异处理
        - 快速响应的重要性
        """
        if not text or not text.strip():
            logger.warning("⚠️ 查询文本为空")
            embedding_dim = self._model_info.get('embedding_dimension', 1024)
            return [0.0] * embedding_dim
        
        if self.model is None:
            raise RuntimeError("模型未正确初始化")
        
        try:
            logger.debug(f"🔍 嵌入查询: {text[:100]}...")
            start_time = time.time()
            
            # 执行单文本嵌入
            embedding = self.model.encode(
                [text.strip()],
                convert_to_numpy=True,
                normalize_embeddings=False
            )
            
            processing_time = time.time() - start_time
            
            logger.debug(f"✅ 查询嵌入完成，耗时: {processing_time:.3f}秒")
            
            return embedding[0].tolist()
            
        except Exception as e:
            logger.error(f"❌ 查询嵌入失败: {e}")
            embedding_dim = self._model_info.get('embedding_dimension', 1024)
            return [0.0] * embedding_dim
    
    def compute_similarity(self, text1: str, text2: str) -> float:
        """
        计算两个文本的语义相似度
        
        Args:
            text1: 第一个文本
            text2: 第二个文本
            
        Returns:
            float: 相似度分数 (0-1之间，1表示完全相似)
            
        学习要点：
        - 余弦相似度的计算方法
        - 向量归一化的重要性
        - 相似度阈值的解释
        """
        if not text1 or not text2:
            return 0.0
        
        try:
            # 获取两个文本的嵌入向量
            embeddings = self.model.encode([text1, text2], convert_to_numpy=True)
            
            # 计算余弦相似度
            similarity_score = np.dot(embeddings[0], embeddings[1]) / (
                np.linalg.norm(embeddings[0]) * np.linalg.norm(embeddings[1])
            )
            
            # 确保结果在0-1范围内
            similarity_score = max(0.0, min(1.0, float(similarity_score)))
            
            return similarity_score
            
        except Exception as e:
            logger.error(f"❌ 相似度计算失败: {e}")
            return 0.0
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            Dict: 模型详细信息
        """
        return self._model_info.copy()
    
    def benchmark_performance(self, 
                            test_texts: Optional[List[str]] = None,
                            num_samples: int = 100) -> Dict[str, Any]:
        """
        性能基准测试
        
        Args:
            test_texts: 测试文本列表，为None时使用默认测试数据
            num_samples: 测试样本数量
            
        Returns:
            Dict: 性能测试结果
            
        学习要点：
        - 性能基准测试的设计
        - 不同场景下的性能表现
        - 瓶颈识别和优化方向
        """
        if test_texts is None:
            # 生成默认测试数据
            test_texts = [
                f"这是第{i}个测试文档，用于评估嵌入模型的性能表现和处理能力。"
                for i in range(num_samples)
            ]
        else:
            test_texts = test_texts[:num_samples]
        
        try:
            logger.info(f"🧪 开始性能基准测试: {len(test_texts)} 个样本")
            
            results = {}
            
            # 测试批量嵌入性能
            start_time = time.time()
            embeddings = self.embed_documents(test_texts, show_progress=False)
            batch_time = time.time() - start_time
            
            results["batch_embedding"] = {
                "total_time": round(batch_time, 3),
                "docs_per_second": round(len(test_texts) / batch_time, 2),
                "ms_per_doc": round(batch_time * 1000 / len(test_texts), 2)
            }
            
            # 测试单个查询性能
            single_times = []
            for i in range(min(10, len(test_texts))):
                start_time = time.time()
                self.embed_query(test_texts[i])
                single_times.append(time.time() - start_time)
            
            avg_single_time = np.mean(single_times)
            results["single_query"] = {
                "avg_time": round(avg_single_time, 4),
                "queries_per_second": round(1 / avg_single_time, 2)
            }
            
            # 设备和模型信息
            results["system_info"] = {
                "device": self.device,
                "model_name": self.model_name,
                "embedding_dimension": self._model_info['embedding_dimension'],
                "max_seq_length": self._model_info['max_seq_length']
            }
            
            logger.info("✅ 性能基准测试完成")
            logger.info(f"📊 批量处理: {results['batch_embedding']['docs_per_second']:.1f} docs/sec")
            logger.info(f"🔍 单查询: {results['single_query']['queries_per_second']:.1f} queries/sec")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 性能测试失败: {e}")
            return {"error": str(e)}