# 测试图片获取指南

## 方案1：立即可用 - Kaggle Cityscapes简化版

### 下载链接：
- **主要推荐**: https://www.kaggle.com/datasets/dansbecker/cityscapes-image-pairs
- **备选**: https://www.kaggle.com/datasets/shuvoalok/cityscapes

### 下载步骤：
1. 注册/登录Kaggle账号
2. 点击"Download"按钮下载ZIP文件
3. 解压后选择5-10张代表性图片
4. 重命名为我们的测试场景：
   - `scenario_001_pedestrian_crossing.jpg` - 包含行人过街的图片
   - `scenario_002_illegal_parking.jpg` - 包含停车场景的图片  
   - `scenario_003_traffic_light.jpg` - 包含交通信号灯的图片
   - `scenario_004_bike_lane.jpg` - 包含自行车道的图片
   - `scenario_005_normal_traffic.jpg` - 正常交通场景

## 方案2：完整官方数据集 - Cityscapes官网

### 获取步骤：
1. 访问：https://www.cityscapes-dataset.com/login/
2. 注册学术账号（需要提供机构邮箱）
3. 同意使用协议
4. 下载所需文件：
   - `leftImg8bit_trainvaltest.zip` (11GB) - 原始图像
   - `gtFine_trainvaltest.zip` (241MB) - 精细标注

### 数据集优势：
- 5,000张高质量标注图像
- 20,000张粗糙标注图像  
- 像素级语义分割标注
- 30个语义类别（车辆、行人、道路等）
- 50个城市的真实街景

## 当前建议：

**立即行动**: 先从Kaggle下载几张图片，放到 `/home/<USER>/lhp/projects/0714agent/my-agent1/data/test_images/` 目录

**长期规划**: 注册Cityscapes官网，后续获取完整数据集用于模型训练和评估

## 文件命名规范：

请将下载的图片按以下方式命名和保存：

```
/home/<USER>/lhp/projects/0714agent/my-agent1/data/test_images/
├── scenario_001_pedestrian_crossing.jpg
├── scenario_002_illegal_parking.jpg  
├── scenario_003_traffic_light.jpg
├── scenario_004_bike_lane.jpg
└── scenario_005_normal_traffic.jpg
```

完成后，我们就可以继续Phase 3的RAG系统构建了！